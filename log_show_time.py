import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI
LOG_FMT = "%(asctime)s - %(levelname)s - %(message)s"
# 解决 uvicorn 日志不展示时间的bug

def config_access_log_to_show_time():
    logger = logging.getLogger("uvicorn.access")
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter(LOG_FMT))
    logger.addHandler(handler)

@asynccontextmanager
async def lifespan(app: FastAPI):
    config_access_log_to_show_time()
    yield