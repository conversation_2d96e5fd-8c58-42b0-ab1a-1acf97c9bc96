# @Time: 2025/8/26
# @Author: AI Assistant
"""
API解析智能体真实流程测试

直接测试已开发完成的API解析智能体代码：
1. 真实调用 ApiAutomationOrchestrator.process_api_document_content
2. 真实执行 ApiDocParserAgent 的解析逻辑
3. 验证整个数据流转和处理过程

使用OSS已上传的PDF文件（ApiDocumentUploadModel id=3）进行测试
不使用单元测试框架和Mock，测试真实代码逻辑
"""
import asyncio
import json
import logging
import uuid
from datetime import datetime
from typing import Any

# 导入Tortoise ORM
from tortoise import Tortoise

from common.settings import TORTOISE_ORM

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


class RealApiDocParserFlowTest:
    """API解析智能体真实流程测试类"""

    def __init__(self):
        self.session_id = str(uuid.uuid4())
        self.orchestrator = None
        self.api_document_content = None  # 存储从 OSS 获取的文档内容
        self.db_initialized = False  # 数据库初始化状态

    async def init_database(self):
        """
        初始化数据库连接
        """
        if self.db_initialized:
            return

        try:
            print("🔌 正在初始化数据库连接...")

            # 初始化 Tortoise ORM
            await Tortoise.init(config=TORTOISE_ORM)

            print("   ✅ 数据库连接初始化成功")
            self.db_initialized = True

        except Exception as e:
            print(f"   ❌ 数据库连接初始化失败: {str(e)}")
            import traceback
            print(f"   错误详情: {traceback.format_exc()}")
            raise

    async def close_database(self):
        """
        关闭数据库连接
        """
        if self.db_initialized:
            try:
                await Tortoise.close_connections()
                print("   ✅ 数据库连接已关闭")
                self.db_initialized = False
            except Exception as e:
                print(f"   ⚠️ 关闭数据库连接失败: {str(e)}")

    async def get_real_document_content(self, document_id: int = 3) -> str:
        """
        从 OSS 获取真实的 PDF 文档内容
        
        Args:
            document_id: ApiDocumentUploadModel 的 ID，默认为 3
            
        Returns:
            str: PDF 文档的文本内容
        """
        try:
            print(f"\n📄 正在从 OSS 获取文档内容 (ID: {document_id})...")

            # 确保数据库已初始化
            await self.init_database()

            # 导入 API 文档服务
            from apps.ai_api_auto.services.api_document_service import ApiDocumentService

            # 创建服务实例
            api_doc_service = ApiDocumentService()

            # 获取文档文本内容
            text_content = await api_doc_service.get_document_text_content(document_id)

            print(f"   ✅ 成功获取文档内容")
            print(f"   - 文档内容长度: {len(text_content)} 字符")
            print(f"   - 内容预览: {text_content[:200]}...")

            return text_content

        except Exception as e:
            print(f"   ❌ 获取文档内容失败: {str(e)}")
            import traceback
            print(f"   错误详情: {traceback.format_exc()}")
            raise

    def print_section(self, title: str, content: str = ""):
        """打印带格式的章节"""
        print(f"\n{'=' * 60}")
        print(f"  {title}")
        print('=' * 60)
        if content:
            print(content)

    def print_step(self, step: int, description: str, data: Any = None):
        """打印步骤信息"""
        print(f"\n📍 步骤 {step}: {description}")
        print("-" * 40)
        if data:
            if isinstance(data, dict):
                try:
                    print(json.dumps(data, indent=2, ensure_ascii=False, default=self._json_serializer))
                except (TypeError, ValueError) as e:
                    print(f"数据格式化失败: {str(e)}")
                    print(f"原始数据类型: {type(data)}")
                    print(f"数据内容: {str(data)[:500]}..." if len(str(data)) > 500 else str(data))
            else:
                print(str(data))

    def _json_serializer(self, obj):
        """自定义JSON序列化器，处理不可序列化的对象"""
        from datetime import datetime

        if isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            return str(obj)
        elif hasattr(obj, 'value'):  # 处理枚举类型
            return obj.value
        else:
            return str(obj)

    async def run_orchestrator_initialization(self):
        """运行真实编排器初始化"""
        self.print_section("测试1: 真实API编排器初始化")

        try:
            # 导入真实的编排器
            from apps.ai_api_auto.services.api_orchestrator_service import ApiAutomationOrchestrator

            # 创建编排器实例
            self.orchestrator = ApiAutomationOrchestrator()

            print("✅ 编排器实例创建成功")
            print(f"   - 会话ID: {self.session_id}")
            print(f"   - 编排器类型: {type(self.orchestrator).__name__}")
            print(f"   - 初始统计指标: {self.orchestrator.orchestrator_metrics}")

            # 真实初始化编排器
            print("\n🔄 正在初始化编排器...")
            await self.orchestrator.initialize()

            print("✅ 编排器初始化完成")
            print(f"   - 运行时状态: {'已启动' if self.orchestrator.runtime else '未启动'}")
            print(f"   - 智能体工厂: {type(self.orchestrator.agent_factory).__name__}")

            return True

        except Exception as e:
            print(f"❌ 编排器初始化失败: {str(e)}")
            import traceback
            print(f"错误详情:\n{traceback.format_exc()}")
            return False

    async def run_document_parsing(self):
        """运行真实文档解析流程"""
        self.print_section("测试2: 真实API文档解析")

        try:
            # 获取真实的 PDF 文档内容
            if not self.api_document_content:
                self.api_document_content = await self.get_real_document_content()

            print("📄 准备解析的API文档内容:")
            print(f"   - 文档来源: OSS PDF文件 (ApiDocumentUploadModel ID: 3)")
            print(f"   - 文档长度: {len(self.api_document_content)} 字符")
            print(f"   - 文档类型: PDF提取的文本内容")

            print("\n🚀 调用真实的 process_api_document_content 方法...")

            # 记录开始时间
            start_time = datetime.now()

            # 真实调用文档处理方法
            result = await self.orchestrator.process_api_document_content(
                session_id=self.session_id,
                api_file_content=self.api_document_content,
                config={
                    "parse_mode": "intelligent",
                    "extract_examples": True,
                    "validate_schema": True
                }
            )

            # 等待智能体处理消息完成，使用智能等待机制
            print("\n⏳ 等待智能体处理消息完成...")
            print("   - 使用智能等待机制，基于智能体实际完成状态")
            print("   - 最大等待时间: 180秒（如需要会根据实际情况调整）")

            # 使用编排器的智能等待机制，等待智能体真正完成处理
            completion_success = await self.orchestrator.wait_for_agent_completion(
                session_id=self.session_id, 
                timeout=180  # 3分钟超时，足够处理大文档
            )

            if completion_success:
                print("   - ✅ 智能体处理完成，大模型已成功完成所有调用")
            else:
                print("   - ⚠️ 智能体等待超时或出现异常，但继续后续流程")

            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()

            print("✅ 真实文档处理完成")
            print(f"   - 处理时间: {processing_time:.2f} 秒")
            print(f"   - 返回结果类型: {type(result).__name__}")

            self.print_step(3, "真实处理结果", result)

            return result

        except Exception as e:
            print(f"❌ 真实文档解析失败: {str(e)}")
            import traceback
            print(f"错误详情:\n{traceback.format_exc()}")
            return None

    async def run_complete_flow(self):
        """运行真实完整流程"""
        self.print_section("真实API解析智能体完整流程测试")

        print(f"🚀 开始真实流程测试")
        print(f"   - 会话ID: {self.session_id}")
        print(f"   - 测试时间: {datetime.now()}")

        try:
            # 步骤0: 获取真实的 PDF 文档内容
            print("\n📄 步骤0: 获取真实 PDF 文档内容...")
            self.api_document_content = await self.get_real_document_content()

            # 步骤1: 初始化编排器
            if not await self.run_orchestrator_initialization():
                return False

            # 步骤2: 执行真实文档解析
            result = await self.run_document_parsing()
            if not result:
                return False

            # 不需要额外等待，因为上面已经智能等待了
            # print("\n⏳ 等待智能体完成所有处理...")
            # await asyncio.sleep(15)  # 不需要固定等待

            # 步骤3: 检查会话状态
            await self.check_session_status()

            print(f"\n🎉 真实流程测试成功!")
            return True

        except Exception as e:
            print(f"\n❌ 测试执行失败: {str(e)}")
            import traceback
            print(f"错误详情:\n{traceback.format_exc()}")
            return False

        finally:
            # 确保最后清理资源，即使出现异常也要清理
            print("\n🧹 正在安全清理资源...")
            await self.cleanup_resources()

    async def check_session_status(self):
        """检查会话状态"""
        self.print_section("测试3: 会话状态检查")

        try:
            print("📊 活跃会话信息:")
            active_sessions = self.orchestrator.active_sessions

            if self.session_id in active_sessions:
                session_info = active_sessions[self.session_id]
                print(f"   - 会话ID: {self.session_id}")
                print(f"   - 开始时间: {session_info.get('start_time')}")
                print(f"   - 结束时间: {session_info.get('end_time', '进行中')}")
                print(f"   - 当前状态: {session_info.get('status')}")
                print(f"   - 当前步骤: {session_info.get('current_step')}")

            print(f"\n📈 编排器统计指标:")
            metrics = self.orchestrator.orchestrator_metrics
            for key, value in metrics.items():
                print(f"   - {key}: {value}")

        except Exception as e:
            print(f"❌ 会话状态检查失败: {str(e)}")

    async def cleanup_resources(self):
        """清理资源"""
        try:
            print("🧹 开始安全清理资源...")

            if self.orchestrator:
                print("⏳ 等待智能体完成所有处理任务...")
                # 增加缓冲时间，确保所有操作完成
                await asyncio.sleep(15)  # 等待15秒缓冲

                # 直接清理编排器，现在编排器内部会正确处理清理顺序
                try:
                    await self.orchestrator.cleanup()
                    print("✅ 编排器资源清理完成")
                except Exception as e:
                    print(f"⚠️ 编排器清理部分失败: {str(e)}")
            else:
                print("ℹ️ 无需清理：编排器未初始化")

            # 关闭数据库连接
            await self.close_database()

            print("✅ 资源清理完成")

        except Exception as e:
            print(f"❌ 资源清理失败: {str(e)}")
            # 不再抛出异常，避免影响整体流程


async def main():
    """主函数"""
    print("🚀 启动真实API解析智能体流程测试")
    print("=" * 60)

    tester = None
    try:
        tester = RealApiDocParserFlowTest()
        success = await tester.run_complete_flow()

        print("\n" + "=" * 60)
        if success:
            print("🎯 真实流程测试成功！")
        else:
            print("⚠️ 真实流程测试部分失败，请检查日志")

    except Exception as e:
        print(f"\n\n❌ 主函数执行异常: {str(e)}")
        import traceback
        print(f"详细错误:\n{traceback.format_exc()}")

        # 如果有测试实例，尝试清理资源
        if tester and hasattr(tester, 'orchestrator') and tester.orchestrator:
            try:
                print("\n⏳ 尝试紧急清理资源...")
                await tester.cleanup_resources()
            except Exception as cleanup_error:
                print(f"⚠️ 紧急清理失败: {str(cleanup_error)}")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断演示")
    except Exception as e:
        print(f"\n\n❌ 演示执行异常: {str(e)}")
        import traceback

        print(f"详细错误:\n{traceback.format_exc()}")
