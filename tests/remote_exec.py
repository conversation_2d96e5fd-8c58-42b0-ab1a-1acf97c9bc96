import json
import os

from langchain_core.messages.human import HumanMessage
from langchain_core.messages.system import SystemMessage
from langchain_openai import ChatOpenAI
from openai import OpenAI

from common.utils import encode_jwt


def deepseek_exec():
    client = OpenAI(api_key="***********************************", base_url="https://api.deepseek.com")

    response = client.chat.completions.create(
        model="deepseek-chat",
        messages=[
            {"role": "system", "content": "You are a helpful assistant"},
            {"role": "user", "content": "Hello"},
        ],
        stream=False
    )

    print(response.choices[0].message.content)


def exec(client, msg, model):
    completion = client.chat.completions.create(
        model=model,  # 模型列表：https://help.aliyun.com/zh/model-studio/getting-started/models
        messages=msg
    )
    response = completion.choices[0].message.content
    msg.append({"role": "assistant", "content": response})
    print(response)


def aliyun_exec(api_key, base_url, model):
    """
    token 存量：https://bailian.console.aliyun.com/?tab=model#/model-market
    :return:
    """
    try:
        msg = [
            {'role': 'system', 'content': 'You are a helpful assistant.'},
            {'role': 'user', 'content': '你是谁？'}
        ]

        # client = OpenAI(
        #     # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key="sk-xxx",
        #     api_key=os.getenv("DASHSCOPE_API_KEY"),
        #     base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        # )

        # client = OpenAI(api_key="sk-MPNhpvlyGVWACR0z892317Cf5377424d9bAfA1662d75E697",
        #                 base_url="https://oneapi-test.seres.cn/v1")
        client = OpenAI(api_key=api_key, base_url=base_url)
        exec(client, msg, model)

        input_content = input("> ")
        while input_content != "exit":
            msg.append({'role': 'user', 'content': input_content})
            exec(client, msg)
            input_content = input("> ")

        print(json.dumps(msg, ensure_ascii=False))

    except Exception as e:
        print(f"错误信息：{e}")
        print("请参考文档：https://help.aliyun.com/zh/model-studio/developer-reference/error-code")


def verify_template(template: str):
    """
    验证字符串是不是**{}**{}**{}分割
    :param template:
    :return:
    """
    stack = list()
    original_var = list()
    var_list = list()

    for word in template:
        if word == "{":
            # 左括号多
            if len(stack) != 0:
                return False, "左括号多"
            else:
                stack.append(word)

        elif word == "}":
            # 右括号多
            if len(stack) != 1:
                return False, "右括号多"
            else:
                # 左右括号包含的变量为空
                if len(original_var) == 0:
                    return False, "左右括号包含的变量为空"
                else:
                    stack.pop()
                    var_list.append(''.join(original_var))
                    original_var.clear()
        else:
            if len(stack) == 1:
                original_var.append(word)
    # 左括号在最后
    if len(stack) != 0:
        return False, "左括号在最后"
    return True, var_list


if __name__ == "__main__":
    # messages = [
    #     SystemMessage(content="你是一个知识渊博的助手，能回答各种问题。"),
    #     HumanMessage(content="介绍一下长城")
    # ]
    msg = [
        {'role': 'system', 'content': 'You are a helpful assistant.'},
        {'role': 'user', 'content': '你是谁？'}
    ]
    api_key = "sk-MPNhpvlyGVWACR0z892317Cf5377424d9bAfA1662d75E697"
    base_url = "https://oneapi-test.seres.cn/v1"
    model = "qwen2.5-instruct"
    client = OpenAI(api_key=api_key, base_url=base_url)
    print(client)
    completion = client.chat.completions.create(model=model, messages=msg, stream=False)
    response = completion.choices[0].message.content
    print(response)
    # llm = ChatOpenAI(
    #     base_url=base_url,  # 请求的API服务地址
    #     api_key=api_key,  # API Key
    #     model=model,  # 本次使用的模型
    #     temperature=0.3,  # 发散的程度，一般为0
    # )
    # print(llm)
    # ai_msg = llm.invoke(messages)
    # print(ai_msg.content)
# aliyun_exec(
#     api_key="sk-MPNhpvlyGVWACR0z892317Cf5377424d9bAfA1662d75E697",
#     base_url="https://oneapi-test.seres.cn/v1",
#     model="qwen2.5-instruct"
# )
# str = ("角色设定： 你是一位经验丰富{角色}。情境设定： 我正在为一个新推出的智能家居产品{内容}，想要优化关键词{目的}搜索引擎排名。"
#        ""
#        ""
#        ""
#        "任务指令： 请你为我制定一份详细的 SEO 优化策略，包括关键词研究、内容结构建议、内外部链接策略等方面的信息。{")
# str = str.replace(" ", "")
# print(verify_template(str))
# print(encode_jwt(payload={'id': 32, 'username': "杨长谕"}))
