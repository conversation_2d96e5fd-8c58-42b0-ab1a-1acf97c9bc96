from typing import Annotated

from fastapi import APIRouter, Request, Depends
from fastapi.security import OA<PERSON>2Pass<PERSON><PERSON>earer, HTTPAuthorizationCredentials, HTTPBearer

from apps.material.models import MaterialModel
from apps.suite.models import SuiteModel
from apps.users.models import UsersModel
from common.utils import response_success, encode_jwt, model_data_exist

test_router = APIRouter(prefix="/test")

# oauth2_scheme = HTTPAuthorizationCredentials()
security = HTTPBearer()


@test_router.get("/", summary="测试接口get", tags=['测试模块'])
async def api_test_get():
    print(encode_jwt(payload={'id': 32, 'username': "杨长谕"}))
    material = await MaterialModel.get(id=10)
    suite = await SuiteModel.filter(material__id=material.id)
    print(suite)
    return response_success(data=suite)


@test_router.post("", summary="测试接口post", tags=['测试模块'])
async def api_test_post(pk: int):
    print(await model_data_exist(pk, UsersModel))
    return response_success()

#
# @test_router.get("/users/me",tags=['测试模块'])
# async def read_items(credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)]):
#     return {"scheme": credentials.scheme, "credentials": credentials.credentials}
#
#
# @test_router.get("/get/token",tags=['测试模块'])
# async def gene_token(id):
#     return response_success(data=encode_jwt(payload={'id': id, 'username': "test_user"}))
