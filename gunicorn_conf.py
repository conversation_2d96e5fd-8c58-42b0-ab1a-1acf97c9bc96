bind = '127.0.0.1:8080'  # 绑定ip和端口号
backlog = 512  # 可服务的客户端数量
worker_class = 'uvicorn.workers.UvicornWorker'  # 使用gevent模式，还可以使用sync 模式，默认的是sync模式
workers = 4  # 进程数
loglevel = 'info'  # 日志级别，这个日志级别指的是错误日志的级别，而访问日志的级别无法设置
access_logformat = '%(t)s %(p)s %(h)s "%(r)s" %(s)s %(L)s %(b)s %(f)s" "%(a)s"'
accesslog = "-"  # 访问日志文件 标准输出 会被supervisor截获
errorlog = "-"  # 错误日志文件 标准输出 会被supervisor截获
timeout = 180