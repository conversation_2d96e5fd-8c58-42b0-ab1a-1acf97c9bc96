#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证中间件模块

该模块提供了JWT认证中间件，用于验证API请求的身份认证。
支持白名单机制，允许特定路径无需认证即可访问。

@Time: 2025/3/19 16:32
@Author: jinglv
@LastModified: 2025/7/7
@Version: 2.0
"""

import logging
from typing import Set

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint

from apps.users.models import UsersModel
from common.utils import verify_jwt

logger = logging.getLogger(__name__)


class VerifyAuthMiddleware(BaseHTTPMiddleware):
    """
    JWT认证中间件

    功能说明：
    - 验证API请求的JWT token
    - 支持白名单机制，特定路径无需认证
    - 支持管理员权限验证
    - 自动解析用户身份信息

    白名单路径：
    - 用户登录接口
    - Swagger文档相关路径
    - 静态资源路径
    - 健康检查接口
    """

    def __init__(self, app):
        super().__init__(app)

        # ========== 定义白名单路径 ==========
        self.whitelist_paths: Set[str] = {
            # 用户认证相关
            '/user/login',
            '/user/admin/login',
            '/user/generate_token',    # Token生成接口，无需认证

            # Swagger文档相关
            '/docs',
            '/redoc',
            '/openapi.json',

            # 静态资源
            '/static',
            '/favicon.ico',

            # 健康检查
            '/health',
            '/ping',

            # 测试接口（开发环境）
            '/test'
        }

        # 白名单路径前缀（支持前缀匹配）
        self.whitelist_prefixes: Set[str] = {
            '/docs',      # Swagger文档页面
            '/redoc',     # ReDoc文档页面
            '/static',    # 静态资源
            '/test'       # 测试接口
        }

    def _is_path_whitelisted(self, path: str) -> bool:
        """
        检查路径是否在白名单中

        Args:
            path: 请求路径

        Returns:
            bool: True表示在白名单中，False表示需要认证
        """
        # 精确匹配
        if path in self.whitelist_paths:
            return True

        # 前缀匹配
        for prefix in self.whitelist_prefixes:
            if path.startswith(prefix):
                return True

        return False

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """
        中间件主要处理逻辑

        Args:
            request: HTTP请求对象
            call_next: 下一个中间件或路由处理器

        Returns:
            Response: HTTP响应对象
        """
        path = request.url.path

        logger.debug(f"处理请求路径: {path}")

        # ========== 1. 检查是否在白名单中 ==========
        if self._is_path_whitelisted(path):
            logger.debug(f"路径 {path} 在白名单中，跳过认证")
            response = await call_next(request)
            return response

        # ========== 2. 获取并验证Authorization头 ==========
        auth_info = request.headers.get('authorization', None)

        if auth_info is None:
            logger.warning(f"路径 {path} 缺少Authorization头")
            return Response(
                status_code=401,
                content='{"error": "未提供认证信息", "code": "NO_AUTH_HEADER"}',
                media_type="application/json"
            )

        # ========== 3. 验证JWT token ==========
        status, info = verify_jwt(auth_info)
        if not status:
            logger.warning(f"路径 {path} JWT验证失败: {info}")
            return Response(
                status_code=401,
                content=f'{{"error": "认证失败", "detail": "{info}", "code": "INVALID_TOKEN"}}',
                media_type="application/json"
            )

        user_id = info.get('id')
        if not user_id:
            logger.error(f"JWT token中缺少用户ID信息")
            return Response(
                status_code=401,
                content='{"error": "token中缺少用户信息", "code": "MISSING_USER_ID"}',
                media_type="application/json"
            )

        # ========== 4. 管理员权限验证 ==========
        if path.startswith("/manage"):
            try:
                is_admin = await UsersModel.filter(id=user_id, role=1).exists()
                if not is_admin:
                    logger.warning(f"用户 {user_id} 尝试访问管理员路径 {path} 但权限不足")
                    return Response(
                        status_code=403,
                        content='{"error": "需要管理员权限", "code": "INSUFFICIENT_PRIVILEGES"}',
                        media_type="application/json"
                    )
            except Exception as e:
                logger.error(f"验证管理员权限时出错: {str(e)}")
                return Response(
                    status_code=500,
                    content='{"error": "权限验证失败", "code": "AUTH_CHECK_ERROR"}',
                    media_type="application/json"
                )

        # ========== 5. 设置用户信息到请求状态 ==========
        request.state.user_id = user_id

        logger.debug(f"用户 {user_id} 成功通过认证，访问路径: {path}")

        # ========== 6. 继续处理请求 ==========
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            logger.error(f"处理请求时出错: {str(e)}")
            return Response(
                status_code=500,
                content='{"error": "服务器内部错误", "code": "INTERNAL_ERROR"}',
                media_type="application/json"
            )
