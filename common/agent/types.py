# @Time: 2025/8/25 14:39
# @Author: lvjing
"""
核心类型定义
定义系统中使用的枚举类型和常量
"""
from enum import Enum
from typing import Dict


class AgentTypes(Enum):
    """
    智能体类型枚举
    """
    API_DOC_PARSER = "api_doc_parser"
    API_DATA_PERSISTENCE = "api_data_persistence"


class TopicTypes(Enum):
    """
    主题类型枚举
    """
    # 接口自动化相关主题
    API_DOC_PARSER = "api_doc_parser"
    API_DATA_PERSISTENCE = "api_data_persistence"
    # 系统主题
    STREAM_OUTPUT = "stream_output"


class AgentPlatform(Enum):
    """
    智能体平台枚举
    """
    API_AUTOMATION = "api_automation"
    AUTO_GEN = "autogen"


class MessageRegion(Enum):
    """
    消息区域枚举
    """
    PROCESS = "process"
    INFO = "info"
    ERROR = "error"
    SUCCESS = "success"
    WARNING = "warning"


# 智能体名映射
AGENT_NAMES: Dict[str, str] = {
    AgentTypes.API_DOC_PARSER.value: "API文档解析智能体",
    AgentTypes.API_DATA_PERSISTENCE: "API数据持久化智能体",
}
