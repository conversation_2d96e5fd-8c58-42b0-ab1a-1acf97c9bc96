"""
Office 文件转 PDF 服务
支持 Word (.docx, .doc)、Excel (.xlsx, .xls)、PowerPoint (.pptx, .ppt) 转换为 PDF
确保转换后的 PDF 内容完整，格式保持一致
"""

import logging
import shutil
import tempfile
from enum import Enum
from pathlib import Path
from typing import Union, Optional, Dict, Any

try:
    # 导入 python-docx 用于处理 Word 文档
    from docx import Document
    from docx2pdf import convert as docx_to_pdf
except ImportError:
    docx_to_pdf = None

try:
    # 导入 openpyxl 用于处理 Excel 文档
    import openpyxl
    from openpyxl.utils import get_column_letter
    from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
except ImportError:
    openpyxl = None

try:
    # 导入 python-pptx 用于处理 PowerPoint 文档
    from pptx import Presentation
except ImportError:
    Presentation = None

try:
    # 导入 reportlab 用于生成 PDF
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.pdfgen import canvas
    from reportlab.lib.utils import ImageReader
except ImportError:
    SimpleDocTemplate = None

try:
    # 导入 LibreOffice 转换支持
    import subprocess
    import platform
except ImportError:
    subprocess = None


class OfficeFileType(Enum):
    """Office 文件类型枚举"""
    WORD = "word"
    EXCEL = "excel"
    POWERPOINT = "powerpoint"
    UNKNOWN = "unknown"


class ConversionMethod(Enum):
    """转换方法枚举"""
    LIBREOFFICE = "libreoffice"  # 使用 LibreOffice 命令行
    PYTHON_LIBS = "python_libs"  # 使用 Python 库
    AUTO = "auto"  # 自动选择最佳方法


class OfficeToPdfService:
    """Office 文件转 PDF 服务类"""

    def __init__(self, temp_dir: Optional[str] = None, conversion_method: ConversionMethod = ConversionMethod.AUTO):
        """
        初始化服务
        
        Args:
            temp_dir: 临时文件目录，默认使用系统临时目录
            conversion_method: 转换方法
        """
        self.logger = logging.getLogger(__name__)
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.conversion_method = conversion_method

        # 确保临时目录存在
        Path(self.temp_dir).mkdir(parents=True, exist_ok=True)

        # 检查依赖
        self._check_dependencies()

    def _check_dependencies(self) -> Dict[str, bool]:
        """
        检查依赖库和工具的可用性
        
        Returns:
            Dict[str, bool]: 各种依赖的可用性状态
        """
        dependencies = {
            "docx2pdf": docx_to_pdf is not None,
            "openpyxl": openpyxl is not None,
            "python-pptx": Presentation is not None,
            "reportlab": SimpleDocTemplate is not None,
            "libreoffice": self._check_libreoffice()
        }

        self.logger.info(f"依赖检查结果: {dependencies}")
        return dependencies

    def _check_libreoffice(self) -> bool:
        """检查 LibreOffice 是否可用"""
        try:
            if platform.system() == "Darwin":  # macOS
                result = subprocess.run(
                    ["/Applications/LibreOffice.app/Contents/MacOS/soffice", "--version"],
                    capture_output=True, text=True, timeout=10
                )
            elif platform.system() == "Linux":
                result = subprocess.run(
                    ["libreoffice", "--version"],
                    capture_output=True, text=True, timeout=10
                )
            elif platform.system() == "Windows":
                result = subprocess.run(
                    ["soffice", "--version"],
                    capture_output=True, text=True, timeout=10
                )
            else:
                return False

            return result.returncode == 0
        except Exception:
            return False

    def detect_file_type(self, file_path: Union[str, Path]) -> OfficeFileType:
        """
        检测文件类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            OfficeFileType: 文件类型
        """
        file_path = Path(file_path)
        suffix = file_path.suffix.lower()

        if suffix in ['.docx', '.doc']:
            return OfficeFileType.WORD
        elif suffix in ['.xlsx', '.xls']:
            return OfficeFileType.EXCEL
        elif suffix in ['.pptx', '.ppt']:
            return OfficeFileType.POWERPOINT
        else:
            return OfficeFileType.UNKNOWN

    def convert_to_pdf(
            self,
            input_file: Union[str, Path],
            output_file: Optional[Union[str, Path]] = None,
            method: Optional[ConversionMethod] = None
    ) -> str:
        """
        将 Office 文件转换为 PDF
        
        Args:
            input_file: 输入文件路径
            output_file: 输出 PDF 文件路径，如果为 None 则自动生成
            method: 转换方法，如果为 None 则使用实例默认方法
            
        Returns:
            str: 输出 PDF 文件路径
            
        Raises:
            FileNotFoundError: 输入文件不存在
            ValueError: 不支持的文件类型
            RuntimeError: 转换失败
        """
        input_file = Path(input_file)
        if not input_file.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_file}")

        # 检测文件类型
        file_type = self.detect_file_type(input_file)
        if file_type == OfficeFileType.UNKNOWN:
            raise ValueError(f"不支持的文件类型: {input_file.suffix}")

        # 生成输出文件路径
        if output_file is None:
            output_file = input_file.with_suffix('.pdf')
        else:
            output_file = Path(output_file)

        # 确保输出目录存在
        output_file.parent.mkdir(parents=True, exist_ok=True)

        # 选择转换方法
        conversion_method = method or self.conversion_method
        if conversion_method == ConversionMethod.AUTO:
            conversion_method = self._choose_best_method(file_type)

        self.logger.info(f"开始转换: {input_file} -> {output_file}, 方法: {conversion_method.value}")

        try:
            if conversion_method == ConversionMethod.LIBREOFFICE:
                self._convert_with_libreoffice(input_file, output_file)
            else:
                self._convert_with_python_libs(input_file, output_file, file_type)

            if not output_file.exists():
                raise RuntimeError("转换完成但输出文件不存在")

            self.logger.info(f"转换成功: {output_file}")
            return str(output_file)

        except Exception as e:
            self.logger.error(f"转换失败: {str(e)}")
            raise RuntimeError(f"转换失败: {str(e)}") from e

    def _choose_best_method(self, file_type: OfficeFileType) -> ConversionMethod:
        """
        根据文件类型选择最佳转换方法
        
        Args:
            file_type: 文件类型
            
        Returns:
            ConversionMethod: 推荐的转换方法
        """
        dependencies = self._check_dependencies()

        # LibreOffice 通常提供最好的兼容性和质量
        if dependencies["libreoffice"]:
            return ConversionMethod.LIBREOFFICE

        # 根据文件类型选择合适的 Python 库
        if file_type == OfficeFileType.WORD and dependencies["docx2pdf"]:
            return ConversionMethod.PYTHON_LIBS
        elif file_type == OfficeFileType.EXCEL and dependencies["openpyxl"] and dependencies["reportlab"]:
            return ConversionMethod.PYTHON_LIBS
        elif file_type == OfficeFileType.POWERPOINT and dependencies["python-pptx"] and dependencies["reportlab"]:
            return ConversionMethod.PYTHON_LIBS

        raise RuntimeError("没有可用的转换方法，请安装 LibreOffice 或相关 Python 库")

    def _convert_with_libreoffice(self, input_file: Path, output_file: Path):
        """
        使用 LibreOffice 进行转换
        
        Args:
            input_file: 输入文件
            output_file: 输出文件
        """
        try:
            # 创建临时输出目录
            temp_output_dir = Path(self.temp_dir) / "libreoffice_output"
            temp_output_dir.mkdir(exist_ok=True)

            # 构建 LibreOffice 命令
            if platform.system() == "Darwin":  # macOS
                soffice_path = "/Applications/LibreOffice.app/Contents/MacOS/soffice"
            elif platform.system() == "Linux":
                soffice_path = "libreoffice"
            elif platform.system() == "Windows":
                soffice_path = "soffice"
            else:
                raise RuntimeError("不支持的操作系统")

            cmd = [
                soffice_path,
                "--headless",
                "--convert-to", "pdf",
                "--outdir", str(temp_output_dir),
                str(input_file)
            ]

            # 执行转换
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )

            if result.returncode != 0:
                raise RuntimeError(f"LibreOffice 转换失败: {result.stderr}")

            # 移动生成的 PDF 到目标位置
            temp_pdf = temp_output_dir / f"{input_file.stem}.pdf"
            if temp_pdf.exists():
                shutil.move(str(temp_pdf), str(output_file))
            else:
                raise RuntimeError("LibreOffice 转换完成但找不到输出文件")

            # 清理临时文件
            shutil.rmtree(temp_output_dir, ignore_errors=True)

        except subprocess.TimeoutExpired:
            raise RuntimeError("LibreOffice 转换超时")
        except Exception as e:
            raise RuntimeError(f"LibreOffice 转换失败: {str(e)}") from e

    def _convert_with_python_libs(self, input_file: Path, output_file: Path, file_type: OfficeFileType):
        """
        使用 Python 库进行转换

        Args:
            input_file: 输入文件
            output_file: 输出文件
            file_type: 文件类型
        """
        if file_type == OfficeFileType.WORD:
            self._convert_word_to_pdf(input_file, output_file)
        elif file_type == OfficeFileType.EXCEL:
            self._convert_excel_to_pdf(input_file, output_file)
        elif file_type == OfficeFileType.POWERPOINT:
            self._convert_powerpoint_to_pdf(input_file, output_file)
        else:
            raise ValueError(f"不支持的文件类型: {file_type}")

    def _convert_word_to_pdf(self, input_file: Path, output_file: Path):
        """
        将 Word 文档转换为 PDF

        Args:
            input_file: Word 文件路径
            output_file: PDF 输出路径
        """
        if docx_to_pdf is None:
            raise RuntimeError("docx2pdf 库未安装，无法转换 Word 文档")

        try:
            # 使用 docx2pdf 进行转换
            docx_to_pdf(str(input_file), str(output_file))

        except Exception as e:
            # 如果 docx2pdf 失败，尝试使用 python-docx + reportlab
            self._convert_word_with_reportlab(input_file, output_file)

    def _convert_word_with_reportlab(self, input_file: Path, output_file: Path):
        """
        使用 python-docx + reportlab 转换 Word 文档

        Args:
            input_file: Word 文件路径
            output_file: PDF 输出路径
        """
        if SimpleDocTemplate is None:
            raise RuntimeError("reportlab 库未安装")

        try:
            # 读取 Word 文档
            doc = Document(str(input_file))

            # 创建 PDF 文档
            pdf_doc = SimpleDocTemplate(str(output_file), pagesize=A4)
            styles = getSampleStyleSheet()
            story = []

            # 转换段落
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    # 创建段落样式
                    style = styles['Normal']
                    if paragraph.style.name.startswith('Heading'):
                        style = styles['Heading1']

                    # 添加段落到 PDF
                    p = Paragraph(paragraph.text, style)
                    story.append(p)
                    story.append(Spacer(1, 12))

            # 处理表格
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        row_data.append(cell.text)
                    table_data.append(row_data)

                if table_data:
                    pdf_table = Table(table_data)
                    pdf_table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 14),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    story.append(pdf_table)
                    story.append(Spacer(1, 12))

            # 生成 PDF
            pdf_doc.build(story)

        except Exception as e:
            raise RuntimeError(f"Word 转 PDF 失败: {str(e)}") from e

    def _convert_excel_to_pdf(self, input_file: Path, output_file: Path):
        """
        将 Excel 文档转换为 PDF

        Args:
            input_file: Excel 文件路径
            output_file: PDF 输出路径
        """
        if openpyxl is None or SimpleDocTemplate is None:
            raise RuntimeError("openpyxl 或 reportlab 库未安装，无法转换 Excel 文档")

        try:
            # 读取 Excel 文件
            workbook = openpyxl.load_workbook(str(input_file))

            # 创建 PDF 文档
            pdf_doc = SimpleDocTemplate(str(output_file), pagesize=A4)
            styles = getSampleStyleSheet()
            story = []

            # 处理每个工作表
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]

                # 添加工作表标题
                title = Paragraph(f"工作表: {sheet_name}", styles['Heading1'])
                story.append(title)
                story.append(Spacer(1, 12))

                # 获取有数据的区域
                if worksheet.max_row > 0 and worksheet.max_column > 0:
                    # 构建表格数据
                    table_data = []
                    for row in worksheet.iter_rows(min_row=1, max_row=worksheet.max_row,
                                                   min_col=1, max_col=worksheet.max_column):
                        row_data = []
                        for cell in row:
                            value = cell.value
                            if value is None:
                                value = ""
                            row_data.append(str(value))
                        table_data.append(row_data)

                    if table_data:
                        # 创建 PDF 表格
                        pdf_table = Table(table_data)
                        pdf_table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                            ('FONTSIZE', (0, 0), (-1, -1), 8),
                            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))
                        story.append(pdf_table)

                # 添加分页符（除了最后一个工作表）
                if sheet_name != workbook.sheetnames[-1]:
                    story.append(PageBreak())

            # 生成 PDF
            pdf_doc.build(story)

        except Exception as e:
            raise RuntimeError(f"Excel 转 PDF 失败: {str(e)}") from e

    def _convert_powerpoint_to_pdf(self, input_file: Path, output_file: Path):
        """
        将 PowerPoint 文档转换为 PDF

        Args:
            input_file: PowerPoint 文件路径
            output_file: PDF 输出路径
        """
        if Presentation is None or SimpleDocTemplate is None:
            raise RuntimeError("python-pptx 或 reportlab 库未安装，无法转换 PowerPoint 文档")

        try:
            # 读取 PowerPoint 文件
            prs = Presentation(str(input_file))

            # 创建 PDF 文档
            pdf_doc = SimpleDocTemplate(str(output_file), pagesize=A4)
            styles = getSampleStyleSheet()
            story = []

            # 处理每张幻灯片
            for i, slide in enumerate(prs.slides):
                # 添加幻灯片标题
                title = Paragraph(f"幻灯片 {i + 1}", styles['Heading1'])
                story.append(title)
                story.append(Spacer(1, 12))

                # 提取文本内容
                slide_text = []
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text.append(shape.text.strip())

                # 添加文本到 PDF
                for text in slide_text:
                    p = Paragraph(text, styles['Normal'])
                    story.append(p)
                    story.append(Spacer(1, 6))

                # 处理表格
                for shape in slide.shapes:
                    if shape.has_table:
                        table = shape.table
                        table_data = []
                        for row in table.rows:
                            row_data = []
                            for cell in row.cells:
                                row_data.append(cell.text)
                            table_data.append(row_data)

                        if table_data:
                            pdf_table = Table(table_data)
                            pdf_table.setStyle(TableStyle([
                                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                                ('FONTSIZE', (0, 0), (-1, -1), 10),
                                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                                ('GRID', (0, 0), (-1, -1), 1, colors.black)
                            ]))
                            story.append(pdf_table)
                            story.append(Spacer(1, 12))

                # 添加分页符（除了最后一张幻灯片）
                if i < len(prs.slides) - 1:
                    story.append(PageBreak())

            # 生成 PDF
            pdf_doc.build(story)

        except Exception as e:
            raise RuntimeError(f"PowerPoint 转 PDF 失败: {str(e)}") from e

    def batch_convert(
            self,
            input_dir: Union[str, Path],
            output_dir: Union[str, Path],
            file_pattern: str = "*",
            method: Optional[ConversionMethod] = None
    ) -> Dict[str, Any]:
        """
        批量转换目录中的 Office 文件

        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            file_pattern: 文件匹配模式，默认为所有文件
            method: 转换方法

        Returns:
            Dict[str, Any]: 转换结果统计
        """
        input_dir = Path(input_dir)
        output_dir = Path(output_dir)

        if not input_dir.exists():
            raise FileNotFoundError(f"输入目录不存在: {input_dir}")

        # 确保输出目录存在
        output_dir.mkdir(parents=True, exist_ok=True)

        # 支持的文件扩展名
        supported_extensions = {'.docx', '.doc', '.xlsx', '.xls', '.pptx', '.ppt'}

        # 查找所有支持的文件
        files_to_convert = []
        for pattern in file_pattern.split(','):
            pattern = pattern.strip()
            for file_path in input_dir.glob(pattern):
                if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                    files_to_convert.append(file_path)

        # 转换统计
        result = {
            "total_files": len(files_to_convert),
            "successful": 0,
            "failed": 0,
            "success_files": [],
            "failed_files": [],
            "errors": []
        }

        # 逐个转换文件
        for input_file in files_to_convert:
            try:
                output_file = output_dir / f"{input_file.stem}.pdf"
                self.convert_to_pdf(input_file, output_file, method)

                result["successful"] += 1
                result["success_files"].append({
                    "input": str(input_file),
                    "output": str(output_file)
                })

                self.logger.info(f"批量转换成功: {input_file.name}")

            except Exception as e:
                result["failed"] += 1
                error_info = {
                    "file": str(input_file),
                    "error": str(e)
                }
                result["failed_files"].append(error_info)
                result["errors"].append(error_info)

                self.logger.error(f"批量转换失败: {input_file.name}, 错误: {str(e)}")

        self.logger.info(f"批量转换完成: 成功 {result['successful']}, 失败 {result['failed']}")
        return result

    def get_file_info(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        获取 Office 文件信息

        Args:
            file_path: 文件路径

        Returns:
            Dict[str, Any]: 文件信息
        """
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")

        file_type = self.detect_file_type(file_path)
        file_info = {
            "file_path": str(file_path),
            "file_name": file_path.name,
            "file_size": file_path.stat().st_size,
            "file_type": file_type.value,
            "supported": file_type != OfficeFileType.UNKNOWN
        }

        try:
            if file_type == OfficeFileType.WORD:
                doc = Document(str(file_path))
                file_info.update({
                    "paragraphs": len(doc.paragraphs),
                    "tables": len(doc.tables)
                })
            elif file_type == OfficeFileType.EXCEL:
                workbook = openpyxl.load_workbook(str(file_path))
                file_info.update({
                    "worksheets": len(workbook.sheetnames),
                    "sheet_names": workbook.sheetnames
                })
            elif file_type == OfficeFileType.POWERPOINT:
                prs = Presentation(str(file_path))
                file_info.update({
                    "slides": len(prs.slides)
                })
        except Exception as e:
            file_info["analysis_error"] = str(e)

        return file_info
