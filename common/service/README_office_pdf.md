# Office 转 PDF 服务

一个完整的 Office 文件转 PDF 服务，支持 Word、Excel、PowerPoint 文件转换为 PDF，确保内容完整性和格式保持。

## 🚀 功能特性

- ✅ **多格式支持**: Word (.docx, .doc)、Excel (.xlsx, .xls)、PowerPoint (.pptx, .ppt)
- ✅ **多种转换方法**: LibreOffice 命令行、Python 库转换
- ✅ **自动方法选择**: 根据环境自动选择最佳转换方法
- ✅ **批量转换**: 支持目录批量转换
- ✅ **内容完整性**: 保持原文档的格式、表格、图片等
- ✅ **错误处理**: 完善的异常处理和日志记录
- ✅ **文件分析**: 获取 Office 文件的详细信息

## 📦 安装依赖

### 自动安装

运行自动安装脚本：

```bash
python common/service/install_office_pdf_dependencies.py
```

### 手动安装

#### Python 依赖

```bash
pip install python-docx docx2pdf openpyxl python-pptx reportlab Pillow
```

#### LibreOffice（推荐）

**macOS:**
```bash
# 使用 Homebrew
brew install --cask libreoffice

# 或手动下载安装
# https://www.libreoffice.org/download/download/
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt-get update
sudo apt-get install libreoffice
```

**Linux (CentOS/RHEL):**
```bash
sudo yum install libreoffice
```

**Windows:**
```bash
# 使用 Chocolatey
choco install libreoffice

# 或手动下载安装
# https://www.libreoffice.org/download/download/
```

## 🔧 使用方法

### 基本使用

```python
from common.service.office_to_pdf_service import OfficeToPdfService

# 创建服务实例
service = OfficeToPdfService()

# 转换单个文件
pdf_file = service.convert_to_pdf("document.docx")
print(f"转换完成: {pdf_file}")

# 指定输出路径
service.convert_to_pdf("document.docx", "output.pdf")
```

### 批量转换

```python
# 批量转换目录中的所有 Office 文件
result = service.batch_convert(
    input_dir="/path/to/input",
    output_dir="/path/to/output",
    file_pattern="*.docx,*.xlsx,*.pptx"
)

print(f"转换结果: 成功 {result['successful']}, 失败 {result['failed']}")
```

### 指定转换方法

```python
from common.service.office_to_pdf_service import ConversionMethod

# 使用 LibreOffice 转换
service = OfficeToPdfService(conversion_method=ConversionMethod.LIBREOFFICE)

# 使用 Python 库转换
service = OfficeToPdfService(conversion_method=ConversionMethod.PYTHON_LIBS)

# 自动选择最佳方法（默认）
service = OfficeToPdfService(conversion_method=ConversionMethod.AUTO)
```

### 文件信息分析

```python
# 获取文件详细信息
file_info = service.get_file_info("document.docx")
print(f"文件类型: {file_info['file_type']}")
print(f"文件大小: {file_info['file_size']} 字节")
print(f"段落数: {file_info.get('paragraphs', 'N/A')}")
```

## 📋 API 参考

### OfficeToPdfService

#### 构造函数

```python
OfficeToPdfService(
    temp_dir: Optional[str] = None,
    conversion_method: ConversionMethod = ConversionMethod.AUTO
)
```

- `temp_dir`: 临时文件目录
- `conversion_method`: 转换方法

#### 主要方法

##### convert_to_pdf()

```python
convert_to_pdf(
    input_file: Union[str, Path],
    output_file: Optional[Union[str, Path]] = None,
    method: Optional[ConversionMethod] = None
) -> str
```

转换单个 Office 文件为 PDF。

##### batch_convert()

```python
batch_convert(
    input_dir: Union[str, Path],
    output_dir: Union[str, Path],
    file_pattern: str = "*",
    method: Optional[ConversionMethod] = None
) -> Dict[str, Any]
```

批量转换目录中的 Office 文件。

##### get_file_info()

```python
get_file_info(file_path: Union[str, Path]) -> Dict[str, Any]
```

获取 Office 文件的详细信息。

##### detect_file_type()

```python
detect_file_type(file_path: Union[str, Path]) -> OfficeFileType
```

检测文件类型。

### 枚举类型

#### OfficeFileType

- `WORD`: Word 文档
- `EXCEL`: Excel 文档  
- `POWERPOINT`: PowerPoint 文档
- `UNKNOWN`: 未知类型

#### ConversionMethod

- `LIBREOFFICE`: 使用 LibreOffice 命令行
- `PYTHON_LIBS`: 使用 Python 库
- `AUTO`: 自动选择最佳方法

## 🔍 转换方法对比

| 方法 | 优点 | 缺点 | 推荐场景 |
|------|------|------|----------|
| LibreOffice | 兼容性最好，格式保持完整 | 需要安装额外软件 | 生产环境，高质量转换 |
| Python 库 | 纯 Python，部署简单 | 复杂格式支持有限 | 简单文档，快速转换 |

## 📝 支持的文件格式

### 输入格式

- **Word**: .docx, .doc
- **Excel**: .xlsx, .xls
- **PowerPoint**: .pptx, .ppt

### 输出格式

- **PDF**: .pdf

## ⚠️ 注意事项

1. **LibreOffice 路径**: 确保 LibreOffice 安装在标准路径
2. **文件权限**: 确保有读取输入文件和写入输出目录的权限
3. **内存使用**: 大文件转换可能消耗较多内存
4. **字体支持**: 某些特殊字体可能需要系统支持
5. **图片处理**: 复杂图片可能影响转换质量

## 🐛 故障排除

### 常见问题

**1. LibreOffice 未找到**
```
解决方案: 确保 LibreOffice 已正确安装并在 PATH 中
```

**2. Python 库导入失败**
```
解决方案: pip install 相关依赖包
```

**3. 转换质量不佳**
```
解决方案: 尝试使用 LibreOffice 方法，确保字体已安装
```

**4. 大文件转换失败**
```
解决方案: 增加超时时间，检查内存使用
```

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

service = OfficeToPdfService()
# 查看详细日志
```

## 📊 性能优化

1. **批量转换**: 使用 `batch_convert()` 而不是循环调用
2. **临时目录**: 使用 SSD 作为临时目录
3. **内存管理**: 处理大文件时适当释放内存
4. **并发处理**: 可以考虑多进程处理多个文件

## 🧪 测试

运行测试脚本：

```bash
python common/service/office_to_pdf_example.py
```

或运行依赖检查：

```bash
python common/service/test_office_pdf_service.py
```

## 📄 许可证

本服务基于项目许可证，请查看项目根目录的 LICENSE 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个服务。

## 📞 支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查依赖是否正确安装
3. 查看日志输出
4. 提交 Issue 描述问题
