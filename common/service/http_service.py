"""
HTTP服务类，基于httpx实现
提供统一的HTTP请求接口，支持GET、POST、PUT、DELETE等方法
"""

import json
import logging
from typing import Any, Dict, Optional, Union
from urllib.parse import urljoin

import httpx


# 异常类定义
class HttpServiceError(Exception):
    """HTTP服务基础异常"""
    pass


class HttpServiceTimeoutError(HttpServiceError):
    """HTTP请求超时异常"""
    pass


class HttpServiceConnectionError(HttpServiceError):
    """HTTP连接异常"""
    pass


class HttpService:
    """HTTP请求服务类"""

    def __init__(
            self,
            base_url: str = "",
            timeout: float = 30.0,
            headers: Optional[Dict[str, str]] = None,
            verify: bool = True,
            follow_redirects: bool = True
    ):
        """
        初始化HTTP服务
        
        Args:
            base_url: 基础URL
            timeout: 请求超时时间（秒）
            headers: 默认请求头
            verify: 是否验证SSL证书
            follow_redirects: 是否跟随重定向
        """
        self.base_url = base_url
        self.timeout = timeout
        self.default_headers = headers or {}
        self.verify = verify
        self.follow_redirects = follow_redirects
        self.logger = logging.getLogger(__name__)

        # 默认请求头
        self.default_headers.setdefault("Content-Type", "application/json")
        self.default_headers.setdefault("User-Agent", "HttpService/1.0")

    def _build_url(self, endpoint: str) -> str:
        """构建完整URL"""
        if endpoint.startswith(("http://", "https://")):
            return endpoint
        return urljoin(self.base_url, endpoint)

    def _merge_headers(self, headers: Optional[Dict[str, str]]) -> Dict[str, str]:
        """合并请求头"""
        merged_headers = self.default_headers.copy()
        if headers:
            merged_headers.update(headers)
        return merged_headers

    def _prepare_data(self, data: Any) -> Union[str, bytes, None]:
        """准备请求数据"""
        if data is None:
            return None
        if isinstance(data, (str, bytes)):
            return data
        return json.dumps(data, ensure_ascii=False)

    def request(
            self,
            method: str,
            endpoint: str,
            params: Optional[Dict[str, Any]] = None,
            data: Any = None,
            json_data: Any = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> httpx.Response:
        """
        发送HTTP请求（同步）
        
        Args:
            method: HTTP方法
            endpoint: 请求端点
            params: URL参数
            data: 请求体数据
            json_data: JSON数据（会自动设置Content-Type）
            headers: 请求头
            timeout: 超时时间
            **kwargs: 其他httpx参数
            
        Returns:
            httpx.Response: 响应对象
        """
        url = self._build_url(endpoint)
        merged_headers = self._merge_headers(headers)
        request_timeout = timeout or self.timeout

        # 处理JSON数据
        if json_data is not None:
            data = json.dumps(json_data, ensure_ascii=False)
            merged_headers["Content-Type"] = "application/json"
        elif data is not None:
            data = self._prepare_data(data)

        self.logger.info(f"发送{method}请求: {url}")

        with httpx.Client(
                verify=self.verify,
                follow_redirects=self.follow_redirects,
                timeout=request_timeout
        ) as client:
            response = client.request(
                method=method,
                url=url,
                params=params,
                content=data,
                headers=merged_headers,
                **kwargs
            )

        self.logger.info(f"响应状态码: {response.status_code}")
        return response

    async def async_request(
            self,
            method: str,
            endpoint: str,
            params: Optional[Dict[str, Any]] = None,
            data: Any = None,
            json_data: Any = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> httpx.Response:
        """
        发送HTTP请求（异步）
        
        Args:
            method: HTTP方法
            endpoint: 请求端点
            params: URL参数
            data: 请求体数据
            json_data: JSON数据（会自动设置Content-Type）
            headers: 请求头
            timeout: 超时时间
            **kwargs: 其他httpx参数
            
        Returns:
            httpx.Response: 响应对象
        """
        url = self._build_url(endpoint)
        merged_headers = self._merge_headers(headers)
        request_timeout = timeout or self.timeout

        # 处理JSON数据
        if json_data is not None:
            data = json.dumps(json_data, ensure_ascii=False)
            merged_headers["Content-Type"] = "application/json"
        elif data is not None:
            data = self._prepare_data(data)

        self.logger.info(f"发送异步{method}请求: {url}")

        async with httpx.AsyncClient(
                verify=self.verify,
                follow_redirects=self.follow_redirects,
                timeout=request_timeout
        ) as client:
            response = await client.request(
                method=method,
                url=url,
                params=params,
                content=data,
                headers=merged_headers,
                **kwargs
            )

        self.logger.info(f"异步响应状态码: {response.status_code}")
        return response

    # 同步方法
    def get(
            self,
            endpoint: str,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> httpx.Response:
        """发送GET请求"""
        return self.request("GET", endpoint, params=params, headers=headers, timeout=timeout, **kwargs)

    def post(
            self,
            endpoint: str,
            data: Any = None,
            json_data: Any = None,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> httpx.Response:
        """发送POST请求"""
        return self.request("POST", endpoint, params=params, data=data, json_data=json_data,
                            headers=headers, timeout=timeout, **kwargs)

    def put(
            self,
            endpoint: str,
            data: Any = None,
            json_data: Any = None,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> httpx.Response:
        """发送PUT请求"""
        return self.request("PUT", endpoint, params=params, data=data, json_data=json_data,
                            headers=headers, timeout=timeout, **kwargs)

    def delete(
            self,
            endpoint: str,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> httpx.Response:
        """发送DELETE请求"""
        return self.request("DELETE", endpoint, params=params, headers=headers, timeout=timeout, **kwargs)

    def patch(
            self,
            endpoint: str,
            data: Any = None,
            json_data: Any = None,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> httpx.Response:
        """发送PATCH请求"""
        return self.request("PATCH", endpoint, params=params, data=data, json_data=json_data,
                            headers=headers, timeout=timeout, **kwargs)

    # 异步方法
    async def async_get(
            self,
            endpoint: str,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> httpx.Response:
        """发送异步GET请求"""
        return await self.async_request("GET", endpoint, params=params, headers=headers, timeout=timeout, **kwargs)

    async def async_post(
            self,
            endpoint: str,
            data: Any = None,
            json_data: Any = None,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> httpx.Response:
        """发送异步POST请求"""
        return await self.async_request("POST", endpoint, params=params, data=data, json_data=json_data,
                                        headers=headers, timeout=timeout, **kwargs)

    async def async_put(
            self,
            endpoint: str,
            data: Any = None,
            json_data: Any = None,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> httpx.Response:
        """发送异步PUT请求"""
        return await self.async_request("PUT", endpoint, params=params, data=data, json_data=json_data,
                                        headers=headers, timeout=timeout, **kwargs)

    async def async_delete(
            self,
            endpoint: str,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> httpx.Response:
        """发送异步DELETE请求"""
        return await self.async_request("DELETE", endpoint, params=params, headers=headers, timeout=timeout, **kwargs)

    async def async_patch(
            self,
            endpoint: str,
            data: Any = None,
            json_data: Any = None,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> httpx.Response:
        """发送异步PATCH请求"""
        return await self.async_request("PATCH", endpoint, params=params, data=data, json_data=json_data,
                                        headers=headers, timeout=timeout, **kwargs)

    # 便利方法
    def get_json(
            self,
            endpoint: str,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> Dict[str, Any]:
        """发送GET请求并返回JSON数据"""
        response = self.get(endpoint, params=params, headers=headers, timeout=timeout, **kwargs)
        response.raise_for_status()
        return response.json()

    def post_json(
            self,
            endpoint: str,
            json_data: Any = None,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> Dict[str, Any]:
        """发送POST请求并返回JSON数据"""
        response = self.post(endpoint, json_data=json_data, params=params, headers=headers, timeout=timeout, **kwargs)
        response.raise_for_status()
        return response.json()

    async def async_get_json(
            self,
            endpoint: str,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> Dict[str, Any]:
        """发送异步GET请求并返回JSON数据"""
        response = await self.async_get(endpoint, params=params, headers=headers, timeout=timeout, **kwargs)
        response.raise_for_status()
        return response.json()

    async def async_post_json(
            self,
            endpoint: str,
            json_data: Any = None,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            **kwargs
    ) -> Dict[str, Any]:
        """发送异步POST请求并返回JSON数据"""
        response = await self.async_post(endpoint, json_data=json_data, params=params, headers=headers, timeout=timeout,
                                         **kwargs)
        response.raise_for_status()
        return response.json()

    def download_file(
            self,
            endpoint: str,
            file_path: str,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            chunk_size: int = 8192,
            **kwargs
    ) -> None:
        """下载文件"""
        response = self.get(endpoint, params=params, headers=headers, timeout=timeout, **kwargs)
        response.raise_for_status()

        with open(file_path, 'wb') as f:
            for chunk in response.iter_bytes(chunk_size=chunk_size):
                f.write(chunk)

        self.logger.info(f"文件下载完成: {file_path}")

    async def async_download_file(
            self,
            endpoint: str,
            file_path: str,
            params: Optional[Dict[str, Any]] = None,
            headers: Optional[Dict[str, str]] = None,
            timeout: Optional[float] = None,
            chunk_size: int = 8192,
            **kwargs
    ) -> None:
        """异步下载文件"""
        response = await self.async_get(endpoint, params=params, headers=headers, timeout=timeout, **kwargs)
        response.raise_for_status()

        with open(file_path, 'wb') as f:
            async for chunk in response.aiter_bytes(chunk_size=chunk_size):
                f.write(chunk)

        self.logger.info(f"异步文件下载完成: {file_path}")
