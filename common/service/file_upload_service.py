"""
文件上传和读取服务
支持文本文件直接上传到OSS，Office文件先转换为PDF再上传
提供从OSS读取文件内容的功能
"""

import io
import logging
import os
import tempfile
import uuid
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Union, Optional, Dict, Any

import alibabacloud_oss_v2 as oss
from fastapi import UploadFile

from common.service.office_to_pdf_service import OfficeToPdfService
from common.settings import OSS_CONFIG

# 尝试导入PDF处理库
try:
    import PyPDF2

    PDF_AVAILABLE = True
except ImportError:
    try:
        import pdfplumber

        PDF_AVAILABLE = True
    except ImportError:
        PDF_AVAILABLE = False


class FileType(Enum):
    """文件类型枚举"""
    TEXT = "text"
    OFFICE = "office"
    PDF = "pdf"
    IMAGE = "image"
    OTHER = "other"


class FileUploadService:
    """文件上传和读取服务类"""

    def __init__(self, temp_dir: Optional[str] = None):
        """
        初始化文件上传服务

        Args:
            temp_dir: 临时文件目录
        """
        self.logger = logging.getLogger(__name__)
        self.temp_dir = temp_dir or tempfile.gettempdir()

        # 初始化OSS客户端
        self._init_oss_client()

        # 初始化Office转PDF服务
        self.office_service = OfficeToPdfService(temp_dir=self.temp_dir)

        # 支持的文件扩展名
        self.text_extensions = {'.txt', '.md', '.json', '.xml', '.csv', '.log', '.py', '.js', '.html', '.css'}
        self.office_extensions = {'.docx', '.doc', '.xlsx', '.xls', '.pptx', '.ppt'}
        self.pdf_extensions = {'.pdf'}
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}

        # 确保临时目录存在
        Path(self.temp_dir).mkdir(parents=True, exist_ok=True)

    def _init_oss_client(self):
        """初始化OSS客户端"""
        try:
            # 检查OSS配置
            if not OSS_CONFIG.get("access_key_id") or not OSS_CONFIG.get("access_key_secret"):
                raise ValueError("OSS配置缺失: 请检查环境变量 OSS_ACCESS_KEY_ID 和 OSS_ACCESS_KEY_SECRET")

            # 确保环境变量设置正确
            os.environ["OSS_ACCESS_KEY_ID"] = OSS_CONFIG["access_key_id"]
            os.environ["OSS_ACCESS_KEY_SECRET"] = OSS_CONFIG["access_key_secret"]

            # 从环境变量中加载访问OSS所需的认证信息
            credentials_provider = oss.credentials.EnvironmentVariableCredentialsProvider()

            # 使用SDK的默认配置创建配置对象
            self.cfg = oss.config.load_default()
            self.cfg.credentials_provider = credentials_provider
            self.cfg.region = OSS_CONFIG["region"]

            # 如果有自定义endpoint，则设置
            if OSS_CONFIG.get("endpoint"):
                self.cfg.endpoint = OSS_CONFIG["endpoint"]

            # 初始化OSS客户端
            self.oss_client = oss.Client(self.cfg)
            self.bucket_name = OSS_CONFIG["bucket_name"]

            self.logger.info("OSS客户端初始化成功")

        except Exception as e:
            self.logger.error(f"OSS客户端初始化失败: {str(e)}")
            raise e

    def detect_file_type(self, filename: str) -> FileType:
        """
        检测文件类型
        
        Args:
            filename: 文件名
            
        Returns:
            FileType: 文件类型
        """
        suffix = Path(filename).suffix.lower()

        if suffix in self.text_extensions:
            return FileType.TEXT
        elif suffix in self.office_extensions:
            return FileType.OFFICE
        elif suffix in self.pdf_extensions:
            return FileType.PDF
        elif suffix in self.image_extensions:
            return FileType.IMAGE
        else:
            return FileType.OTHER

    def extract_pdf_text(self, pdf_content: bytes) -> str:
        """
        从PDF内容中提取文本

        Args:
            pdf_content: PDF文件的字节内容

        Returns:
            str: 提取的文本内容
        """
        if not PDF_AVAILABLE:
            return "PDF文本提取功能不可用，请安装 PyPDF2 或 pdfplumber"

        try:
            # 尝试使用 PyPDF2
            if 'PyPDF2' in globals():
                pdf_file = io.BytesIO(pdf_content)
                pdf_reader = PyPDF2.PdfReader(pdf_file)
                text_content = ""

                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text_content += page.extract_text() + "\n"

                return text_content.strip()

            # 尝试使用 pdfplumber
            elif 'pdfplumber' in globals():
                pdf_file = io.BytesIO(pdf_content)
                text_content = ""

                with pdfplumber.open(pdf_file) as pdf:
                    for page in pdf.pages:
                        page_text = page.extract_text()
                        if page_text:
                            text_content += page_text + "\n"

                return text_content.strip()

            else:
                return "PDF文本提取库未正确加载"

        except Exception as e:
            self.logger.error(f"PDF文本提取失败: {str(e)}")
            return f"PDF文本提取失败: {str(e)}"

    async def upload_file(
            self,
            file: Union[UploadFile, str, Path, bytes],
            filename: Optional[str] = None,
            convert_office_to_pdf: bool = True
    ) -> Dict[str, Any]:
        """
        上传文件到SSO

        Args:
            file: 文件对象、文件路径、UploadFile对象或字节内容
            filename: 文件名（当file是字节内容时必须提供）
            convert_office_to_pdf: 是否将Office文件转换为PDF

        Returns:
            Dict[str, Any]: 上传结果
        """
        try:
            # 处理不同类型的文件输入
            if isinstance(file, UploadFile):
                file_content = await file.read()
                file_name = file.filename
            elif isinstance(file, (str, Path)):
                file_path = Path(file)
                if not file_path.exists():
                    raise FileNotFoundError(f"文件不存在: {file_path}")
                with open(file_path, 'rb') as f:
                    file_content = f.read()
                file_name = file_path.name
            elif isinstance(file, bytes):
                file_content = file
                file_name = filename
                if not file_name:
                    raise ValueError("当file是字节内容时，必须提供filename参数")
            else:
                raise ValueError("不支持的文件类型，支持UploadFile、文件路径或字节内容")

            if filename:
                file_name = filename

            if not file_name:
                raise ValueError("无法确定文件名")

            # 检测文件类型
            file_type = self.detect_file_type(file_name)
            self.logger.info(f"检测到文件类型: {file_type.value}, 文件名: {file_name}")

            # 处理Office文件转换
            if file_type == FileType.OFFICE and convert_office_to_pdf:
                return await self._upload_office_file(file_content, file_name)
            else:
                return await self._upload_direct(file_content, file_name)

        except Exception as e:
            self.logger.error(f"文件上传失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "file_name": file_name if 'file_name' in locals() else "unknown"
            }

    async def _upload_office_file(self, file_content: bytes, file_name: str) -> Dict[str, Any]:
        """
        上传Office文件（先转换为PDF）
        
        Args:
            file_content: 文件内容
            file_name: 文件名
            
        Returns:
            Dict[str, Any]: 上传结果
        """
        temp_input_file = None
        temp_pdf_file = None

        try:
            # 保存原始文件到临时目录
            temp_input_file = Path(self.temp_dir) / f"temp_input_{file_name}"
            with open(temp_input_file, 'wb') as f:
                f.write(file_content)

            # 转换为PDF
            pdf_file_path = self.office_service.convert_to_pdf(temp_input_file)
            temp_pdf_file = Path(pdf_file_path)

            # 读取PDF内容
            with open(temp_pdf_file, 'rb') as f:
                pdf_content = f.read()

            # 生成PDF文件名
            pdf_file_name = Path(file_name).stem + '.pdf'

            # 上传PDF到OSS
            result = await self._upload_direct(pdf_content, pdf_file_name)

            # 添加转换信息
            if result.get("success"):
                result["converted_from"] = file_name
                result["original_type"] = "office"
                result["converted_to_pdf"] = True

            return result

        except Exception as e:
            self.logger.error(f"Office文件转换上传失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": f"Office文件转换失败: {str(e)}",
                "file_name": file_name
            }
        finally:
            # 清理临时文件
            for temp_file in [temp_input_file, temp_pdf_file]:
                if temp_file and temp_file.exists():
                    try:
                        temp_file.unlink()
                    except Exception as e:
                        self.logger.warning(f"清理临时文件失败: {temp_file}, 错误: {e}")

    async def _upload_direct(self, file_content: bytes, file_name: str) -> Dict[str, Any]:
        """
        直接上传文件到OSS

        Args:
            file_content: 文件内容
            file_name: 文件名

        Returns:
            Dict[str, Any]: 上传结果
        """
        try:
            # 生成唯一文件名
            file_extension = os.path.splitext(file_name)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"

            # 构建OSS key
            date_folder = datetime.now().strftime("%Y/%m/%d")
            oss_key = f"documents/{date_folder}/{unique_filename}"

            # 根据文件扩展名设置Content-Type
            headers = {}
            file_extension_lower = file_extension.lower()
            if file_extension_lower in ['.docx']:
                headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            elif file_extension_lower in ['.doc']:
                headers['Content-Type'] = 'application/msword'
            elif file_extension_lower in ['.xlsx']:
                headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            elif file_extension_lower in ['.xls']:
                headers['Content-Type'] = 'application/vnd.ms-excel'
            elif file_extension_lower in ['.pptx']:
                headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
            elif file_extension_lower in ['.ppt']:
                headers['Content-Type'] = 'application/vnd.ms-powerpoint'
            elif file_extension_lower == '.pdf':
                headers['Content-Type'] = 'application/pdf'
            elif file_extension_lower in ['.txt']:
                headers['Content-Type'] = 'text/plain'
            elif file_extension_lower in ['.jpg', '.jpeg']:
                headers['Content-Type'] = 'image/jpeg'
            elif file_extension_lower == '.png':
                headers['Content-Type'] = 'image/png'
            else:
                headers['Content-Type'] = 'application/octet-stream'

            self.logger.info(f"准备上传文件到OSS: {file_name} -> {oss_key}")

            # 创建上传请求
            put_request = oss.PutObjectRequest(
                bucket=self.bucket_name,
                key=oss_key,
                body=file_content,
                headers=headers
            )

            # 上传到OSS
            result = self.oss_client.put_object(put_request)

            if result.status_code == 200:
                # 构建原始OSS URL（用于数据库保存）
                oss_url = f"https://{self.bucket_name}.{OSS_CONFIG['endpoint'].replace('https://', '')}/{oss_key}"

                self.logger.info(f"文件上传成功: {oss_key}")
                self.logger.info(f"OSS URL: {oss_url}")

                return {
                    "success": True,
                    "file_id": oss_key,  # 使用OSS key作为文件ID
                    "file_name": unique_filename,
                    "original_name": file_name,
                    "file_size": len(file_content),
                    "file_url": oss_url,
                    "oss_key": oss_key
                }
            else:
                raise Exception(f"OSS上传失败，状态码: {result.status_code}")

        except Exception as e:
            self.logger.error(f"直接上传文件失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "file_name": file_name
            }

    async def read_file_content(self, file_id: str) -> Dict[str, Any]:
        """
        从OSS读取文件内容

        Args:
            file_id: OSS文件key

        Returns:
            Dict[str, Any]: 文件内容和信息
        """
        try:
            self.logger.info(f"从OSS读取文件: {file_id}")

            # 创建下载请求
            get_request = oss.GetObjectRequest(
                bucket=self.bucket_name,
                key=file_id
            )

            # 从OSS下载文件
            result = self.oss_client.get_object(get_request)

            if result.status_code == 200:
                # 读取文件内容
                file_content = result.body.read()

                # 从文件key中提取文件名
                file_name = os.path.basename(file_id)

                # 检测文件类型
                file_type = self.detect_file_type(file_name)

                response_data = {
                    "success": True,
                    "file_id": file_id,
                    "file_name": file_name,
                    "file_size": len(file_content),
                    "file_type": file_type.value,
                    "content": file_content
                }

                # 根据文件类型处理文本内容
                if file_type == FileType.TEXT:
                    # 文本文件：尝试解码为字符串
                    try:
                        text_content = file_content.decode('utf-8')
                        response_data["text_content"] = text_content
                    except UnicodeDecodeError:
                        try:
                            text_content = file_content.decode('gbk')
                            response_data["text_content"] = text_content
                        except UnicodeDecodeError:
                            response_data["text_content"] = None
                            response_data["decode_error"] = "无法解码文本内容"

                elif file_type == FileType.PDF:
                    # PDF文件：提取文本内容
                    try:
                        text_content = self.extract_pdf_text(file_content)
                        if text_content and text_content.strip():
                            response_data["text_content"] = text_content
                            response_data["pdf_extracted"] = True
                        else:
                            response_data["text_content"] = None
                            response_data["decode_error"] = "PDF文件为空或无法提取文本"
                    except Exception as e:
                        response_data["text_content"] = None
                        response_data["decode_error"] = f"PDF文本提取失败: {str(e)}"

                return response_data
            else:
                raise Exception(f"从OSS下载文件失败，状态码: {result.status_code}")

        except Exception as e:
            self.logger.error(f"读取文件内容失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "file_id": file_id
            }

    async def get_file_info(self, file_id: str) -> Dict[str, Any]:
        """
        获取OSS文件信息

        Args:
            file_id: OSS文件key

        Returns:
            Dict[str, Any]: 文件信息
        """
        try:
            self.logger.info(f"获取OSS文件信息: {file_id}")

            # 创建获取文件元数据请求
            head_request = oss.HeadObjectRequest(
                bucket=self.bucket_name,
                key=file_id
            )

            # 获取文件元数据
            result = self.oss_client.head_object(head_request)

            if result.status_code == 200:
                # 从文件key中提取文件名
                file_name = os.path.basename(file_id)

                return {
                    "success": True,
                    "file_id": file_id,
                    "file_name": file_name,
                    "file_size": result.headers.get('Content-Length', 0),
                    "content_type": result.headers.get('Content-Type', 'application/octet-stream'),
                    "last_modified": result.headers.get('Last-Modified'),
                    "etag": result.headers.get('ETag')
                }
            else:
                raise Exception(f"获取OSS文件信息失败，状态码: {result.status_code}")

        except Exception as e:
            self.logger.error(f"获取文件信息失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "file_id": file_id
            }

    def generate_presigned_url(self, file_id: str, expires: int = 3600, method: str = 'GET') -> str:
        """
        生成预签名URL用于授权访问OSS文件

        Args:
            file_id: OSS文件key
            expires: 过期时间（秒），默认1小时
            method: HTTP方法，默认GET

        Returns:
            str: 预签名授权URL
        """
        try:
            # 限制最大过期时间为7天
            expires = min(expires, 7 * 24 * 3600)

            if method.upper() == 'GET':
                # 创建GET请求
                get_request = oss.GetObjectRequest(
                    bucket=self.bucket_name,
                    key=file_id
                )

                # 创建过期时间间隔
                expiration_delta = timedelta(seconds=expires)

                # 生成预签名URL
                pre_result = self.oss_client.presign(get_request, expires=expiration_delta)

                self.logger.info(f"生成预签名URL成功: {file_id}, 过期时间: {expires}秒")

                return pre_result.url
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

        except Exception as e:
            self.logger.error(f"生成预签名URL失败: {str(e)}")
            raise e

    async def get_file_access_url(self, file_id: str, expires: int = 3600) -> Dict[str, Any]:
        """
        获取文件访问URL

        Args:
            file_id: OSS文件key
            expires: 过期时间（秒），默认1小时

        Returns:
            Dict[str, Any]: 包含访问URL的结果
        """
        try:
            # 生成预签名URL
            presigned_url = self.generate_presigned_url(file_id, expires)

            # 构建原始OSS URL（用于参考）
            oss_url = f"https://{self.bucket_name}.{OSS_CONFIG['endpoint'].replace('https://', '')}/{file_id}"

            return {
                "success": True,
                "file_id": file_id,
                "presigned_url": presigned_url,
                "oss_url": oss_url,
                "expires_in": expires,
                "expires_at": (datetime.now() + timedelta(seconds=expires)).isoformat()
            }

        except Exception as e:
            self.logger.error(f"获取文件访问URL失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "file_id": file_id
            }

    async def delete_file(self, file_id: str) -> Dict[str, Any]:
        """
        从OSS删除文件

        Args:
            file_id: OSS文件key

        Returns:
            Dict[str, Any]: 删除结果
        """
        try:
            self.logger.info(f"删除OSS文件: {file_id}")

            # 创建删除请求
            delete_request = oss.DeleteObjectRequest(
                bucket=self.bucket_name,
                key=file_id
            )

            # 从OSS删除文件
            result = self.oss_client.delete_object(delete_request)

            if result.status_code == 204:  # OSS删除成功返回204
                self.logger.info(f"OSS文件删除成功: {file_id}")
                return {
                    "success": True,
                    "file_id": file_id,
                    "message": "文件删除成功"
                }
            else:
                raise Exception(f"OSS文件删除失败，状态码: {result.status_code}")

        except Exception as e:
            self.logger.error(f"删除文件失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "file_id": file_id
            }


# 异常类定义
class FileUploadServiceError(Exception):
    """文件上传服务异常"""
    pass
