# @Time: 2025/8/22 09:28
# @Author: lvjing
"""
PDF文档解析服务

专门负责解析PDF文件，提取文本和表格内容

核心功能：
1. 支持多种PDF解析库（PyPDF2, pdfplumber, PyMuPDF, pdfminer）
2. 智能选择最佳解析方法
3. 提取纯文本内容
4. 提取表格内容并进行格式化
5. 提供统一的PDF内容提取接口

使用场景：
- 作为核心服务，被其他服务（如API文档解析服务）调用
- 独立处理PDF文件，提供高质量的文本内容
"""
import logging
from pathlib import Path

import PyPDF2
import fitz  # PyMuPDF
import pdfplumber
from pdfminer.high_level import extract_text

logger = logging.getLogger(__name__)


class PdfParserService:
    """
    PDF文档解析服务类

    提供统一的PDF内容提取接口，支持多种PDF解析库
    """

    def __init__(self):
        """
        初始化PDF解析服务
        """
        logger.info("PDF解析服务初始化完成")

    async def extract_pdf_content(self, file_path: Path) -> str:
        """
        提取PDF文档内容 - 支持多种PDF解析库

        Args:
            file_path: PDF文件路径

        Returns:
            str: 提取的文本内容

        Raises:
            RuntimeError: 所有PDF解析方法都失败
        """
        try:
            logger.info(f"开始提取PDF内容: {file_path.name}")

            # 尝试使用PyPDF2提取文本
            try:
                logger.info("使用 PyPDF2 备用方法提取 PDF 内容")
                return await self._extract_with_pypdf2(file_path)
            except ImportError:
                logger.debug("PyPDF2未安装，尝试其他方法")
            except Exception as e:
                logger.warning(f"PyPDF2提取失败: {str(e)}")

            # 尝试使用pdfplumber提取文本
            try:
                logger.info("使用 pdfplumber 备用方法提取 PDF 内容")
                return await self._extract_with_pdfplumber(file_path)
            except ImportError:
                logger.debug("pdfplumber未安装，尝试其他方法")
            except Exception as e:
                logger.warning(f"pdfplumber提取失败: {str(e)}")

            # 尝试使用pymupdf (fitz)提取文本
            try:
                logger.info("使用 PyMuPDF 备用方法提取 PDF 内容")
                return await self._extract_with_pymupdf(file_path)
            except ImportError:
                logger.debug("PyMuPDF未安装，尝试其他方法")
            except Exception as e:
                logger.warning(f"PyMuPDF提取失败: {str(e)}")

            # 尝试使用pdfminer提取文本
            try:
                logger.info("使用 pdfminer 备用方法提取 PDF 内容")
                return await self._extract_with_pdfminer(file_path)
            except ImportError:
                logger.debug("pdfminer未安装")
            except Exception as e:
                logger.warning(f"pdfminer提取失败: {str(e)}")

            # 如果所有方法都失败，返回错误信息
            error_msg = f"无法提取PDF内容: {file_path.name}。请安装PDF解析库。"
            logger.error(error_msg)
            raise RuntimeError(error_msg)

        except Exception as e:
            logger.error(f"PDF内容提取失败: {str(e)}")
            raise

    async def _extract_with_pypdf2(self, file_path: Path) -> str:
        """
        使用PyPDF2提取PDF内容
        """
        text_content = []
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page_num, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text and page_text.strip():
                        text_content.append(f"=== 第{page_num + 1}页 ===\n{page_text}\n")
                except Exception as e:
                    logger.warning(f"PyPDF2提取第{page_num + 1}页失败: {str(e)}")
                    continue
        if not text_content:
            raise RuntimeError("PyPDF2未能提取到任何文本内容")
        return "\n".join(text_content)

    async def _extract_with_pdfplumber(self, file_path: Path) -> str:
        """
        使用pdfplumber提取PDF内容
        """
        text_content = []
        with pdfplumber.open(file_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                try:
                    page_text = page.extract_text()
                    if page_text and page_text.strip():
                        text_content.append(f"=== 第{page_num + 1}页 ===\n{page_text}\n")
                    tables = page.extract_tables()
                    if tables:
                        for table_num, table in enumerate(tables):
                            table_text = self._format_table_content(table, page_num + 1, table_num + 1)
                            text_content.append(table_text)
                except Exception as e:
                    logger.warning(f"pdfplumber提取第{page_num + 1}页失败: {str(e)}")
                    continue
        if not text_content:
            raise RuntimeError("pdfplumber未能提取到任何文本内容")
        return "\n".join(text_content)

    async def _extract_with_pymupdf(self, file_path: Path) -> str:
        """
        使用PyMuPDF提取PDF内容
        """
        text_content = []
        pdf_document = fitz.open(file_path)
        try:
            for page_num in range(pdf_document.page_count):
                try:
                    page = pdf_document[page_num]
                    page_text = page.get_text()
                    if page_text and page_text.strip():
                        text_content.append(f"=== 第{page_num + 1}页 ===\n{page_text}\n")
                    tables = page.find_tables()
                    if tables:
                        for table_num, table in enumerate(tables):
                            try:
                                table_data = table.extract()
                                table_text = self._format_table_content(table_data, page_num + 1, table_num + 1)
                                text_content.append(table_text)
                            except Exception as e:
                                logger.warning(f"提取表格失败: {str(e)}")
                except Exception as e:
                    logger.warning(f"PyMuPDF提取第{page_num + 1}页失败: {str(e)}")
                    continue
        finally:
            pdf_document.close()
        if not text_content:
            raise RuntimeError("PyMuPDF未能提取到任何文本内容")
        return "\n".join(text_content)

    async def _extract_with_pdfminer(self, file_path: Path) -> str:
        """
        使用pdfminer提取PDF内容
        """
        try:
            text_content = extract_text(str(file_path))
            if not text_content or not text_content.strip():
                raise RuntimeError("pdfminer未能提取到任何文本内容")
            return f"=== PDF文档内容 ===\n{text_content}"
        except Exception as e:
            logger.error(f"pdfminer提取失败: {str(e)}")
            raise RuntimeError(f"pdfminer提取失败: {str(e)}")

    def _format_table_content(self, table_data: list, page_num: int, table_num: int) -> str:
        """
        格式化表格内容
        """
        try:
            if not table_data:
                return ""
            formatted_lines = [f"=== 第{page_num}页 表格{table_num} ==="]
            for row in table_data:
                if row and any(cell for cell in row if cell):
                    cleaned_row = []
                    for cell in row:
                        if cell:
                            cleaned_cell = str(cell).strip().replace('\n', ' ').replace('\r', '')
                            cleaned_row.append(cleaned_cell)
                        else:
                            cleaned_row.append("")
                    formatted_lines.append("\t".join(cleaned_row))
            formatted_lines.append("")
            return "\n".join(formatted_lines)
        except Exception as e:
            logger.warning(f"格式化表格内容失败: {str(e)}")
            return ""
