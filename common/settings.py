# @Time：2025/3/19 16:32
# @Author：jinglv
import os

# =========================数据库的配置信息=======================
DATABASE = {
    'host': 'rm-uf64hi1cl8l2g83zr.mysql.rds.aliyuncs.com',
    'port': '3306',
    'user': 'api_server',
    'password': 'weiWur3slkYjfj8sd',
    'database': 'api_server_new',
}

# 项目中的所以应用的models
INSTALLED_APPS = [
    'apps.users.models',
    'apps.project.models',
    'apps.project_member.models',
    'apps.requirement.models',
    'apps.material.models',
    'apps.suite.models',
    'apps.ai_test.models',
    'apps.llm_manage.models',
    'apps.prompt_template.models',
    'apps.prompt.models',
    'apps.suite_execute.models',
    'apps.chat.models',
    'apps.agent_tools.models',
    'apps.ai_api_auto.models',
]
# 关于tortoise的配置
TORTOISE_ORM = {
    'connections': {
        'default': {
            'engine': 'tortoise.backends.mysql',
            'credentials': DATABASE
        },
    },
    'apps': {
        'models': {
            'models': ['aerich.models', *INSTALLED_APPS],  # 模型类所在的包名
            'default_connection': 'default',
        },
    },
    'use_tz': False,
    'timezone': 'Asia/Shanghai',
}
# 统一登陆相关配置
SSO = {
    # TODO 生产环境读取环境配置，测试走配置文件
    'app_id': "739276",
    'app_secret': "3d559538-6511-43f9-b678-ae0b9db9691d",
    'env': {
        'dev': 'https://api-dev.seres.cn/sso',
        'test': 'https://api-test.seres.cn/sso',
        'uat': 'https://api-uat.seres.cn/sso',
        'prod': 'https://api.seres.cn/sso',
    },
    'api': {
        'get_token': '/cgi-bin/getToken',  # /cgi-bin/getToken?appld=APPID&appSecret=APPSECRET
        'get_user_info': '/v1/base/getUserInfoByTicket'  # /v1/base/getUserInfoByTicket?clientToken=TOKEN&ticket=TICKET
    }
}

# jwt
JWT = {
    "ALGORITHM": 'HS256',
    "SECRET_KEY": '3018f08b17fdec3ac298b70e0f98bcb43534359ae12c274e517fdd434772d89e',
    "ACCESS_TOKEN_EXPIRE_HOURS": 24,
}

AI_MODEL = {
    "API_BASE": "https://oneapi-test.seres.cn/v1",
    "API_KEY": "sk-MPNhpvlyGVWACR0z892317Cf5377424d9bAfA1662d75E697",
    "MODEL": "qwen2.5-instruct"
}

# OSS配置
OSS_CONFIG = {
    "endpoint": os.getenv("OSS_ENDPOINT", "https://oss-cn-shanghai.aliyuncs.com"),
    "access_key_id": os.getenv("OSS_ACCESS_KEY_ID", "LTAI5t6hBZ9Y8SAup5iUeKuc"),
    "access_key_secret": os.getenv("OSS_ACCESS_KEY_SECRET", "******************************"),
    "bucket_name": os.getenv("OSS_BUCKET_NAME", "ai-rag-data"),
    "region": os.getenv("OSS_REGION", "cn-shanghai"),
}

# 文件上传配置
UPLOAD_CONFIG = {
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "allowed_extensions": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"],
    "upload_path": "uploads/images/",
}

# 应用配置
APP_CONFIG = {
    "title": "测试用例生成器",
    "version": "1.0.0",
    "description": "基于AI的智能测试用例生成系统",
    "cors_origins": ["*"],  # 生产环境中应该设置具体的域名
}
