import datetime
import jwt
import requests
from fastapi import Request
from tortoise.exceptions import DoesNotExist
from tortoise.models import Model
from typing import Type
from apps.project_member.models import ProjectMemberModel
from apps.users.models import UsersModel
from common.settings import JWT, SSO


# 响应相关
def response_success(data=None, msg=None, total=None):
    return {
        "success": True,
        "msg": "成功",
        "resp": data,
        "total": total
    }


def response_fail(data=None, msg=None, total=None):
    return {
        "success": False,
        "msg": msg,
        "resp": data,
        "total": None
    }


def response_success_paged(data=None, total=None, current_page=None, total_page=None):
    return {
        "success": True,
        "msg": "成功",
        "resp": data,
        "total": total,
        "current_page": current_page,
        "total_page": total_page
    }


# JWT相关
def encode_jwt(payload: dict, exp: datetime = None):
    """
    创建jwt令牌
    """
    if exp is None:
        payload.update({
            'exp': datetime.datetime.now(datetime.UTC) + datetime.timedelta(hours=JWT['ACCESS_TOKEN_EXPIRE_HOURS'])
        })
    else:
        payload.update({'exp': exp})

    token = jwt.encode(
        payload=payload,
        key=JWT['SECRET_KEY'],
        algorithm=JWT['ALGORITHM']
    )
    return token


def verify_jwt(token):
    """
    验证jwt令牌
    """
    try:
        # 解码并验证JWT
        decoded_payload = jwt.decode(token, JWT['SECRET_KEY'], algorithms=[JWT['ALGORITHM']])
        return True, decoded_payload

    except jwt.ExpiredSignatureError:
        return False, "Token 已过期"

    except jwt.InvalidTokenError:
        return False, "Token 无效"

    except Exception as e:
        return False, "系统异常:\n{}".format(e)


# SSO相关接口
def sso_client_token_get(env: str):
    """
    获取sso的client
    """
    import logging
    logger = logging.getLogger(__name__)

    try:
        params = {
            'appId': SSO['app_id'],
            'appSecret': SSO['app_secret']
        }
        url = env + "/cgi-bin/getToken"

        logger.info(f"SSO Token请求 - URL: {url}, 参数: {params}")
        response = requests.get(url=url, params=params)
        if response.status_code == 200:
            resp_data = response.json()
            logger.info(f"SSO Token响应: {resp_data}")
            if resp_data['success']:
                cli_token = resp_data['resp'][0]['clientToken']
                logger.info(f"成功解析Token: {cli_token[:20]}..." if cli_token else "Token为空")
                return True, cli_token
            else:
                logger.error(f"SSO Token获取失败: {resp_data}")
                return False, resp_data
        else:
            logger.error(f"SSO Token请求HTTP失败: {response.status_code}, {response.text}")
            return False, response.content

    except Exception as e:
        return False, 'SSO接口调用异常' + e


def verify_sso_user_ticket(env: str, ticket: str):
    """
    验证用户ticket
    """

    status, cli_token = sso_client_token_get(env)
    if not status:
        return status, '[获取sso的cli_token失败] ' + str(cli_token)
    try:
        params = {
            'clientToken': cli_token,
            'ticket': ticket
        }
        url = env + "/v1/base/getUserInfoByTicket"
        response = requests.get(url=url, params=params)
        if response.status_code == 200:
            resp_data = response.json()
            if resp_data['success']:
                return True, resp_data['resp'][0]
            else:
                return False, resp_data['msg']
        else:
            return False, '调用SSO用户验证接口失败'
    except Exception as e:
        return False, '调用SSO用户验证接口异常' + e


# 接口执行人
async def get_caller(request: Request) -> UsersModel | None:
    try:
        user = await UsersModel.get(id=request.state.user_id)
        return user
    except DoesNotExist:
        return None


async def is_superuser(request: Request) -> bool:
    try:
        user = await UsersModel.get(id=request.state.user_id)
        return user.role == 1
    except DoesNotExist:
        return False


async def is_project_member(project_id, user_id) -> bool:
    return await ProjectMemberModel.filter(project_id=project_id, user_id=user_id).exists()


def get_caller_id(request: Request) -> int | None:
    try:
        return request.state.user_id
    except Exception:
        return None


async def model_data_exist(pk: int, model: Type[Model]):
    return await model.filter(pk=pk).exists()


def verify_template(template: str):
    """
    验证字符串是不是**{}**{}**{}分割
    :param template:
    :return:
    """
    stack = list()
    original_var = list()
    var_list = list()

    for word in template:
        if word == "{":
            # 左括号多
            if len(stack) != 0:
                return False, None
            else:
                stack.append(word)

        elif word == "}":
            # 右括号多
            if len(stack) != 1:
                return False, None
            else:
                # 左右括号包含的变量为空
                if len(original_var) == 0:
                    return False, None
                else:
                    stack.pop()
                    var_list.append(''.join(original_var))
                    original_var.clear()
        else:
            if len(stack) == 1:
                original_var.append(word)
    return True, var_list
