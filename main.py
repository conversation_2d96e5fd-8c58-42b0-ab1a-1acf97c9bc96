#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AutoMatrix平台主应用入口

该文件是AutoMatrix AI测试平台的主要应用配置文件，包含：
- FastAPI应用初始化和配置
- 数据库ORM配置
- 路由注册
- 中间件配置
- CORS跨域配置

@Time: 2025/3/19 16:26
@Author: jinglv
@LastModified: 2025/7/7
@Version: 2.0
"""

import uvicorn
from fastapi import FastAPI
from starlette.middleware.cors import CORSMiddleware
from tortoise.contrib.fastapi import register_tortoise

from apps.agent_tools.test_cases import router as agent_tools_router
from apps.ai_api_auto.api import router as api_auto_router
from apps.ai_test.api import router as ai_test_router
# ==================== 应用模块导入 ====================
from apps.chat.api import router as ai_agent_router
from apps.llm_manage.api import router as llm_manage_router
from apps.material.api import material_router
from apps.project.api import project_router
from apps.project_member.api import project_member_router
from apps.prompt.api import prompt_router
from apps.prompt_template.api import prompt_template_router
from apps.reports.api import router as reports_router
from apps.requirement.api import requirement_router
from apps.suite.api import suite_router
from apps.suite_execute.api import suite_execute_router
from apps.users.api import router as user_router
# ==================== 公共模块导入 ====================
from common.middleware import VerifyAuthMiddleware
from common.settings import TORTOISE_ORM
from log_show_time import lifespan
from tests.api import test_router

# ==================== FastAPI应用配置 ====================
app = FastAPI(
    title="AutoMatrix AI测试平台",
    version="2.0.0",
    description="AutoMatrix AI测试平台API文档",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    contact={
        "name": "AutoMatrix团队",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
)

# ==================== 数据库ORM配置 ====================
register_tortoise(
    app,
    config=TORTOISE_ORM,
    modules={"models": ["models"]},
    generate_schemas=False,  # 生产环境设为False
    add_exception_handlers=True
)

# ==================== 路由注册 ====================
# 用户和权限管理
app.include_router(user_router, tags=["用户管理"])

# 项目管理相关
app.include_router(project_router, tags=["项目管理"])
app.include_router(project_member_router, tags=["项目成员管理"])
app.include_router(requirement_router, tags=["需求管理"])
app.include_router(material_router, tags=["素材管理"])

# 测试用例和执行
app.include_router(suite_router, tags=["测试套件管理"])
app.include_router(suite_execute_router, tags=["测试执行管理"])

# API自动化管理
app.include_router(api_auto_router, tags=["API接口自动化管理"])

# AI功能模块
app.include_router(ai_test_router, tags=["AI测试评估"])
app.include_router(ai_agent_router, tags=["AI智能体"])
app.include_router(llm_manage_router, tags=["大模型管理"])
app.include_router(agent_tools_router, tags=["智能体工具"])

# 提示词和模板管理
app.include_router(prompt_router, tags=["提示词管理"])
app.include_router(prompt_template_router, tags=["提示词模板"])

# 报告和分析
app.include_router(reports_router, tags=["报告管理"])

# 测试和开发接口
app.include_router(test_router, tags=["测试接口"])

# ==================== 中间件配置 ====================
# 认证中间件（注意：中间件的添加顺序很重要）
app.add_middleware(VerifyAuthMiddleware)

# CORS跨域中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://************:5555",  # 生产环境前端
        "http://localhost:5173",  # 本地开发前端
        "http://127.0.0.1:5173",  # 本地开发前端
        "http://localhost:3000",  # 备用前端端口
        "http://127.0.0.1:3000",  # 备用前端端口
    ],
    allow_credentials=True,  # 允许携带认证信息
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有请求头
    allow_origin_regex=r"http://localhost:.*"  # 允许所有localhost端口
)

# ==================== 应用启动配置 ====================
if __name__ == '__main__':
    # 开发环境启动配置
    # 生产环境建议使用: uvicorn main:app --host 0.0.0.0 --port 8080 --workers 4
    uvicorn.run(
        'main:app',
        host='127.0.0.1',
        port=8080,
        loop='asyncio',
        reload=True,
        log_level="info",
        access_log=True
    )
