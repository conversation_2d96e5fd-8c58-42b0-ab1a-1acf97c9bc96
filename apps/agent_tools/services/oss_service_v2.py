#!/anaconda3/envs/FEALPy/bin python3.8
# -*- coding: utf-8 -*-
# @Author: lijing.wang
# @E-mail: <EMAIL>
# @Time: 6月 27, 2025

"""
OSS对象存储服务 - 使用阿里云OSS v2 SDK
"""
import alibabacloud_oss_v2 as oss
import uuid
import os
import shutil
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, <PERSON><PERSON>
from fastapi import UploadFile

from common.settings import OSS_CONFIG, UPLOAD_CONFIG
import logging

logger = logging.getLogger(__name__)


class OSSServiceV2:
    """OSS对象存储服务类 - 使用v2 SDK"""
    
    def __init__(self):
        """初始化OSS服务"""
        # 检查OSS配置
        if not OSS_CONFIG.get("access_key_id") or not OSS_CONFIG.get("access_key_secret"):
            logger.error("OSS配置缺失: access_key_id 或 access_key_secret 为空")
            raise ValueError("OSS配置缺失: 请检查环境变量 OSS_ACCESS_KEY_ID 和 OSS_ACCESS_KEY_SECRET")
        
        # 确保环境变量设置正确
        os.environ["OSS_ACCESS_KEY_ID"] = OSS_CONFIG["access_key_id"]
        os.environ["OSS_ACCESS_KEY_SECRET"] = OSS_CONFIG["access_key_secret"]
        
        # 从环境变量中加载访问OSS所需的认证信息
        credentials_provider = oss.credentials.EnvironmentVariableCredentialsProvider()
        
        # 使用SDK的默认配置创建配置对象
        self.cfg = oss.config.load_default()
        self.cfg.credentials_provider = credentials_provider
        self.cfg.region = OSS_CONFIG["region"]
        
        # 如果有自定义endpoint，则设置
        if OSS_CONFIG.get("endpoint"):
            self.cfg.endpoint = OSS_CONFIG["endpoint"]
        
        # 初始化OSS客户端
        self.client = oss.Client(self.cfg)
        self.bucket_name = OSS_CONFIG["bucket_name"]
        
        logger.info("使用OSS v2 SDK初始化成功")
    
    def upload_file(self, file: UploadFile, folder: str = "uploads", expires: int = 1800) -> Tuple[str, str, str, str]:
        """
        上传文件到OSS并返回原始URL、授权URL、OSS Key和本地文件路径

        Args:
            file: 上传的文件
            folder: 存储文件夹
            expires: 授权URL过期时间（秒），默认30分钟

        Returns:
            Tuple[str, str, str, str]: (原始OSS URL, 授权访问URL, OSS Key, 本地文件路径)
        """
        try:
            # 生成唯一文件名
            file_extension = os.path.splitext(file.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"

            # 构建OSS key
            date_folder = datetime.now().strftime("%Y/%m/%d")
            oss_key = f"{folder}/{date_folder}/{unique_filename}"

            # 创建本地upload目录结构
            local_upload_dir = Path("upload") / folder / date_folder
            local_upload_dir.mkdir(parents=True, exist_ok=True)

            # 本地文件路径
            local_file_path = local_upload_dir / unique_filename

            # 读取文件内容
            file_content = file.file.read()

            # 先保存到本地
            logger.info(f"保存文件到本地: {local_file_path}")
            with open(local_file_path, "wb") as local_file:
                local_file.write(file_content)

            logger.info(f"文件本地保存成功: {local_file_path}")

            # 构建本地文件URL（相对路径）
            image_url = str(local_file_path).replace("\\", "/")  # 确保使用正斜杠
            
            # 设置上传时的Header
            headers = {}
            if file.content_type:
                headers['Content-Type'] = file.content_type
            
            # 创建上传请求
            put_request = oss.PutObjectRequest(
                bucket=self.bucket_name,
                key=oss_key,
                body=file_content,
                headers=headers
            )
            
            # 上传到OSS
            result = self.client.put_object(put_request)
            
            if result.status_code == 200:
                # 构建原始OSS URL（用于数据库保存）
                oss_url = f"https://{self.bucket_name}.{OSS_CONFIG['endpoint'].replace('https://', '')}/{oss_key}"

                # 生成授权访问URL（用于实际访问）
                authorized_url = self.generate_presigned_url(oss_key, expires)

                logger.info(f"文件上传成功: {oss_key}")
                logger.info(f"原始URL: {oss_url}")
                logger.info(f"授权URL: {authorized_url}")
                logger.info(f"本地文件路径: {image_url}")

                return oss_url, authorized_url, oss_key, image_url
            else:
                # 如果OSS上传失败，删除本地文件
                if local_file_path.exists():
                    local_file_path.unlink()
                    logger.warning(f"OSS上传失败，已删除本地文件: {local_file_path}")
                raise Exception(f"OSS上传失败，状态码: {result.status_code}")
                
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise e
        finally:
            # 重置文件指针
            file.file.seek(0)
    
    def upload_bytes(self, content: bytes, filename: str, folder: str = "generated", expires: int = 1800) -> Tuple[str, str, str]:
        """
        上传字节内容到OSS并返回原始URL、授权URL和OSS Key
        
        Args:
            content: 文件内容字节
            filename: 文件名
            folder: 存储文件夹
            expires: 授权URL过期时间（秒），默认30分钟
            
        Returns:
            Tuple[str, str, str]: (原始OSS URL, 授权访问URL, OSS Key)
        """
        try:
            # 构建OSS key
            date_folder = datetime.now().strftime("%Y/%m/%d")
            oss_key = f"{folder}/{date_folder}/{filename}"
            
            # 根据文件扩展名设置Content-Type
            headers = {}
            file_extension = os.path.splitext(filename)[1].lower()
            if file_extension == '.xlsx':
                headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            elif file_extension == '.xmind':
                headers['Content-Type'] = 'application/xmind'
            elif file_extension in ['.jpg', '.jpeg']:
                headers['Content-Type'] = 'image/jpeg'
            elif file_extension == '.png':
                headers['Content-Type'] = 'image/png'
            
            # 创建上传请求
            put_request = oss.PutObjectRequest(
                bucket=self.bucket_name,
                key=oss_key,
                body=content,
                headers=headers
            )
            
            # 上传到OSS
            result = self.client.put_object(put_request)
            
            if result.status_code == 200:
                # 构建原始OSS URL（用于数据库保存）
                oss_url = f"https://{self.bucket_name}.{OSS_CONFIG['endpoint'].replace('https://', '')}/{oss_key}"
                
                # 生成授权访问URL（用于实际访问）
                authorized_url = self.generate_presigned_url(oss_key, expires)
                
                logger.info(f"文件上传成功: {oss_key}")
                logger.info(f"原始URL: {oss_url}")
                logger.info(f"授权URL: {authorized_url}")
                
                return oss_url, authorized_url, oss_key
            else:
                raise Exception(f"OSS上传失败，状态码: {result.status_code}")
                
        except Exception as e:
            logger.error(f"文件上传失败: {str(e)}")
            raise e
    
    def delete_local_file(self, local_file_path: str) -> bool:
        """
        删除本地文件

        Args:
            local_file_path: 本地文件路径

        Returns:
            bool: 删除是否成功
        """
        try:
            file_path = Path(local_file_path)
            if file_path.exists():
                file_path.unlink()
                logger.info(f"本地文件删除成功: {local_file_path}")
                return True
            else:
                logger.warning(f"本地文件不存在: {local_file_path}")
                return False
        except Exception as e:
            logger.error(f"本地文件删除失败: {str(e)}")
            return False

    def delete_file(self, oss_key: str, local_file_path: str = None) -> bool:
        """
        删除OSS文件和本地文件

        Args:
            oss_key: OSS文件key
            local_file_path: 本地文件路径（可选）

        Returns:
            bool: 删除是否成功
        """
        oss_success = True
        local_success = True

        # 删除OSS文件
        try:
            delete_request = oss.DeleteObjectRequest(
                bucket=self.bucket_name,
                key=oss_key
            )

            result = self.client.delete_object(delete_request)

            if result.status_code == 204:
                logger.info(f"OSS文件删除成功: {oss_key}")
                oss_success = True
            else:
                logger.error(f"OSS文件删除失败，状态码: {result.status_code}")
                oss_success = False

        except Exception as e:
            logger.error(f"OSS文件删除失败: {str(e)}")
            oss_success = False

        # 删除本地文件（如果提供了路径）
        if local_file_path:
            local_success = self.delete_local_file(local_file_path)

        return oss_success and local_success
    
    def generate_presigned_url(self, oss_key: str, expires: int = 1800, method: str = 'GET') -> str:
        """
        生成预签名URL用于授权访问
        
        Args:
            oss_key: OSS文件key
            expires: 过期时间（秒），默认30分钟
            method: HTTP方法，默认GET
            
        Returns:
            str: 预签名授权URL
        """
        try:
            # 限制最大过期时间为32400秒（9小时）
            expires = min(expires, 32400)
            
            if method.upper() == 'GET':
                # 创建GET请求
                get_request = oss.GetObjectRequest(
                    bucket=self.bucket_name,
                    key=oss_key
                )
                
                # 创建过期时间间隔
                expiration_delta = timedelta(seconds=expires)

                # 生成预签名URL
                pre_result = self.client.presign(get_request, expires=expiration_delta)
                
                logger.info(f"生成预签名URL成功: {oss_key}, 过期时间: {expires}秒")
                logger.info(f"URL过期时间: {pre_result.expiration.strftime('%Y-%m-%dT%H:%M:%S.000Z')}")
                
                return pre_result.url
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
                
        except Exception as e:
            logger.error(f"生成预签名URL失败: {str(e)}")
            raise e
    
    def get_download_url(self, oss_key: str, expires: int = 1800) -> str:
        """
        获取文件下载URL（兼容性方法）
        
        Args:
            oss_key: OSS文件key
            expires: 过期时间（秒）
            
        Returns:
            str: 下载URL
        """
        return self.generate_presigned_url(oss_key, expires, 'GET')


# 创建全局实例
oss_service_v2 = OSSServiceV2()
