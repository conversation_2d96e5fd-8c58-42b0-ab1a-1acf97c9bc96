import json
import re
from typing import List, Dict, Any, AsyncGenerator

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, MultiModalMessage as AGMultiModalMessage, StructuredMessage
from autogen_core import Image as AGImage
from PIL import Image as PILImage
from common.llms import model_client

from apps.agent_tools.models import TestCaseSchema, TestCaseResponse,TestCase
import logging
logger = logging.getLogger(__name__)

class AIService:
    def __init__(self):
        # 在这里初始化 AI 模型
        # 在真实实现中，你需要加载模型
        self.image_analysis_model = None
        self.test_case_generator_model = None

    def extract_json(self, text: str) -> str:
        # 支持匹配 JSON 花括号块 或 LaTeX 双美元符号块（支持嵌套）
        match = re.search(r'(\{(?:[^{}]|(?-))*})|(\$\$[^$]*(?:\$[^\$][^$]*)*\$\$)', text, flags=re.DOTALL)
        if match:
            return match.group(0)
        return ""
    async def generate_test_cases_stream(
        self,
        image_path: str,
        context: str,
        requirements: str
    ) -> AsyncGenerator[str, None]:
        """
        基于图像分析、上下文和需求生成测试用例

        参数:
            image_path: 图像路径
            context: 用户提供的上下文
            requirements: 用户提供的需求

        产出:
            Markdown 格式的生成的测试用例块
        """
        # 在真实实现中，你需要调用支持流式输出的 LLM API
        pil_image = PILImage.open(image_path)
        img = AGImage(pil_image)

        # 构建提示词，要求先生成 Markdown 格式的测试用例，然后再生成结构化的 JSON 数据
        prompt = f"""请基于上传的图像生成全面的测试用例。

上下文信息: {context}

需求: {requirements}

请先以 Markdown 格式生成测试用例，包含以下内容：
1. 测试用例 ID 和标题（使用二级标题格式，如 ## TC-001: 测试标题）
2. 优先级（加粗显示，如 **优先级:** 高）
3. 描述（加粗显示，如 **描述:** 测试描述）
4. 前置条件（如果有，加粗显示，如 **前置条件:** 条件描述）
5. 测试步骤和预期结果（使用标准 Markdown 表格格式）

对于测试步骤表格，请使用以下格式：

```
### 测试步骤

| # | 步骤描述 | 预期结果 |
| --- | --- | --- |
| 1 | 第一步描述 | 第一步预期结果 |
| 2 | 第二步描述 | 第二步预期结果 |
```

请确保表格格式正确，包含表头和分隔行。

然后，在生成完 Markdown 格式的测试用例后，请生成结构化的测试用例数据，包含相同的内容，但使用 JSON 格式，以便于导出到 Excel。

请确保测试用例覆盖全面，包含正向和负向测试场景。"""



        multi_modal_message = AGMultiModalMessage(content=[prompt, img], source="user")
        agent = AssistantAgent(
            name=f"agent",
            model_client=model_client,
            system_message="你是一个专业的测试用例生成器，擅长基于图像生成全面的测试用例。请先以标准 Markdown 格式生成测试用例，包含正确的表格格式，然后再生成结构化的 JSON 数据，以便于导出到 Excel。",
            model_client_stream=True,  # 启用流式输出
            # 移除 output_content_type 约束，让我们自己处理JSON解析
        )

        # 首先输出标题
        yield "# 正在生成测试用例...\n\n"

        # 初始化变量
        markdown_buffer = ""
        full_response = ""

        # 流式输出生成的测试用例
        async for event in agent.run_stream(task=multi_modal_message):
            if isinstance(event, ModelClientStreamingChunkEvent):
                # 返回生成的文本片段
                chunk_content = event.content
                yield chunk_content
                markdown_buffer += chunk_content
            elif isinstance(event, TaskResult):
                # 获取完整响应
                full_response = event.messages[-1].content if event.messages else ""

        # 在流式输出结束后，处理测试用例数据
        # 尝试从完整响应中提取JSON数据
        if full_response:
            logger.info("尝试从完整响应中提取测试用例JSON")
            test_cases_json = self._safe_parse_json_response(str(full_response))
            if test_cases_json:
                yield "\n\n<!-- TEST_CASES_JSON: " + json.dumps(test_cases_json, ensure_ascii=False) + " -->\n"
                logger.info(f"从完整响应中成功提取测试用例，数量: {len(test_cases_json)}")
            else:
                # 如果从完整响应中提取失败，尝试从markdown中提取
                logger.info("从完整响应提取失败，尝试从markdown中提取")
                test_cases_json = self._extract_test_cases_from_markdown(markdown_buffer)
                if test_cases_json:
                    yield "\n\n<!-- TEST_CASES_JSON: " + json.dumps(test_cases_json, ensure_ascii=False) + " -->\n"
                    logger.info(f"从markdown中成功提取测试用例，数量: {len(test_cases_json)}")
        else:
            # 如果没有完整响应，尝试从 Markdown 中提取测试用例
            logger.info("没有完整响应，尝试从markdown中提取测试用例")
            test_cases_json = self._extract_test_cases_from_markdown(markdown_buffer)
            if test_cases_json:
                yield "\n\n<!-- TEST_CASES_JSON: " + json.dumps(test_cases_json, ensure_ascii=False) + " -->\n"
                logger.info(f"从markdown中成功提取测试用例，数量: {len(test_cases_json)}")


    async def generate_test_cases_stream_with_record(
        self,
        image_url: str,
        context: str,
        requirements: str,
        generation_record
    ) -> AsyncGenerator[str, None]:
        """
        生成测试用例并更新数据库记录
        """
        test_cases_json = []
        markdown_buffer = ""
        json_buffer = ""  # 用于累积JSON内容
        json_start_found = False
        json_end_found = False

        # 在函数开头导入user_service，避免作用域问题
        from apps.agent_tools.services.record_service import user_service

        try:
            async for chunk in self.generate_test_cases_stream(image_url, context, requirements):
                markdown_buffer += chunk

                # 检查JSON开始标记
                if "<!-- TEST_CASES_JSON:" in chunk and not json_start_found:
                    json_start_found = True
                    start_idx = chunk.find("<!-- TEST_CASES_JSON:") + len("<!-- TEST_CASES_JSON:")
                    json_buffer = chunk[start_idx:]
                    logger.info("发现JSON开始标记，开始累积JSON内容")
                elif json_start_found and not json_end_found:
                    # 继续累积JSON内容
                    json_buffer += chunk

                # 检查JSON结束标记
                if json_start_found and "-->" in json_buffer and not json_end_found:
                    json_end_found = True
                    end_idx = json_buffer.find("-->")
                    json_str = json_buffer[:end_idx].strip()

                    logger.info(f"发现JSON结束标记，开始解析JSON内容，长度: {len(json_str)}")
                    logger.debug(f"原始JSON内容前200字符: {json_str[:200]}...")

                    try:
                        # 清理JSON字符串
                        cleaned_json = self._clean_json_string(json_str)
                        logger.debug(f"清理后JSON内容前200字符: {cleaned_json[:200]}...")

                        # 尝试解析JSON
                        test_cases_json = json.loads(cleaned_json)
                        logger.info(f"成功解析测试用例JSON，数量: {len(test_cases_json)}")

                    except json.JSONDecodeError as e:
                        logger.error(f"JSON格式错误: {str(e)}")
                        logger.error(f"错误位置: line {e.lineno}, column {e.colno}")
                        logger.error(f"问题JSON片段: {json_str[:500]}...")

                        # 尝试使用安全解析方法
                        test_cases_json = self._safe_parse_json_response(json_str)
                        if test_cases_json:
                            logger.info(f"通过安全解析成功恢复，数量: {len(test_cases_json)}")
                        else:
                            logger.error("安全解析也失败，将尝试从markdown中提取")

                    except Exception as e:
                        logger.error(f"解析测试用例JSON失败: {str(e)}")
                        logger.error(f"异常类型: {type(e).__name__}")

                yield chunk

            # 如果没有通过JSON标记提取到数据，尝试从markdown中提取
            if not test_cases_json and markdown_buffer:
                logger.info("尝试从markdown内容中提取测试用例")
                test_cases_json = self._extract_test_cases_from_markdown(markdown_buffer)
                if test_cases_json:
                    logger.info(f"从markdown中成功提取测试用例，数量: {len(test_cases_json)}")

            # 更新生成记录
            if test_cases_json:
                try:
                    # 验证测试用例数据格式
                    validated_test_cases = self._validate_test_cases_data(test_cases_json)

                    await user_service.update_generation_record(
                        generation_record,
                        test_cases_json=validated_test_cases,
                        markdown_content=markdown_buffer,
                        status="ai_completed"  # AI生成完成，等待文件生成
                    )
                    logger.info(f"AI生成完成，记录已更新: {generation_record.id}")

                    # 在响应末尾添加记录ID信息
                    yield f"\n\n<!-- RECORD_ID: {generation_record.id} -->\n"

                except Exception as e:
                    logger.error(f"更新生成记录失败: {str(e)}")
                    # 更新状态为失败
                    try:
                        await user_service.update_generation_record(
                            generation_record,
                            status="failed",
                            error_message=f"更新记录失败: {str(e)}"
                        )
                    except Exception as inner_e:
                        logger.error(f"更新失败状态也失败: {str(inner_e)}")
            else:
                # 没有生成测试用例，标记为失败
                logger.warning(f"未能生成测试用例: {generation_record.id}")
                try:
                    await user_service.update_generation_record(
                        generation_record,
                        markdown_content=markdown_buffer,
                        status="failed",
                        error_message="未能从AI响应中提取有效的测试用例数据"
                    )
                except Exception as e:
                    logger.error(f"更新失败状态失败: {str(e)}")

        except Exception as e:
            logger.error(f"生成测试用例过程中出错: {str(e)}")
            logger.error(f"异常类型: {type(e).__name__}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")

            # 更新状态为失败
            try:
                await user_service.update_generation_record(
                    generation_record,
                    status="failed",
                    error_message=f"生成过程异常: {str(e)}"
                )
            except Exception as inner_e:
                logger.error(f"更新失败状态失败: {str(inner_e)}")
            raise e

    def _test_case_to_dict(self, test_case: TestCase) -> Dict[str, Any]:
        """
        将 TestCase 对象转换为字典
        """
        return {
            'id': test_case.id,
            'title': test_case.title,
            'description': test_case.description,
            'preconditions': test_case.preconditions,
            'priority': test_case.priority,
            'steps': [
                {
                    'step_number': step.step_number,
                    'description': step.description,
                    'expected_result': step.expected_result
                } for step in test_case.steps
            ]
        }

    def _generate_markdown_from_test_cases(self, test_cases: List[TestCase]) -> str:
        """
        从测试用例列表生成 Markdown 格式的文本
        """
        markdown = "# 生成的测试用例\n\n"

        for tc in test_cases:
            markdown += f"## {tc.id}: {tc.title}\n\n"

            if tc.priority:
                markdown += f"**优先级:** {tc.priority}\n\n"

            markdown += f"**描述:** {tc.description}\n\n"

            if tc.preconditions:
                markdown += f"**前置条件:** {tc.preconditions}\n\n"

            # 确保表格格式正确，包含表头和分隔行
            markdown += "### 测试步骤\n\n"
            # 添加表格头
            markdown += "| # | 步骤描述 | 预期结果 |\n"
            # 添加分隔行（这一行很重要）
            markdown += "| --- | --- | --- |\n"

            # 添加每一行数据
            for step in tc.steps:
                markdown += f"| {step.step_number} | {step.description} | {step.expected_result} |\n"

            markdown += "\n\n"

        return markdown

    def _extract_test_cases_from_markdown(self, markdown_text: str) -> List[Dict[str, Any]]:
        """
        从 Markdown 文本中提取测试用例信息

        参数:
            markdown_text: Markdown 格式的测试用例文本

        返回:
            测试用例列表
        """
        test_cases = []
        lines = markdown_text.split('\n')

        current_test_case = None
        current_steps = []
        in_table = False
        table_headers = []

        for line in lines:
            # 检测新测试用例的开始
            if line.startswith('## '):
                # 如果有当前测试用例，将其添加到列表中
                if current_test_case is not None and current_steps:
                    current_test_case['steps'] = current_steps
                    test_cases.append(current_test_case)

                # 初始化新的测试用例
                title_parts = line[3:].strip().split(': ', 1)
                if len(title_parts) > 1:
                    test_id = title_parts[0].strip()
                    title = title_parts[1].strip()
                else:
                    test_id = f"TC-{len(test_cases) + 1}"
                    title = line[3:].strip()

                current_test_case = {
                    'id': test_id,
                    'title': title,
                    'description': '',
                    'preconditions': None,
                    'priority': None
                }
                current_steps = []
                in_table = False

            # 提取优先级
            elif line.startswith('**优先级:**') and current_test_case:
                current_test_case['priority'] = line.replace('**优先级:**', '').strip()

            # 提取描述
            elif line.startswith('**描述:**') and current_test_case:
                current_test_case['description'] = line.replace('**描述:**', '').strip()

            # 提取前置条件
            elif line.startswith('**前置条件:**') and current_test_case:
                current_test_case['preconditions'] = line.replace('**前置条件:**', '').strip()

            # 检测表格头
            elif '| --- | --- | --- |' in line:
                in_table = True
                # 获取前一行的表头
                for i, prev_line in enumerate(reversed(lines[:lines.index(line)])):
                    if '|' in prev_line:
                        table_headers = [h.strip() for h in prev_line.split('|')[1:-1]]
                        break

            # 提取测试步骤
            elif in_table and '|' in line and '---' not in line and len(line.split('|')) > 3:
                cells = [cell.strip() for cell in line.split('|')[1:-1]]
                if len(cells) >= 3:
                    try:
                        step_number = int(cells[0])
                        description = cells[1]
                        expected_result = cells[2]

                        current_steps.append({
                            'step_number': step_number,
                            'description': description,
                            'expected_result': expected_result
                        })
                    except (ValueError, IndexError):
                        pass  # 忽略无法解析的行

        # 添加最后一个测试用例
        if current_test_case is not None and current_steps:
            current_test_case['steps'] = current_steps
            test_cases.append(current_test_case)

        return test_cases

    def _validate_test_cases_data(self, test_cases_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        验证和清理测试用例数据，确保格式正确

        参数:
            test_cases_data: 原始测试用例数据

        返回:
            验证后的测试用例数据
        """
        validated_cases = []

        for i, test_case in enumerate(test_cases_data):
            try:
                # 确保必需字段存在
                validated_case = {
                    'id': test_case.get('id', f'TC-{i+1}'),
                    'title': str(test_case.get('title', '')).strip(),
                    'description': str(test_case.get('description', '')).strip(),
                    'preconditions': str(test_case.get('preconditions') or '').strip() or '无特殊前置条件',
                    'priority': test_case.get('priority', 'Medium')
                }

                # 验证和清理测试步骤 - 转换为前端期望的格式
                steps = test_case.get('steps', []) or test_case.get('test_steps', [])
                validated_steps = []

                logger.debug(f"处理测试用例 {test_case.get('id', 'Unknown')} 的步骤，原始步骤数: {len(steps)}")

                if isinstance(steps, list) and len(steps) > 0:
                    for j, step in enumerate(steps):
                        if isinstance(step, dict):
                            # 支持多种输入格式，统一转换为前端期望的格式
                            step_description = (
                                step.get('step') or
                                step.get('description') or
                                step.get('action') or
                                ''
                            ).strip()

                            expected_result = (
                                step.get('expected_result') or
                                step.get('expected') or
                                ''
                            ).strip()

                            if step_description:  # 只添加有描述的步骤
                                validated_step = {
                                    'step': step_description,
                                    'expected_result': expected_result
                                }
                                validated_steps.append(validated_step)
                                logger.debug(f"添加步骤 {j+1}: {step_description[:50]}...")
                else:
                    # 如果没有步骤，尝试生成默认步骤
                    logger.warning(f"测试用例 {test_case.get('id', 'Unknown')} 没有步骤信息，尝试生成默认步骤")
                    default_steps = self._generate_default_steps(test_case)
                    validated_steps.extend(default_steps)

                validated_case['test_steps'] = validated_steps  # 使用 test_steps 而不是 steps
                logger.debug(f"测试用例 {test_case.get('id', 'Unknown')} 最终步骤数: {len(validated_steps)}")

                # 只添加有效的测试用例（至少有标题）
                if validated_case['title']:
                    validated_cases.append(validated_case)

            except Exception as e:
                logger.warning(f"验证测试用例 {i} 时出错: {str(e)}")
                continue

        logger.info(f"验证完成，有效测试用例数量: {len(validated_cases)}")
        return validated_cases

    def _generate_default_steps(self, test_case: Dict[str, Any]) -> List[Dict[str, str]]:
        """
        为没有步骤的测试用例生成默认步骤

        参数:
            test_case: 测试用例数据

        返回:
            默认步骤列表
        """
        title = test_case.get('title', '')
        description = test_case.get('description', '')

        # 根据测试用例标题和描述生成基本步骤
        default_steps = []

        if '新增' in title or '添加' in title:
            default_steps = [
                {
                    'step': '打开相关功能页面',
                    'expected_result': '页面正常加载显示'
                },
                {
                    'step': '点击新增/添加按钮',
                    'expected_result': '弹出新增对话框或跳转到新增页面'
                },
                {
                    'step': '填写必要信息',
                    'expected_result': '信息输入正常'
                },
                {
                    'step': '点击保存/确认按钮',
                    'expected_result': '操作成功，显示成功提示'
                }
            ]
        elif '验证' in title and ('为空' in title or '空' in title):
            default_steps = [
                {
                    'step': '打开相关功能页面',
                    'expected_result': '页面正常加载显示'
                },
                {
                    'step': '不填写必要信息，直接提交',
                    'expected_result': '系统显示相应的错误提示信息'
                }
            ]
        elif '删除' in title:
            default_steps = [
                {
                    'step': '选择要删除的项目',
                    'expected_result': '项目被选中'
                },
                {
                    'step': '点击删除按钮',
                    'expected_result': '弹出确认删除对话框'
                },
                {
                    'step': '确认删除操作',
                    'expected_result': '删除成功，项目从列表中移除'
                }
            ]
        else:
            # 通用默认步骤
            default_steps = [
                {
                    'step': '执行相关操作',
                    'expected_result': '操作执行成功'
                },
                {
                    'step': '验证结果',
                    'expected_result': '结果符合预期'
                }
            ]

        logger.info(f"为测试用例 {test_case.get('id', 'Unknown')} 生成了 {len(default_steps)} 个默认步骤")
        return default_steps

    def _clean_json_string(self, json_str: str) -> str:
        """
        清理JSON字符串，移除可能导致解析失败的字符和内容

        参数:
            json_str: 原始JSON字符串

        返回:
            清理后的JSON字符串
        """
        import re

        # 移除开头和结尾的空白字符
        cleaned = json_str.strip()

        # 特殊处理：移除尾随的中文文本和标点符号
        # 这个正则表达式匹配JSON结束后的任何中文文本
        chinese_text_pattern = r'(\}|\])\s*[。，；：！？、》】）]*\s*[\u4e00-\u9fff\s]*[。，；：！？、》】）|]*\s*$'
        match = re.search(chinese_text_pattern, cleaned)
        if match:
            # 保留到JSON结束符号
            end_pos = match.start() + 1  # +1 to include the } or ]
            cleaned = cleaned[:end_pos]
            logger.debug(f"移除尾随中文文本，清理后长度: {len(cleaned)}")

        # 移除常见的尾随字符
        trailing_chars = ['|', '`', '~', '#', '*', '_', '-', '+', '=']
        for char in trailing_chars:
            while cleaned.endswith(char):
                cleaned = cleaned[:-1].strip()

        # 移除可能的中文句号和其他中文标点
        chinese_punctuation = ['。', '，', '；', '：', '！', '？', '、', '》', '】', '）']
        for punct in chinese_punctuation:
            while cleaned.endswith(punct):
                cleaned = cleaned[:-1].strip()

        # 查找最后一个有效的JSON结束字符
        last_brace = cleaned.rfind('}')
        last_bracket = cleaned.rfind(']')

        if last_brace > last_bracket and last_brace != -1:
            # 以}结尾的JSON对象
            cleaned = cleaned[:last_brace + 1]
        elif last_bracket > last_brace and last_bracket != -1:
            # 以]结尾的JSON数组
            cleaned = cleaned[:last_bracket + 1]

        # 移除JSON中可能的控制字符
        cleaned = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', cleaned)

        # 修复常见的JSON格式问题
        try:
            # 规范化空白字符
            cleaned = re.sub(r'\s+', ' ', cleaned)
            # 移除对象和数组末尾的多余逗号
            cleaned = re.sub(r',\s*}', '}', cleaned)
            cleaned = re.sub(r',\s*]', ']', cleaned)
            # 确保键值对之间有正确的逗号
            cleaned = re.sub(r'"\s*"', '", "', cleaned)
            # 修复可能的引号问题
            cleaned = re.sub(r'"\s*:', '":', cleaned)
            cleaned = re.sub(r':\s*"', ': "', cleaned)
        except Exception as e:
            logger.warning(f"JSON格式修复时出错: {str(e)}")

        # 确保JSON格式正确（基本验证）
        if not (cleaned.startswith('{') or cleaned.startswith('[')):
            # 尝试找到第一个有效的JSON开始字符
            first_brace = cleaned.find('{')
            first_bracket = cleaned.find('[')

            if first_brace != -1 and (first_bracket == -1 or first_brace < first_bracket):
                cleaned = cleaned[first_brace:]
            elif first_bracket != -1:
                cleaned = cleaned[first_bracket:]

        logger.debug(f"JSON清理完成，原长度: {len(json_str)}, 清理后长度: {len(cleaned)}")
        return cleaned

    def _repair_json_structure(self, json_str: str) -> str:
        """
        修复JSON结构问题

        参数:
            json_str: 需要修复的JSON字符串

        返回:
            修复后的JSON字符串
        """
        import re

        try:
            # 首先尝试直接解析，如果成功就不需要修复
            json.loads(json_str)
            return json_str
        except json.JSONDecodeError:
            pass

        # 开始修复过程
        repaired = json_str

        # 1. 修复引号问题 - 将中文引号替换为英文引号
        repaired = re.sub(r'"([^"]*)"', r'"\1"', repaired)
        repaired = re.sub(r'"([^"]*)"', r'"\1"', repaired)

        # 2. 修复键值对格式
        # 确保键名被正确引用
        repaired = re.sub(r'(\w+):', r'"\1":', repaired)
        # 修复已经有引号的情况
        repaired = re.sub(r'""(\w+)"":', r'"\1":', repaired)

        # 3. 修复逗号问题
        # 移除末尾多余的逗号
        repaired = re.sub(r',(\s*[}\]])', r'\1', repaired)

        # 4. 修复空白字符
        repaired = re.sub(r'\s+', ' ', repaired)
        repaired = re.sub(r'{\s+', '{', repaired)
        repaired = re.sub(r'\s+}', '}', repaired)
        repaired = re.sub(r'\[\s+', '[', repaired)
        repaired = re.sub(r'\s+\]', ']', repaired)

        # 5. 确保冒号和逗号后有空格
        repaired = re.sub(r':(?!\s)', ': ', repaired)
        repaired = re.sub(r',(?!\s)', ', ', repaired)

        logger.debug(f"JSON结构修复完成")
        return repaired

    def _safe_parse_json_response(self, response_text: str) -> List[Dict[str, Any]]:
        """
        安全地解析JSON响应，处理可能的格式问题

        参数:
            response_text: 响应文本

        返回:
            测试用例列表或None
        """
        try:
            logger.debug(f"开始解析响应文本，长度: {len(response_text)}")
            logger.debug(f"响应文本前500字符: {response_text[:500]}...")

            # 首先尝试查找明确的JSON块标记
            import re

            # 查找被```json```包围的JSON块
            json_block_pattern = r'```json\s*(.*?)\s*```'
            json_matches = re.findall(json_block_pattern, response_text, re.DOTALL)

            for match in json_matches:
                try:
                    cleaned_json = self._clean_json_string(match)
                    parsed = json.loads(cleaned_json)
                    if isinstance(parsed, dict) and 'test_cases' in parsed:
                        logger.info(f"从JSON代码块中成功解析，数量: {len(parsed['test_cases'])}")
                        return parsed['test_cases']
                    elif isinstance(parsed, list):
                        logger.info(f"从JSON代码块中成功解析数组，数量: {len(parsed)}")
                        return parsed
                except json.JSONDecodeError as e:
                    logger.debug(f"JSON代码块解析失败: {str(e)}")
                    continue

            # 查找独立的JSON对象（以{开头，以}结尾）
            json_object_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
            json_objects = re.findall(json_object_pattern, response_text, re.DOTALL)

            for json_obj in json_objects:
                try:
                    cleaned_json = self._clean_json_string(json_obj)
                    parsed = json.loads(cleaned_json)
                    if isinstance(parsed, dict) and 'test_cases' in parsed:
                        logger.info(f"从JSON对象中成功解析，数量: {len(parsed['test_cases'])}")
                        return parsed['test_cases']
                except json.JSONDecodeError as e:
                    logger.debug(f"JSON对象解析失败: {str(e)}")
                    continue

            # 尝试直接清理整个响应文本
            cleaned_text = self._clean_json_string(response_text)
            if cleaned_text.strip().startswith('{') or cleaned_text.strip().startswith('['):
                try:
                    parsed = json.loads(cleaned_text)
                    if isinstance(parsed, dict) and 'test_cases' in parsed:
                        logger.info(f"从清理后的完整文本中成功解析，数量: {len(parsed['test_cases'])}")
                        return parsed['test_cases']
                    elif isinstance(parsed, list):
                        logger.info(f"从清理后的完整文本中成功解析数组，数量: {len(parsed)}")
                        return parsed
                except json.JSONDecodeError as e:
                    logger.debug(f"完整文本解析失败: {str(e)}")

                    # 尝试修复JSON结构
                    try:
                        logger.info("尝试修复JSON结构...")
                        repaired_json = self._repair_json_structure(cleaned_text)
                        parsed = json.loads(repaired_json)
                        if isinstance(parsed, dict) and 'test_cases' in parsed:
                            logger.info(f"JSON结构修复成功，数量: {len(parsed['test_cases'])}")
                            return parsed['test_cases']
                        elif isinstance(parsed, list):
                            logger.info(f"JSON结构修复成功，数组数量: {len(parsed)}")
                            return parsed
                    except json.JSONDecodeError as e2:
                        logger.debug(f"JSON结构修复也失败: {str(e2)}")

            logger.warning("无法从响应中提取有效的JSON数据")
            return None

        except Exception as e:
            logger.error(f"解析JSON响应失败: {str(e)}")
            import traceback
            logger.error(f"解析异常堆栈: {traceback.format_exc()}")
            return None


ai_service = AIService()
