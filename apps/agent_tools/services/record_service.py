#!/anaconda3/envs/FEALPy/bin python3.8
# -*- coding: utf-8 -*-
# @Author: lijing.wang
# @E-mail: <EMAIL>
# @Time: 6月 27, 2025

"""
用户服务
"""
import uuid
from typing import Optional
from fastapi import Request
from apps.agent_tools.models import GenerationRecord
from apps.users.models import UsersModel
import logging

logger = logging.getLogger(__name__)


class UserService:
    """用户服务类"""
    
    async def get_authenticated_user(self, request: Request) -> UsersModel:
        """
        获取已认证用户，不支持匿名用户

        Args:
            request: FastAPI请求对象

        Returns:
            UsersModel: 已认证的用户对象

        Raises:
            HTTPException: 当用户未认证时抛出401错误
        """
        from fastapi import HTTPException, status
        import jwt
        import json

        try:
            user_id = request.state.user_id

            # 如果没有，尝试从Authorization头部解析JWT token
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="用户ID不存在或已被删除")

            # 查找用户
            user = await UsersModel.filter(id=user_id).first()
            if not user:
                logger.warning(f"用户ID {user_id} 不存在")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="用户不存在或已被删除"
                )

            logger.info(f"用户 {user.username}({user.id}) 通过认证")
            return user

        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"获取认证用户失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="用户认证服务异常"
            )
    
    async def get_user_recent_records(self, user: UsersModel, limit: int = 10) -> list:
        """
        获取用户最近的生成记录
        
        Args:
            user: 用户对象
            limit: 返回记录数量限制
            
        Returns:
            list: 生成记录列表
        """
        try:
            records = await GenerationRecord.filter(
                user=user,
                status__in=["completed", "ai_completed"]  # 包含AI完成和全部完成的记录
            ).order_by("-created_at").limit(limit)
            
            # 转换为字典格式
            result = []
            for record in records:
                result.append({
                    "id": str(record.id),
                    "context": record.context[:100] + "..." if len(record.context) > 100 else record.context,
                    "requirements": record.requirements[:100] + "..." if len(record.requirements) > 100 else record.requirements,
                    "test_cases_count": len(record.test_cases_json) if record.test_cases_json else 0,
                    "created_at": record.created_at.isoformat(),
                    "excel_url": record.excel_url,
                    "xmind_url": record.xmind_url
                })
            
            return result
            
        except Exception as e:
            logger.error(f"获取用户记录失败: {str(e)}")
            return []
    
    async def create_generation_record(
        self,
        user: UsersModel,
        image_url: str,
        context: str,
        requirements: str
    ) -> GenerationRecord:
        """
        创建生成记录
        
        Args:
            user: 用户对象
            image_url: 图片URL
            context: 上下文
            requirements: 需求
            
        Returns:
            GenerationRecord: 生成记录对象
        """
        try:
            record = await GenerationRecord.create(
                user=user,
                image_url=image_url,
                context=context,
                requirements=requirements,
                status="processing"
            )
            logger.info(f"创建生成记录: {record.id}")
            return record
            
        except Exception as e:
            logger.error(f"创建生成记录失败: {str(e)}")
            raise e
    
    async def update_generation_record(
        self,
        record: GenerationRecord,
        test_cases_json: Optional[list] = None,
        markdown_content: Optional[str] = None,
        excel_url: Optional[str] = None,
        excel_key: Optional[str] = None,
        xmind_url: Optional[str] = None,
        xmind_key: Optional[str] = None,
        status: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> GenerationRecord:
        """
        更新生成记录

        Args:
            record: 生成记录对象
            test_cases_json: 测试用例JSON数据
            markdown_content: Markdown内容
            excel_url: Excel文件URL
            excel_key: Excel文件OSS key
            xmind_url: XMind文件URL
            xmind_key: XMind文件OSS key
            status: 状态
            error_message: 错误信息

        Returns:
            GenerationRecord: 更新后的生成记录对象
        """
        try:
            if test_cases_json is not None:
                record.test_cases_json = test_cases_json
            if markdown_content is not None:
                record.markdown_content = markdown_content
            if excel_url is not None:
                record.excel_url = excel_url
            if excel_key is not None:
                record.excel_key = excel_key
            if xmind_url is not None:
                record.xmind_url = xmind_url
            if xmind_key is not None:
                record.xmind_key = xmind_key
            if status is not None:
                record.status = status
            if error_message is not None:
                record.error_message = error_message

            if status == "completed":
                from datetime import datetime
                record.completed_at = datetime.now()

            await record.save()
            logger.info(f"更新生成记录: {record.id}")
            return record

        except Exception as e:
            logger.error(f"更新生成记录失败: {str(e)}")
            raise e


# 创建全局用户服务实例
user_service = UserService()
