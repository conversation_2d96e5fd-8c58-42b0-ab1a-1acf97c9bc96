#!/anaconda3/envs/FEALPy/bin python3.8
# -*- coding: utf-8 -*-
# @Author: lijing.wang
# @E-mail: <EMAIL>
# @Time: 6月 27, 2025

"""
XMind文件生成服务
"""
import os
import uuid
import tempfile
from datetime import datetime
from typing import List, Dict, Any, Union
import logging
import xmind
from xmind.core.topic import TopicElement

logger = logging.getLogger(__name__)


class XMindService:
    """XMind文件生成服务类"""

    def __init__(self):
        """初始化XMind服务"""
        pass

    def generate_xmind(self, test_cases: List[Union[Dict[str, Any], Any]], filename_prefix: str = "test_cases") -> bytes:
        """
        从测试用例生成XMind文件

        Args:
            test_cases: 测试用例列表
            filename_prefix: 文件名前缀

        Returns:
            bytes: XMind文件内容
        """
        try:
            logger.info(f"开始生成XMind，接收到的数据类型: {type(test_cases)}")
            logger.info(f"测试用例数量: {len(test_cases) if test_cases else 0}")

            # 检查输入数据
            if not test_cases:
                logger.warning("测试用例列表为空，生成空的XMind文件")
                test_cases = []

            # 使用简化的方法创建XMind文件
            import zipfile
            import xml.etree.ElementTree as ET
            from xml.dom import minidom

            # 创建XMind内容的XML结构
            content_xml = self._create_xmind_content(test_cases)

            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.xmind', delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # 创建XMind文件（实际上是一个zip文件）
                with zipfile.ZipFile(temp_path, 'w', zipfile.ZIP_DEFLATED) as xmind_zip:
                    # 添加content.xml
                    xmind_zip.writestr('content.xml', content_xml)

                    # 添加META-INF/manifest.xml
                    manifest_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<manifest xmlns="urn:xmind:xmap:xmlns:manifest:1.0">
    <file-entry full-path="content.xml" media-type="text/xml"/>
</manifest>'''
                    xmind_zip.writestr('META-INF/manifest.xml', manifest_xml)

                # 读取文件内容
                with open(temp_path, 'rb') as f:
                    xmind_content = f.read()

                logger.info(f"XMind文件生成成功，大小: {len(xmind_content)} bytes")
                return xmind_content

            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)

        except Exception as e:
            logger.error(f"XMind文件生成失败: {str(e)}")
            raise e

    def _create_xmind_content(self, test_cases: List[Dict[str, Any]]) -> str:
        """
        创建XMind内容的XML结构

        Args:
            test_cases: 测试用例列表

        Returns:
            str: XMind内容的XML字符串
        """
        import xml.etree.ElementTree as ET
        from xml.dom import minidom

        # 创建根元素
        workbook = ET.Element('xmap-content', {
            'xmlns': 'urn:xmind:xmap:xmlns:content:2.0',
            'xmlns:fo': 'http://www.w3.org/1999/XSL/Format',
            'version': '2.0'
        })

        # 创建工作表
        sheet = ET.SubElement(workbook, 'sheet', {
            'id': 'sheet1',
            'theme': 'theme1'
        })

        # 创建主题
        topic = ET.SubElement(sheet, 'topic', {
            'id': 'root',
            'structure-class': 'org.xmind.ui.map.unbalanced'
        })

        # 设置根主题标题
        title = ET.SubElement(topic, 'title')
        title.text = '测试用例'

        # 创建子主题容器
        children = ET.SubElement(topic, 'children')
        topics = ET.SubElement(children, 'topics', {'type': 'attached'})

        # 为每个测试用例创建子主题
        logger.info(f"开始创建 {len(test_cases)} 个测试用例的XMind主题")
        for i, test_case in enumerate(test_cases):
            tc_data = self._normalize_test_case(test_case, i)
            steps_count = len(tc_data.get('test_steps', [])) or len(tc_data.get('steps', []))
            logger.debug(f"创建XMind主题 {i+1}: {tc_data.get('id', 'Unknown')}, 步骤数: {steps_count}")
            self._create_xmind_topic(topics, tc_data, f'topic_{i+1}')

        # 格式化XML
        rough_string = ET.tostring(workbook, encoding='unicode')
        reparsed = minidom.parseString(rough_string)
        return reparsed.toprettyxml(indent="  ", encoding=None)

    def _create_xmind_topic(self, parent, tc_data: Dict[str, Any], topic_id: str):
        """
        创建XMind主题节点

        Args:
            parent: 父节点
            tc_data: 测试用例数据
            topic_id: 主题ID
        """
        import xml.etree.ElementTree as ET

        # 创建测试用例主题
        topic = ET.SubElement(parent, 'topic', {'id': topic_id})

        # 设置标题
        title = ET.SubElement(topic, 'title')
        title.text = f"{tc_data['id']}: {tc_data['title']}"

        # 创建子主题
        children = ET.SubElement(topic, 'children')
        topics = ET.SubElement(children, 'topics', {'type': 'attached'})

        # 添加描述
        if tc_data.get('description'):
            desc_topic = ET.SubElement(topics, 'topic', {'id': f'{topic_id}_desc'})
            desc_title = ET.SubElement(desc_topic, 'title')
            desc_title.text = f"描述: {tc_data['description']}"

        # 添加前置条件
        if tc_data.get('preconditions'):
            pre_topic = ET.SubElement(topics, 'topic', {'id': f'{topic_id}_pre'})
            pre_title = ET.SubElement(pre_topic, 'title')
            pre_title.text = f"前置条件: {tc_data['preconditions']}"

        # 添加测试步骤 - 支持新的 test_steps 格式
        steps = tc_data.get('test_steps', []) or tc_data.get('steps', [])
        if steps and len(steps) > 0:
            steps_topic = ET.SubElement(topics, 'topic', {'id': f'{topic_id}_steps'})
            steps_title = ET.SubElement(steps_topic, 'title')
            steps_title.text = "🔧 测试步骤"

            steps_children = ET.SubElement(steps_topic, 'children')
            steps_topics = ET.SubElement(steps_children, 'topics', {'type': 'attached'})

            for j, step in enumerate(steps):
                # 获取步骤信息 - 支持多种字段名格式
                step_num = step.get('step_number', j+1)

                # 获取操作描述 - 支持新的格式和多种字段名
                action = (step.get('step') or
                         step.get('description') or
                         step.get('action') or
                         step.get('操作') or
                         step.get('测试方法') or
                         step.get('操作步骤') or '')

                # 获取预期结果 - 支持多种字段名
                expected = (step.get('expected_result') or
                           step.get('expected') or
                           step.get('预期结果') or
                           step.get('预期') or
                           step.get('期望结果') or '')

                # 创建步骤主题
                step_topic = ET.SubElement(steps_topics, 'topic', {'id': f'{topic_id}_step_{j+1}'})
                step_title = ET.SubElement(step_topic, 'title')

                # 确保操作描述不为空
                if action:
                    step_title.text = f"步骤 {step_num}: {action}"
                else:
                    step_title.text = f"步骤 {step_num}: [操作描述缺失]"

                # 如果有预期结果，添加子主题
                if expected:
                    step_children = ET.SubElement(step_topic, 'children')
                    step_sub_topics = ET.SubElement(step_children, 'topics', {'type': 'attached'})
                    expected_topic = ET.SubElement(step_sub_topics, 'topic', {'id': f'{topic_id}_step_{j+1}_exp'})
                    expected_title = ET.SubElement(expected_topic, 'title')
                    expected_title.text = f"✅ 预期: {expected}"

        # 添加预期结果
        if tc_data.get('expected_result'):
            result_topic = ET.SubElement(topics, 'topic', {'id': f'{topic_id}_result'})
            result_title = ET.SubElement(result_topic, 'title')
            result_title.text = f"预期结果: {tc_data['expected_result']}"

        # 添加优先级和类型
        info_topic = ET.SubElement(topics, 'topic', {'id': f'{topic_id}_info'})
        info_title = ET.SubElement(info_topic, 'title')
        info_title.text = f"优先级: {tc_data.get('priority', '中')} | 类型: {tc_data.get('type', '功能测试')}"


    def _normalize_test_case(self, test_case: Union[Dict[str, Any], Any], index: int) -> Dict[str, Any]:
        """
        标准化测试用例数据

        Args:
            test_case: 测试用例数据
            index: 索引

        Returns:
            Dict[str, Any]: 标准化的测试用例数据
        """
        if isinstance(test_case, dict):
            return test_case
        else:
            # 处理对象类型的测试用例，转换为新格式
            steps = getattr(test_case, 'test_steps', []) or getattr(test_case, 'steps', [])
            return {
                'id': getattr(test_case, 'id', f'TC-{index+1}'),
                'title': getattr(test_case, 'title', ''),
                'description': getattr(test_case, 'description', ''),
                'priority': getattr(test_case, 'priority', ''),
                'preconditions': getattr(test_case, 'preconditions', ''),
                'test_steps': [
                    {
                        'step': (
                            getattr(step, 'step', '') or
                            getattr(step, 'description', '') or
                            getattr(step, 'action', '')
                        ),
                        'expected_result': getattr(step, 'expected_result', '')
                    } for step in steps
                ]
            }

    def _create_test_case_topic(self, parent_topic, tc_data: Dict[str, Any]):
        """
        创建测试用例主题

        Args:
            parent_topic: 父主题
            tc_data: 测试用例数据
        """
        # 创建测试用例主题
        tc_topic = parent_topic.addSubTopic()
        tc_topic.setTitle(f"{tc_data.get('id', '')}: {tc_data.get('title', '')}")

        # 添加基本信息
        if tc_data.get('priority'):
            priority_topic = tc_topic.addSubTopic()
            priority_topic.setTitle(f"优先级: {tc_data['priority']}")

        if tc_data.get('description'):
            desc_topic = tc_topic.addSubTopic()
            desc_topic.setTitle(f"描述: {tc_data['description']}")

        if tc_data.get('preconditions'):
            pre_topic = tc_topic.addSubTopic()
            pre_topic.setTitle(f"前置条件: {tc_data['preconditions']}")

        # 添加测试步骤 - 支持新的 test_steps 格式
        steps = tc_data.get('test_steps', []) or tc_data.get('steps', [])
        if steps:
            steps_topic = tc_topic.addSubTopic()
            steps_topic.setTitle("测试步骤")

            for i, step in enumerate(steps):
                # 支持新的格式和旧的格式
                step_description = (
                    step.get('step') or
                    step.get('description') or
                    step.get('action') or
                    ''
                )
                step_number = step.get('step_number', i + 1)

                step_topic = steps_topic.addSubTopic()
                step_topic.setTitle(f"步骤 {step_number}: {step_description}")

                # 添加预期结果
                if step.get('expected_result'):
                    result_topic = step_topic.addSubTopic()
                    result_topic.setTitle(f"预期结果: {step['expected_result']}")


# 创建全局XMind服务实例
xmind_service = XMindService()
