#!/anaconda3/envs/FEALPy/bin python3.8
# -*- coding: utf-8 -*-
# @Author: lijing.wang
# @E-mail: <EMAIL>
# @Time: 6月 27, 2025
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, BackgroundTasks, Request, Depends
from fastapi.responses import StreamingResponse, FileResponse, RedirectResponse
import asyncio
import logging

from .models import TestCaseSchema, TestCaseRequest, TestCaseResponse, GenerationRecord, FileUpload
from apps.users.models import UsersModel
from .services.ai_service import ai_service
from .services.excel_service import excel_service
from .services.xmind_service import xmind_service
from .services.oss_service_v2 import oss_service_v2
from .services.record_service import user_service

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api",
    tags=["图生用例"],
    responses={404: {"description": "Not found"}},
)

@router.post("/test-cases/generate",summary="上传图片并生成测试用例",tags=['图生用例'])
async def generate_test_cases(
    request: Request,
    background_tasks: BackgroundTasks,
    image: UploadFile = File(...),
    context: str = Form(...),
    requirements: str = Form(...)
):
    """
    从上传的图像、上下文和需求生成测试用例

    参数:
        image: 上传的流程图、思维导图或UI截图
        context: 测试用例生成的上下文信息
        requirements: 测试用例生成的需求

    返回:
        包含生成的测试用例的流式响应
    """
    generation_record = None
    try:
        # 获取已认证用户
        user = await user_service.get_authenticated_user(request)
        logger.info(f"用户 {user.id} 开始生成测试用例")

        # 验证文件类型
        if not image.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只支持图片文件")

        # 上传图片到OSS
        logger.info("正在上传图片到OSS...")
        oss_url, authorized_url, oss_key,image_url = oss_service_v2.upload_file(image, "uploads/images")
        logger.info(f"图片上传成功: {oss_url}")
        logger.info(f"授权访问URL: {authorized_url}")

        # 记录文件上传
        await FileUpload.create(
            user=user,
            original_filename=image.filename,
            file_size=image.size,
            file_type=image.content_type,
            oss_url=oss_url,  # 保存原始OSS URL到数据库
            oss_key=oss_key
        )

        # 创建生成记录（初始状态为processing）
        generation_record = await user_service.create_generation_record(
            user=user,
            image_url=authorized_url,  # 使用本地URL进行AI分析
            context=context,
            requirements=requirements
        )
        logger.info(f"生成记录创建成功: {generation_record.id}")

        # 在后台处理Excel和XMind文件生成（等待AI生成完成后）
        background_tasks.add_task(
            process_files_generation_in_background,
            generation_record.id
        )

        # 使用流式响应生成测试用例（同时更新记录）
        return StreamingResponse(
            ai_service.generate_test_cases_stream_with_record(
                image_url, context, requirements, generation_record
            ),
            media_type="text/markdown"
        )

    except Exception as e:
        logger.error(f"生成测试用例失败: {str(e)}")
        # 如果有生成记录，更新状态为失败
        if generation_record:
            try:
                await user_service.update_generation_record(
                    generation_record,
                    status="failed"
                )
            except:
                pass
        raise HTTPException(status_code=500, detail=f"生成测试用例失败: {str(e)}")


async def process_files_generation_in_background(record_id: str):
    """
    在后台处理Excel和XMind文件生成
    等待AI生成完成后再生成文件
    """
    max_wait_time = 300  # 最大等待5分钟
    check_interval = 5   # 每5秒检查一次
    waited_time = 0

    try:
        logger.info(f"开始后台文件生成任务: {record_id}")

        while waited_time < max_wait_time:
            # 重新获取记录，检查AI生成状态
            record = await GenerationRecord.get(id=record_id)

            if record.status == "ai_completed" and record.test_cases_json:
                logger.info(f"AI生成完成，开始生成文件: {record_id}")

                excel_url = None
                xmind_url = None
                xmind_key=None
                excel_key=None

                # 生成Excel文件
                try:
                    logger.info("开始生成Excel文件...")
                    logger.info(f"测试用例数据类型: {type(record.test_cases_json)}")
                    logger.info(f"测试用例数据长度: {len(record.test_cases_json) if record.test_cases_json else 0}")
                    if record.test_cases_json:
                        logger.info(f"第一个测试用例示例: {record.test_cases_json[0] if len(record.test_cases_json) > 0 else 'None'}")

                    excel_content = excel_service.generate_excel_bytes(record.test_cases_json)
                    excel_filename = f"testcases_{record.id}.xlsx"
                    excel_oss_url, excel_authorized_url, excel_key = oss_service_v2.upload_bytes(
                        excel_content, excel_filename, "generated/excel"
                    )
                    logger.info(f"Excel文件生成成功: {excel_oss_url}")
                    logger.info(f"Excel授权URL: {excel_authorized_url}")
                    excel_url = excel_authorized_url  # 使用授权URL
                except Exception as e:
                    logger.error(f"Excel文件生成失败: {str(e)}")
                    import traceback
                    logger.error(f"Excel生成错误详情: {traceback.format_exc()}")

                # 生成XMind文件
                try:
                    logger.info("开始生成XMind文件...")
                    logger.info(f"XMind测试用例数据类型: {type(record.test_cases_json)}")
                    logger.info(f"XMind测试用例数据长度: {len(record.test_cases_json) if record.test_cases_json else 0}")

                    xmind_content = xmind_service.generate_xmind(record.test_cases_json)
                    xmind_filename = f"testcases_{record.id}.xmind"
                    xmind_oss_url, xmind_authorized_url, xmind_key = oss_service_v2.upload_bytes(
                        xmind_content, xmind_filename, "generated/xmind"
                    )
                    logger.info(f"XMind文件生成成功: {xmind_oss_url}")
                    logger.info(f"XMind授权URL: {xmind_authorized_url}")
                    xmind_url = xmind_authorized_url  # 使用授权URL
                except Exception as e:
                    logger.error(f"XMind文件生成失败: {str(e)}")
                    import traceback
                    logger.error(f"XMind生成错误详情: {traceback.format_exc()}")

                # 更新记录 - 即使某个文件生成失败，也要更新状态
                try:
                    await user_service.update_generation_record(
                        record,
                        excel_url=excel_url,
                        excel_key=excel_key,
                        xmind_url=xmind_url,
                        xmind_key=xmind_key,
                        status="completed"
                    )
                    logger.info(f"后台文件生成完成: {record_id}, Excel: {'✓' if excel_url else '✗'}, XMind: {'✓' if xmind_url else '✗'}")
                except Exception as e:
                    logger.error(f"更新记录失败: {str(e)}")

                return

            elif record.status == "failed":
                logger.warning(f"AI生成失败，跳过文件生成: {record_id}")
                return

            # 等待一段时间后再检查
            await asyncio.sleep(check_interval)
            waited_time += check_interval

        # 超时处理
        logger.warning(f"等待AI生成超时: {record_id}")
        record = await GenerationRecord.get(id=record_id)
        if record.status == "processing":
            await user_service.update_generation_record(
                record,
                status="timeout"
            )

    except Exception as e:
        logger.error(f"后台文件生成失败: {str(e)}")
        try:
            record = await GenerationRecord.get(id=record_id)
            await user_service.update_generation_record(
                record,
                status="failed"
            )
        except:
            pass

@router.get("/test-cases/record/{record_id}",summary="获取生成记录详情",tags=['图生用例'])
async def get_generation_record(record_id: str, request: Request):
    """
    获取生成记录详情
    """
    try:
        user = await user_service.get_authenticated_user(request)
        record = await GenerationRecord.filter(id=record_id, user=user).first()

        if not record:
            raise HTTPException(status_code=404, detail="记录不存在")

        # 根据状态提供不同的信息
        status_info = {
            "processing": "正在生成测试用例...",
            "ai_completed": "测试用例生成完成，正在生成文件...",
            "completed": "全部完成",
            "failed": "生成失败",
            "timeout": "生成超时"
        }

        # 检查文件是否准备就绪
        excel_ready = record.excel_url is not None and record.excel_url.strip() != ""
        xmind_ready = record.xmind_url is not None and record.xmind_url.strip() != ""

        # 添加调试日志
        logger.info(f"记录 {record.id} 状态检查:")
        logger.info(f"  状态: {record.status}")
        logger.info(f"  Excel URL: {record.excel_url}")
        logger.info(f"  XMind URL: {record.xmind_url}")
        logger.info(f"  excel_ready: {excel_ready}")
        logger.info(f"  xmind_ready: {xmind_ready}")

        response_data = {
            "id": str(record.id),
            "status": record.status,
            "status_message": status_info.get(record.status, record.status),
            "context": record.context,
            "requirements": record.requirements,
            "test_cases": record.test_cases_json,
            "markdown_content": record.markdown_content,
            "excel_url": record.excel_url,
            "xmind_url": record.xmind_url,
            "created_at": record.created_at.isoformat(),
            "completed_at": record.completed_at.isoformat() if record.completed_at else None,
            "error_message": record.error_message,
            "files_ready": record.status == "completed",
            "excel_ready": excel_ready,
            "xmind_ready": xmind_ready
        }

        logger.info(f"返回响应: files_ready={response_data['files_ready']}, excel_ready={response_data['excel_ready']}, xmind_ready={response_data['xmind_ready']}")
        return response_data

    except Exception as e:
        logger.error(f"获取生成记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/test-cases/download/excel/{record_id}",summary="下载Excel文件",tags=['图生用例'])
async def download_excel(record_id: str, request: Request):
    """
    下载Excel文件
    """
    try:
        user = await user_service.get_authenticated_user(request)
        record = await GenerationRecord.filter(id=record_id, user=user).first()

        if not record or not record.excel_key:
            raise HTTPException(status_code=404, detail="Excel文件不存在")
        record.excel_url = oss_service_v2.get_download_url(
            record.excel_key,
            expires=3600
        )

        # 重定向到OSS下载链接
        return RedirectResponse(url=record.excel_url)

    except Exception as e:
        logger.error(f"下载Excel文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/test-cases/download/xmind/{record_id}",summary="下载XMind文件",tags=['图生用例'])
async def download_xmind(record_id: str, request: Request):
    """
    下载XMind文件
    """
    try:
        user = await user_service.get_authenticated_user(request)
        record = await GenerationRecord.filter(id=record_id, user=user).first()

        if not record or not record.xmind_key:
            raise HTTPException(status_code=404, detail="XMind文件不存在")

        record.xmind_url = oss_service_v2.get_download_url(
            record.xmind_key,
            expires=3600
        )

        # 重定向到OSS下载链接
        return RedirectResponse(url=record.xmind_url)

    except Exception as e:
        logger.error(f"下载XMind文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/test-cases/records",summary="获取生成记录列表",tags=['图生用例'])
async def get_generation_records(
    request: Request,
    page: int = 1,
    page_size: int = 20
):
    """
    获取用户的生成记录列表
    """
    try:
        user = await user_service.get_authenticated_user(request)

        # 计算偏移量
        offset = (page - 1) * page_size

        # 查询记录
        records = await GenerationRecord.filter(user=user).order_by('-created_at').offset(offset).limit(page_size)

        # 转换为字典格式
        records_data = []
        for record in records:
            records_data.append({
                "id": str(record.id),
                "image_url": record.image_url,
                "context": record.context,
                "requirements": record.requirements,
                "status": record.status,
                "excel_url": record.excel_url,
                "xmind_url": record.xmind_url,
                "markdown_content": record.markdown_content,
                "created_at": record.created_at.isoformat() if record.created_at else None,
                "updated_at": record.updated_at.isoformat() if hasattr(record, 'updated_at') and record.updated_at else None
            })

        # 获取总数
        total = await GenerationRecord.filter(user=user).count()

        return {
            "records": records_data,
            "total": total,
            "page": page,
            "page_size": page_size,
            "total_pages": (total + page_size - 1) // page_size
        }

    except Exception as e:
        logger.error(f"获取生成记录列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
