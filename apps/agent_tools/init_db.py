"""
数据库初始化脚本
专门用于初始化 agent_tools 模块的数据库表
"""
import asyncio
import sys
import os
from tortoise import Tortoise

# 添加项目根目录到 Python 路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from common.settings import DATABASE

def validate_config():
    """验证配置是否正确"""
    print("验证数据库配置...")
    print(f"数据库主机: {DATABASE['host']}")
    print(f"数据库端口: {DATABASE['port']}")
    print(f"数据库名称: {DATABASE['database']}")
    print(f"用户名: {DATABASE['user']}")
    print("配置验证完成！")
    return True

def validate_models():
    """验证模型是否正确加载"""
    try:
        print("\n验证模型...")
        from apps.agent_tools.models import GenerationRecord, FileUpload, SystemLog, TestCase, TestStep
        from apps.users.models import UsersModel

        models = [
            ('GenerationRecord', GenerationRecord),
            ('FileUpload', FileUpload),
            ('SystemLog', SystemLog),
            ('TestCase', TestCase),
            ('TestStep', TestStep),
            ('UsersModel', UsersModel)
        ]

        for name, model in models:
            print(f"✓ {name} 模型加载成功")

        print("所有模型验证完成！")
        return True
    except Exception as e:
        print(f"✗ 模型验证失败: {e}")
        return False

async def init_database():
    """初始化数据库"""
    try:
        # 验证配置
        if not validate_config():
            return

        # 验证模型
        if not validate_models():
            return

        # 构建数据库连接URL
        db_url = f"mysql://{DATABASE['user']}:{DATABASE['password']}@{DATABASE['host']}:{DATABASE['port']}/{DATABASE['database']}"
        print(f"数据库连接URL: mysql://{DATABASE['user']}:***@{DATABASE['host']}:{DATABASE['port']}/{DATABASE['database']}")

        # 初始化Tortoise ORM，只包含 agent_tools 相关模型
        await Tortoise.init(
            db_url=db_url,
            modules={
                'models': [
                    'apps.agent_tools.models',  # agent_tools 模型
                    'apps.users.models'         # 依赖的 users 模型
                ]
            }
        )

        print("Tortoise ORM 初始化成功！")

        # 生成数据库表
        await Tortoise.generate_schemas()

        print("agent_tools 数据库表初始化成功！")
        print("已创建的表:")
        print("- generation_records (测试用例生成记录)")
        print("- file_uploads (文件上传记录)")
        print("- system_logs (系统日志)")
        print("- test_cases (测试用例)")
        print("- test_steps (测试步骤)")

    except ImportError as e:
        if 'aiomysql' in str(e) or 'asyncmy' in str(e):
            print("缺少 MySQL 异步驱动，请安装:")
            print("pip install aiomysql")
            print("或")
            print("pip install asyncmy")
        else:
            print(f"导入错误: {str(e)}")
    except Exception as e:
        print(f"数据库初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            await Tortoise.close_connections()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(init_database())
