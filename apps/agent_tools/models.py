#!/anaconda3/envs/FEALPy/bin python3.8
# -*- coding: utf-8 -*-
# @Author: lijing.wang
# @E-mail: <EMAIL>
# @Time: 6月 27, 2025

"""
数据库模型定义
"""
from tortoise.models import Model
from tortoise import fields
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel
from enum import Enum
import uuid



class GenerationRecord(Model):
    """测试用例生成记录"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    user = fields.ForeignKeyField("models.UsersModel", related_name="generation_records")

    # 输入信息
    image_url = fields.CharField(max_length=500)  # OSS中的图片URL
    context = fields.TextField()
    requirements = fields.TextField()

    # 生成结果
    test_cases_json = fields.JSONField(null=True)  # 存储生成的测试用例JSON数据
    markdown_content = fields.TextField(null=True)  # 存储Markdown格式内容

    # 文件信息
    excel_url = fields.CharField(max_length=500, null=True)  # Excel文件OSS URL
    excel_key = fields.CharField(max_length=500, null=True)
    xmind_url = fields.CharField(max_length=500, null=True)  # XMind文件OSS URL
    xmind_key = fields.CharField(max_length=500,null= True)

    # 状态信息
    status = fields.CharField(max_length=20, default="processing")  # processing, completed, failed
    error_message = fields.TextField(null=True)

    # 时间信息
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    completed_at = fields.DatetimeField(null=True)

    class Meta:
        table = "generation_records"
        ordering = ["-created_at"]


class FileUpload(Model):
    """文件上传记录"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    user = fields.ForeignKeyField("models.UsersModel", related_name="file_uploads")

    # 文件信息
    original_filename = fields.CharField(max_length=255)
    file_size = fields.IntField()
    file_type = fields.CharField(max_length=50)
    oss_url = fields.CharField(max_length=500)
    oss_key = fields.CharField(max_length=500)

    # 时间信息
    created_at = fields.DatetimeField(auto_now_add=True)

    class Meta:
        table = "file_uploads"


class SystemLog(Model):
    """系统日志"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    user = fields.ForeignKeyField("models.UsersModel", related_name="system_logs", null=True)

    # 日志信息
    action = fields.CharField(max_length=100)  # upload, generate, download, etc.
    details = fields.JSONField()
    ip_address = fields.CharField(max_length=45, null=True)
    user_agent = fields.CharField(max_length=500, null=True)

    # 时间信息
    created_at = fields.DatetimeField(auto_now_add=True)

    class Meta:
        table = "system_logs"


# ==================== 测试用例相关模型 ====================

class TestCaseStatus(str, Enum):
    """测试用例状态枚举"""
    PASS = "pass"
    FAIL = "fail"
    BLOCKED = "blocked"
    NOT_RUN = "not_run"


class TestStep(Model):
    """测试步骤模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    test_case = fields.ForeignKeyField("models.TestCase", related_name="steps")

    step_number = fields.IntField()
    description = fields.TextField()
    expected_result = fields.TextField()

    # 时间信息
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    class Meta:
        table = "test_steps"
        ordering = ["step_number"]


class TestCase(Model):
    """测试用例模型"""
    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    user = fields.ForeignKeyField("models.UsersModel", related_name="test_cases", null=True)
    generation_record = fields.ForeignKeyField("models.GenerationRecord", related_name="test_cases", null=True)

    # 测试用例基本信息
    title = fields.CharField(max_length=255)
    description = fields.TextField()
    preconditions = fields.TextField(null=True)
    priority = fields.CharField(max_length=20, null=True)  # High, Medium, Low
    status = fields.CharEnumField(TestCaseStatus, default=TestCaseStatus.NOT_RUN)

    # 时间信息
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    class Meta:
        table = "test_cases"
        ordering = ["-created_at"]


# ==================== Pydantic 模型（用于API） ====================

class TestStepSchema(BaseModel):
    """测试步骤 Pydantic 模型"""
    step_number: int
    description: str
    expected_result: str

    class Config:
        from_attributes = True


class TestCaseSchema(BaseModel):
    """测试用例 Pydantic 模型"""
    id: Optional[str] = None
    title: str
    description: str
    preconditions: Optional[str] = None
    steps: List[TestStepSchema] = []
    priority: Optional[str] = None
    status: Optional[TestCaseStatus] = TestCaseStatus.NOT_RUN
    created_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "TC-001",
                "title": "Verify login functionality",
                "description": "Test the user login process",
                "preconditions": "User account exists in the system",
                "steps": [
                    {
                        "step_number": 1,
                        "description": "Navigate to login page",
                        "expected_result": "Login page is displayed"
                    },
                    {
                        "step_number": 2,
                        "description": "Enter valid username and password",
                        "expected_result": "Credentials are accepted"
                    },
                    {
                        "step_number": 3,
                        "description": "Click login button",
                        "expected_result": "User is logged in and redirected to dashboard"
                    }
                ],
                "priority": "High",
                "status": "not_run",
                "created_at": "2025-05-12T10:00:00"
            }
        }


class TestCaseRequest(BaseModel):
    """测试用例请求模型"""
    context: str
    requirements: str


class TestCaseResponse(BaseModel):
    """测试用例响应模型"""
    test_cases: List[TestCaseSchema]
    excel_url: Optional[str] = None
