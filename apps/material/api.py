import math
from fastapi import APIRouter, Request
from tortoise.exceptions import DoesNotExist
from tortoise.expressions import Q
from apps.material.models import MaterialModel, MaterialTypeModel, MaterialSourceModel
from apps.material.schemes import material_resp_keys, MaterialAdd, MaterialUpdate
from apps.suite.models import SuiteModel
from common.utils import response_success, response_success_paged, response_fail, get_caller_id

material_router = APIRouter(prefix="/material")


@material_router.get("/type", summary="物料类型", tags=['物料'])
async def get_material_type_all():
    """
    获取物料类型
    :return:
    """
    material_type_all = await MaterialTypeModel.all().values("id", "name")
    return response_success(data=material_type_all, total=len(material_type_all))


@material_router.get("/source", summary="物料来源", tags=['物料'])
async def get_material_source_all():
    """
    获取物料来源
    :return:
    """
    material_source_all = await MaterialSourceModel.all().values("id", "name")
    return response_success(data=material_source_all, total=len(material_source_all))


@material_router.get("/user", summary='获取用户当前项目下可使用的全部物料', tags=['物料'])
async def get_material_user(project_id: int, page: int = 1, page_size: int = 10):
    """
    获取同项目下的所有物料以及其他项目的共享物料
    :param project_id:
    :param page:
    :param page_size:
    :return:
    """

    all_material = await (MaterialModel.filter((~Q(project_id=project_id) & Q(share=2)) | Q(project_id=project_id))
                          .order_by("id")
                          .limit(page_size)
                          .offset((page - 1) * page_size)
                          .values(*material_resp_keys))
    count = await MaterialModel.filter((~Q(project_id=project_id) & Q(share=2)) | Q(project_id=project_id)).count()

    return response_success_paged(
        data=all_material,
        total=count,
        current_page=page,
        total_page=math.ceil(count / page_size)
    )


@material_router.get("/", summary='获取当前项目的全部物料', tags=['物料'])
async def get_material_all_project(project_id: int, page: int = 1, page_size: int = 10):
    """
    获取当前项目的全部物料
    :param project_id:
    :param page:
    :param page_size:
    :return:
    """
    project_material = await (MaterialModel.filter(project_id=project_id)
                              .limit(page_size)
                              .offset((page - 1) * page_size)
                              .values(*material_resp_keys))

    count = await MaterialModel.filter(project_id=project_id).count()

    return response_success_paged(
        data=project_material,
        total=count,
        current_page=page,
        total_page=math.ceil(count / page_size))


@material_router.get("/single", summary="获取单个物料", tags=['物料'])
async def get_material_one(id: int):
    """
    获取单个物料 详情
    :param id:
    :return:
    """
    material = await MaterialModel.filter(id=id).values(*material_resp_keys)
    return response_success(material, total=len(material))


@material_router.post("", summary="新增物料", tags=['物料'])
async def add_material(material: MaterialAdd):
    """
    新增物料
    :param material:
    :return:
    """
    new_material = await MaterialModel.create(
        content_question=material.content_question,
        content_answer=material.content_answer,
        content=material.content,
        source_id=material.source,
        type_id=material.type,
        creator_id=material.creator,
        project_id=material.project,
        share=material.share,
        # is_multiple=material.is_multiple,
        # topics=material.topics
    )
    return response_success(data={'id': new_material.id})


@material_router.post("/copy", summary="复制物料", tags=['物料'])
async def add_material(id: int, request: Request):
    """
    复制物料
    :param id:
    :param request:
    :return:
    """
    caller_id = get_caller_id(request)
    try:
        copied_material = await MaterialModel.get(id=id)
        new_material = await MaterialModel.create(
            content_question=copied_material.content_question,
            content_answer=copied_material.content_answer,
            content=copied_material.content,
            source_id=copied_material.source_id,
            type_id=copied_material.type_id,
            creator_id=caller_id,
            project_id=copied_material.project_id,
            share=copied_material.share,
            # is_multiple=copied_material.is_multiple,
            # topics=copied_material.topics
        )
        return response_success(data={'id': new_material.id})
    except DoesNotExist:
        return response_fail(msg="原始物料不存在")


@material_router.put("", summary="更新物料", tags=['物料'])
async def update_material(material: MaterialUpdate, request: Request):
    """
    仅物料创建者有权限修改,管理员也不可以
    当物料可修改时，如果物料有其他用例集使用，则复制一份返回新的物料id
    :param material:
    :param request:
    :return:
    """
    caller_id = get_caller_id(request)
    try:
        old_material = await MaterialModel.get(id=material.id)
        if old_material.creator_id != caller_id:
            return response_fail(msg="无权限")
    except DoesNotExist:
        return response_fail(msg="物料不存在")

    # 检查物料是否有人使用
    # 目前没有使用，直接更新即可
    if await SuiteModel.filter(material=old_material).count() == 0:
        count = await MaterialModel.filter(id=material.id).update(
            content_question=material.content_question,
            content_answer=material.content_answer,
            content=material.content,
            source_id=material.source,
            type_id=material.type,
            project_id=material.project,
            share=material.share,
            # is_multiple=material.is_multiple,
            # topics=material.topics
        )
        return response_success(total=count, data={'id': old_material.id})
    # 有在使用，则新增一条
    else:
        material.creator = get_caller_id(request)
        new_material = await MaterialModel.create(
            content_question=material.content_question,
            content_answer=material.content_answer,
            content=material.content,
            source_id=material.source,
            type_id=material.type,
            creator_id=material.creator,
            project_id=material.project,
            share=material.share,
            # is_multiple=material.is_multiple,
            # topics=material.topics
        )
        return response_success(data={'id': new_material.id})


@material_router.delete("", summary="删除物料", tags=['物料'])
async def delete_material(id: int, request: Request):
    """
    仅物料创建者有权限删除,管理员也不可以
    当物料有用例集使用时，不可删除
    :param request:
    :param id:
    :return:
    """
    caller_id = get_caller_id(request)
    try:
        old_material = await MaterialModel.get(id=id)
        if old_material.creator_id != caller_id:
            return response_fail(msg="无权限")
    except DoesNotExist:
        return response_fail(msg="物料不存在")

    # 无用例集使用
    suites = await SuiteModel.filter(material=old_material).count()
    if suites == 0:
        await MaterialModel.filter(id=id).delete()
        return response_success(total=1, data={'id': id})
    else:
        suites_used = await SuiteModel.filter(material=old_material).values("id", "name")
        return response_fail(data={'id': id}, msg="当前物料[id:{}]在用例集{}中使用，无法删除".format(id, suites_used))
