from tortoise import models, fields


class MaterialSourceModel(models.Model):
    """
    物料来源表
    """
    id = fields.IntField(pk=True, auto_increment=True)
    name = fields.CharField(max_length=255, description="物料来源", unique=True)
    create_time = fields.data.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.data.DatetimeField(auto_now=True, description="更新时间")

    def __str__(self):
        return self.name

    class Meta:
        table = "material_source"
        table_description = "物料来源表"
        ordering = ['-id']


class MaterialTypeModel(models.Model):
    """
    物料类型表
    """
    id = fields.IntField(pk=True, auto_increment=True)
    name = fields.CharField(max_length=255, description="物料来源", unique=True)
    create_time = fields.data.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.data.DatetimeField(auto_now=True, description="更新时间")

    def __str__(self):
        return self.name

    class Meta:
        table = "material_type"
        table_description = "物料类型表"
        ordering = ['-id']


class MaterialModel(models.Model):
    """
    物料表
    """
    id = fields.IntField(pk=True, auto_increment=True)
    content_question = fields.CharField(max_length=4096,description="内容_问题")
    content_answer = fields.CharField(max_length=4096,description="内容_答案")
    content = fields.CharField(max_length=4096,description="内容")
    source = fields.ForeignKeyField('models.MaterialSourceModel', related_name="material", on_delete=fields.SET_NULL,
                                    null=True, description="来源")
    type = fields.ForeignKeyField('models.MaterialTypeModel', related_name="material", on_delete=fields.SET_NULL,
                                  null=True, description="类型")
    creator = fields.ForeignKeyField('models.UsersModel', related_name="material", on_delete=fields.SET_NULL,
                                     null=True, description="提交人")
    project = fields.ForeignKeyField('models.ProjectModel', related_name="material", on_delete=fields.SET_NULL,
                                     null=True, description="所属项目")
    # is_multiple = fields.BooleanField(default=False, description="是否是多轮对话 0不是 1是")
    # topics = fields.JSONField(max_length=512, null=True, description="问答主题，多轮对话才有")
    share = fields.IntField(default=0, description="共享程度,0:仅本人使用 1:同项目下使用 2:全部登陆人员使用")
    create_time = fields.data.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.data.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "material"
        table_description = "物料表"
        ordering = ['-id']
