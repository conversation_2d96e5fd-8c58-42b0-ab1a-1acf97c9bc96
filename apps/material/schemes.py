from typing import Optional, List, Dict, Set
from pydantic import BaseModel


class MaterialAdd(BaseModel):
    content_question: Optional[str] = None
    content_answer: Optional[str] = None
    content: Optional[str] = None
    source: int
    type: int
    creator: int
    project: int
    share: int
    # is_multiple: bool = False
    # topics: List[str] = list()


class MaterialUpdate(MaterialAdd):
    id: int


material_resp_keys = [
    "id",
    "content_answer",
    "content_question",
    "content",
    "type_id",
    "type__name",
    "source_id",
    "source__name",
    "creator_id",
    "creator__username",
    "project_id",
    "project__name",
    "share",
    # "is_multiple",
    # "topics",
    "create_time",
    "update_time",
]
