from tortoise import models, fields


class ProjectMemberModel(models.Model):
    """
    项目成员表
    """
    id = fields.IntField(pk=True, auto_increment=True)
    user = fields.ForeignKeyField('models.UsersModel', description="成员")
    project = fields.ForeignKeyField('models.ProjectModel', description="项目")
    create_time = fields.data.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.data.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "project_member"
        table_description = "项目成员表"
        ordering = ['-id']
