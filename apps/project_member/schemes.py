from typing import Set

from pip._internal.models.scheme import Scheme
from pydantic import BaseModel

member_resp_key = [
    "id",
    "user_id",
    "user__username",
    "user__staff_no",
    "project_id",
    "project__name",
    "create_time",
    "update_time",
]

class AllProjectMember(BaseModel):
    page:int = 1
    page_size:int = 10
    project_ids:Set[int] = set()
    user_ids:Set[int] = set()

class ProjectMemberAdd(BaseModel):
    project_id: int
    user_id: int