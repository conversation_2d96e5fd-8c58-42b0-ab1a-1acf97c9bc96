import math
from typing import List, Set

from fastapi import APIRouter, Request, Query
from apps.project_member.models import ProjectMemberModel
from apps.project_member.schemes import member_resp_key, AllProjectMember, ProjectMemberAdd
from common.utils import response_success, response_success_paged, is_superuser, response_fail

project_member_router = APIRouter(prefix="/project_member")


@project_member_router.post("/all", summary="查看全部项目成员", tags=['项目成员'])
async def get_project_member_all(req: AllProjectMember, request: Request):
    """
    查询全部项目成员
    :return:
    """
    if not await is_superuser(request):
        return response_fail(msg="无操作权限")

    project_member = ProjectMemberModel.all()
    if len(req.project_ids):
        project_member = project_member.filter(project_id__in=req.project_ids)

    if len(req.user_ids):
        project_member = project_member.filter(user_id__in=req.user_ids)

    project_member_all = await (project_member
                                .limit(req.page_size)
                                .offset((req.page - 1) * req.page_size)
                                .values(*member_resp_key))
    count = await project_member.count()

    return response_success_paged(
        data=project_member_all,
        total=count,
        current_page=req.page,
        total_page=math.ceil(count / req.page_size)
    )


@project_member_router.get("", summary="查看项目成员", tags=['项目成员'])
async def get_project_member(request: Request, project_id: int, page: int = 1, page_size: int = 10):
    """
    查询项目成员
    仅超级管理员可操作
    :param request:
    :param project_id:
    :param page:
    :param page_size:
    :return:
    """
    if not await is_superuser(request):
        return response_fail(msg="无操作权限")

    project_member = await (ProjectMemberModel.filter(project_id=project_id)
                            .limit(page_size)
                            .offset((page - 1) * page_size)
                            .values(*member_resp_key))
    count = await ProjectMemberModel.filter(project_id=project_id).count()

    return response_success_paged(
        data=project_member,
        total=count,
        current_page=page,
        total_page=math.ceil(count / page_size)
    )


@project_member_router.post("", summary="新增项目成员", tags=['项目成员'])
async def add_project_member(req: ProjectMemberAdd, request: Request):
    """
    新增项目成员
    仅超级管理员可操作
    :param req:
    :param request:
    :return:
    """
    if not (await is_superuser(request)):
        return response_fail(msg="[调用者非管理员无权限]")
    # 如果已存在 则不新建。
    new_member, status = await ProjectMemberModel.get_or_create(project_id=req.project_id, user_id=req.user_id)
    return response_success(data={"id": new_member.id})


@project_member_router.delete("", summary="删除项目成员", tags=['项目成员'])
async def delete_project_member(id: int, request: Request):
    """
    删除项目成员
    仅超级管理员可操作
    :param id:
    :param request:
    :return:
    """
    if not (await is_superuser(request)):
        return response_fail(msg="[调用者非管理员无权限]")
    count = await ProjectMemberModel.filter(id=id).delete()
    return response_success(total=count)


@project_member_router.get("/user_in_project", summary="判定是否是项目成员", tags=['项目成员'])
async def delete_project_member(project_id: int, user_id: int):
    """
    判定是否是项目成员
    :param project_id:
    :param user_id:
    :return:
    """
    resp_data = {
        "is_member": await ProjectMemberModel.filter(project_id=project_id, user_id=user_id).exists()
    }
    return response_success(data=resp_data)
