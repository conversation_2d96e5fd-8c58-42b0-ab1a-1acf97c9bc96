import asyncio
import logging
import math
import time
from datetime import datetime
from typing import List
from fastapi import APIRouter, Request, BackgroundTasks
from openai import OpenAI
from tortoise.exceptions import DoesNotExist

from apps.material.models import MaterialModel
from apps.suite.api import generate_hierarchical_structure, transform_data
from apps.suite.models import SuiteModel
from apps.suite_execute.models import SuiteExecuteSequenceModel, SuiteExecuteDetailModel
from apps.suite_execute.schemes import suite_exec_sequence_response_key, suite_exec_sequence_detail_response_key, \
    MultipleConversation, QueryParam
from apps.users.models import UsersModel
from common.settings import AI_MODEL
from common.utils import response_success, response_success_paged, response_fail, get_caller_id, model_data_exist

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
suite_execute_router = APIRouter(prefix="/suite_execute", tags=["用例集执行"])


def talk_to_ai(client: OpenAI, msg: List[dict]):
    """
    向大模型提问并获取答案
    :param client:
    :param msg: 全部对话
    :return:
    """
    completion = client.chat.completions.create(
        model=AI_MODEL.get("MODEL"),  # 模型列表：https://help.aliyun.com/zh/model-studio/getting-started/models
        messages=msg
    )
    response = completion.choices[0].message.content
    msg.append({"role": "assistant", "content": response})


def execute_single_case(case: MaterialModel):
    """
    执行单条case
    :param case:
    :return:
    """
    client = OpenAI(
        # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key="sk-xxx",
        api_key=AI_MODEL.get("API_KEY"),
        base_url=AI_MODEL.get("API_BASE"),
    )

    msg = [{'role': 'system', 'content': 'You are a helpful assistant.'},
           {"role": "user", "content": case.content_question}]

    talk_to_ai(client, msg)

    return msg


async def save_execute_result(case: MaterialModel, answer: List[dict[str, str]], sequence_id: int):
    """
    存储case执行结果
    :param case:
    :param answer:
    :param sequence_id:
    :return:
    """
    answer_llm = answer[len(answer) - 1].get("content", "未获取返回内容")
    await SuiteExecuteDetailModel.create(
        sequence_id=sequence_id,
        is_multiple=False,
        question=case.content_question,
        answer_expect=case.content_answer,
        material_id=case.id,
        answer_llm=answer_llm
    )


async def execute_cases(sequence_id: int, suite_id: int):
    """
    执行用例集下的全部case
    :param sequence_id:
    :param suite_id:
    :return:
    """
    # 获取用例集下的全部用例
    suite = await SuiteModel.get(id=suite_id)
    all_case = await suite.material.all()

    # 执行用例并存储结果
    for case in all_case:
        answer = execute_single_case(case)
        await save_execute_result(case, answer, sequence_id)
        time.sleep(1)

    # 记录执行序列 结束
    await record_execute_sequence(sequence_id=sequence_id, is_start=False)


async def record_execute_sequence(suite_id: int = None, user_id: int = None, sequence_id: int = None,
                                  is_start: bool = True):
    """
    记录执行序列
    :param suite_id:用例集id
    :param user_id: 执行者id
    :param sequence_id: 执行序列id
    :param is_start: 标记记录 执行开始True，执行结束False
    :return:
    """
    if is_start:
        last_sequence = await SuiteExecuteSequenceModel.filter(suite_id=suite_id).limit(1)
        # 首次执行
        if len(last_sequence) == 0:
            current_sequence = await SuiteExecuteSequenceModel.create(
                sequence_num=0,
                suite_id=suite_id,
                exec_start_time=datetime.now(),
                exec_state=0,
                executor_id=user_id,
            )
        # 非首次执行
        else:
            sequence_num = last_sequence[0].sequence_num + 1
            current_sequence = await SuiteExecuteSequenceModel.create(
                sequence_num=sequence_num,
                suite_id=suite_id,
                exec_start_time=datetime.now(),
                exec_state=0,
                executor_id=user_id,
            )
        return current_sequence.id
    else:
        await SuiteExecuteSequenceModel.filter(id=sequence_id).update(
            exec_end_time=datetime.now(),
            exec_state=1,
        )
        return sequence_id


@suite_execute_router.post("/", summary='执行用例集')
async def suite_execute(suite_id: int, request: Request, background_tasks: BackgroundTasks):
    """
    执行用例集
    :param suite_id:
    :param request:
    :param background_tasks:
    :return:
    """
    if not await model_data_exist(suite_id, SuiteModel):
        return response_fail(msg="{} 对应用例集不存在".format(suite_id))

    user_id = get_caller_id(request)
    # 记录执行序列 开始
    sequence_id = await record_execute_sequence(suite_id=suite_id, user_id=user_id)
    # 后台执行
    background_tasks.add_task(execute_cases, sequence_id, suite_id)
    return response_success()


def run_async_function(sequence_id, suite_id):
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        coro = execute_cases(sequence_id, suite_id)
        future = asyncio.run_coroutine_threadsafe(coro, loop)
        # 等待异步操作完成
        result = future.result()
    finally:
        loop.close()


@suite_execute_router.get("/result/sequence", summary='获取用例集执行后的序列')
async def get_suite_execute_sequence(suite_id: int, page: int = 1, page_size: int = 10):
    """
    查看用例集的执行序列
    :param suite_id:
    :param page:
    :param page_size:
    :return:
    """
    if not await model_data_exist(suite_id, SuiteModel):
        return response_fail(msg="{} 对应用例集不存在".format(suite_id))

    all_sequence = await (SuiteExecuteSequenceModel.filter(suite_id=suite_id)
                          .limit(page_size)
                          .offset((page - 1) * page_size)
                          .values(*suite_exec_sequence_response_key))
    total_count = await SuiteExecuteSequenceModel.filter(suite_id=suite_id).count()

    return response_success_paged(
        data=all_sequence,
        total=total_count,
        current_page=page,
        total_page=math.ceil(total_count / page_size)
    )


@suite_execute_router.post("/result/sequence/user", summary='获取用户可见的用例集执行后的序列')
async def get_suite_execute_sequence(query: QueryParam, request: Request):
    """
    获取用户可见的用例集执行后的序列
    """
    try:
        caller = get_caller_id(request)
        user = await UsersModel.get(id=caller)
    except DoesNotExist:
        return response_fail(msg="未登陆，用户不存在")

    hierarchical_structure = await generate_hierarchical_structure(user)

    transformed_data = transform_data(hierarchical_structure.get('hierarchical_structure'))
    # print(json.dumps(transformed_data))

    # 没有传递任何参数，则提供全部的
    if len(query.suite_ids) == 0 and len(query.req_ids) == 0 and len(query.project_ids) == 0:
        suite_ids = hierarchical_structure['level3']
    else:  # 否则按照参数取suite
        suite_ids = list()
        for project_id in query.project_ids:
            suite_ids.extend(
                transformed_data.get('project', {}).get(project_id, {}).get('suite', [])
            )

        for req_id in query.req_ids:
            suite_ids.extend(
                transformed_data.get('requirement', {}).get(req_id, {}).get('suite', [])
            )

        suite_ids.extend(query.suite_ids)
        suite_ids = list(set(suite_ids))

    # 获取suite执行记录
    all_sequence = await (SuiteExecuteSequenceModel.filter(suite_id__in=suite_ids)
                          .limit(query.page_size)
                          .offset((query.page - 1) * query.page_size)
                          .values(*suite_exec_sequence_response_key))
    total_count = await SuiteExecuteSequenceModel.filter(suite_id__in=suite_ids).count()

    # 给数据拼接项目-需求信息
    # print(all_sequence)

    for sequence in all_sequence:
        # 拼接需求
        sequence.update(transformed_data['suite'].get(sequence['suite_id']).get('project')[0])
        # 拼接项目
        sequence.update(transformed_data['suite'].get(sequence['suite_id']).get('requirement')[0])

    return response_success_paged(
        data=all_sequence,
        total=total_count,
        current_page=query.page,
        total_page=math.ceil(total_count / query.page_size)
    )


@suite_execute_router.get("/result/sequence/detail", summary='获取用例集执行后的详细信息')
async def get_suite_execute_sequence_detail(sequence_id: int, page: int = 1, page_size: int = 10):
    """
    查看用例集的执行序列
    :param sequence_id:
    :param page:
    :param page_size:
    :return:
    """
    if not await model_data_exist(sequence_id, SuiteExecuteSequenceModel):
        return response_fail(msg="{} 对应执行序列不存在".format(sequence_id))

    all_detail = await (SuiteExecuteDetailModel.filter(sequence_id=sequence_id)
                        .limit(page_size)
                        .offset((page - 1) * page_size)
                        .values(*suite_exec_sequence_detail_response_key))
    total_count = await SuiteExecuteDetailModel.filter(sequence_id=sequence_id).count()

    return response_success_paged(
        data=all_detail,
        total=total_count,
        current_page=page,
        total_page=math.ceil(total_count / page_size)
    )


@suite_execute_router.post("/record/multiple/conversation", summary='录入多轮对话')
async def record_multiple_conversation(multiple_data: MultipleConversation):
    """
    录入多轮对话
    :param multiple_data:
    :return:
    """
    sequence_id = multiple_data.sequence_id
    if not await model_data_exist(sequence_id, SuiteExecuteSequenceModel):
        return response_fail(msg="[{}]执行序列不存在".format(multiple_data.sequence_id))
    ids = list()
    for data in multiple_data.multi_data:
        record = await SuiteExecuteDetailModel.create(
            sequence_id=sequence_id,
            is_multiple=True,
            answer_expect=data.answer_expect,
            reference=data.answer_expect,
            answer_llm_multiple=data.conversation,
            topics=data.topics
        )
        ids.append(record.id)

    return response_success(data={"ids": ids})
