from typing import Optional, List
from pydantic import BaseModel


class MultipleConversationBase(BaseModel):
    conversation: List[dict[str, str]] = list()
    answer_expect: str
    topics: List[str] = list()

class MultipleConversation(BaseModel):
    sequence_id: int
    multi_data:List[MultipleConversationBase]

class QueryParam(BaseModel):
    suite_ids: list[int] = list()
    req_ids: list[int] = list()
    project_ids: list[int] = list()
    page: int = 1
    page_size: int = 10


suite_exec_sequence_response_key = [
    "id",
    "sequence_num",
    "suite_id",
    "suite__name",
    "suite__suite_type",
    "exec_start_time",
    "exec_end_time",
    "exec_state",
    "executor_id",
    "executor__username",
    "create_time",
    "update_time",
]

suite_exec_sequence_detail_response_key = [
    "id",
    "sequence_id",
    "is_multiple",
    "topics",
    "contexts",
    "question",
    "answer_expect",
    "answer_llm",
    "reference",
    "answer_llm_multiple",
    "create_time",
    "update_time",
]
