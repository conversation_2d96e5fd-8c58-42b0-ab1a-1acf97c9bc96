from tortoise import models, fields


class SuiteExecuteSequenceModel(models.Model):
    """
    用例集执行序号表
    """
    id = fields.IntField(pk=True, auto_increment=True)
    sequence_num = fields.IntField(description="执行序号")
    suite = fields.ForeignKeyField('models.SuiteModel', related_name="suite", on_delete=fields.SET_NULL, null=True,
                                   description="执行的用例集")
    exec_start_time = fields.DatetimeField(description="执行开始时间")
    exec_end_time = fields.DatetimeField(null=True, description="执行结束时间")
    exec_state = fields.IntField(default=0, description="执行状态,0执行中,1执行结束")
    executor = fields.ForeignKeyField('models.UsersModel', related_name="suite_executor", on_delete=fields.SET_NULL,
                                      null=True, description="执行人")
    create_time = fields.data.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.data.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "suite_execute_sequence"
        table_description = "用例集执行序号表"
        ordering = ['-id']


class SuiteExecuteDetailModel(models.Model):
    """
    用例集执行详情表
    """
    id = fields.IntField(pk=True, auto_increment=True)
    sequence = fields.ForeignKeyField('models.SuiteExecuteSequenceModel', related_name="seq", on_delete=fields.SET_NULL,
                                      null=True)
    is_multiple = fields.BooleanField(description="是否是多轮对话 0不是 1是")
    topics = fields.JSONField(max_length=512, null=True, description="多轮对话 问答主题")
    contexts = fields.JSONField(null=True, description="上下文，单独问答")
    question = fields.CharField(null=True, max_length=4096,description="问题")
    answer_expect = fields.CharField(max_length=4096,description="预期答案")
    answer_llm = fields.CharField(null=True, max_length=4096,description="单条问答模型答案")
    answer_llm_multiple = fields.JSONField(null=True, description="多轮对话模型答案")
    reference = fields.CharField(max_length=1024,null=True,description="多轮对话 参考")
    material_id = fields.IntField(null=True,description="物料id")
    create_time = fields.data.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.data.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "suite_execute_detail"
        table_description = "用例集执行详情表"
        ordering = ['-id']
