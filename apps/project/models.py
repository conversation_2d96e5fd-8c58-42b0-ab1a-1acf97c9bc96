from tortoise import models, fields


class ProjectModel(models.Model):
    """
    项目表
    """
    id = fields.IntField(pk=True, auto_increment=True)
    name = fields.CharField(max_length=255, description="项目名", unique=True)
    creator = fields.ForeignKeyField('models.UsersModel', related_name="project", on_delete=fields.SET_NULL, null=True)
    description = fields.CharField(max_length=255, description="项目描述", null=True)
    create_time = fields.data.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.data.DatetimeField(auto_now=True, description="更新时间")

    def __str__(self):
        return self.name

    class Meta:
        table = "project"
        table_description = "项目表"
        ordering = ['-id']
