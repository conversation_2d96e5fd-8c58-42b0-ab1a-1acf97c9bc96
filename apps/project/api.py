import math
from fastapi import APIRouter, Request
from apps.project.models import ProjectModel
from apps.project.schemes import project_resp_keys, ProjectAdd, ProjectUpdate
from apps.project_member.models import ProjectMemberModel
from common.utils import response_success, response_success_paged, is_superuser, response_fail, get_caller_id, \
    is_project_member, model_data_exist

project_router = APIRouter(prefix="/project")


@project_router.get("", summary='获取全部项目信息', tags=['项目'])
async def get_project_all(request: Request, page: int = 1, page_size: int = 10):
    """
    超管 返回全部项目
    非超管 返回所属的全部项目
    :param request:
    :param page:
    :param page_size:
    :return:
    """
    if await is_superuser(request):
        all_project = await (ProjectModel.all()
                             .limit(page_size)
                             .offset((page - 1) * page_size)
                             .values(*project_resp_keys))
        count = await ProjectModel.all().count()
    else:
        call_id = get_caller_id(request)
        project_ids = await ProjectMemberModel.filter(user_id=call_id).values_list('project_id', flat=True)
        all_project = await (ProjectModel.filter(id__in=project_ids)
                             .limit(page_size)
                             .offset((page - 1) * page_size)
                             .values(*project_resp_keys))
        count = await ProjectModel.filter(id__in=project_ids).count()

    return response_success_paged(
        data=all_project,
        total=count,
        current_page=page,
        total_page=math.ceil(count / page_size)
    )


@project_router.get("/{id}", summary="获取单个项目信息", tags=['项目'])
async def get_project_one(id: int, request: Request):
    """
    调用者是 项目成员/超管 才可以查看
    :param id:
    :param request:
    :return:
    """
    caller_id = get_caller_id(request)
    if await model_data_exist(id, ProjectModel):
        if (await is_project_member(project_id=id, user_id=caller_id)) or is_superuser(request):
            project = await ProjectModel.filter(id=id).values(*project_resp_keys)
            return response_success(project, total=len(project))
        else:
            return response_fail(msg="用户无权访问")
    else:
        return response_fail(msg="[{}]对应数据不存在".format(id))


@project_router.post("", summary="新增项目", tags=['项目'])
async def add_project(project: ProjectAdd, request: Request):
    """
    仅管理员可操作
    :param project:
    :param request:
    :return:
    """
    # if not await is_superuser(request):
    #     return response_fail(msg="无操作权限")

    caller_id = get_caller_id(request)

    if await ProjectModel.filter(name=project.name).exists():
        return response_fail(msg="项目名[{}]已存在".format(project.name))

    # 创建项目
    new_project = await ProjectModel.create(
        name=project.name,
        creator_id=caller_id,
        description=project.description)

    # 把创建人放到项目成员中
    await ProjectMemberModel.create(
        user_id=caller_id,
        project_id=new_project.id
    )

    return response_success(data={'id': new_project.id})


@project_router.put("", summary="更新项目", tags=['项目'])
async def update_project(project: ProjectUpdate, request: Request):
    """
    仅管理员可操作
    :param project:
    :param request:
    :return:
    """
    if not await model_data_exist(project.id, ProjectModel):
        return response_fail(msg="项目不存在")

    call_id = get_caller_id(request)

    if not await is_superuser(request) and not await ProjectModel.filter(id=project.id,creator_id=call_id).exists():
        return response_fail(msg="无操作权限")

    if await ProjectModel.exclude(id=project.id).filter(name=project.name).exists():
        return response_fail(msg="项目名[{}]已存在".format(project.name))

    count = await ProjectModel.filter(id=project.id) \
        .update(name=project.name, description=project.description)
    return response_success(total=count)


@project_router.delete("", summary="删除项目", tags=['项目'])
async def delete_project(id: int, request: Request):
    """
    仅管理员可操作
    :param id:
    :param request:
    :return:
    """
    if not (await is_superuser(request)):
        return response_fail(msg="无操作权限")

    count = await ProjectModel.filter(id=id).delete()
    return response_success(total=count)
