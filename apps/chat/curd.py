# @Time: 2025/6/19 16:41
# @Author: lvjing
from datetime import datetime
from typing import List, Optional, Dict, Any

from tortoise.exceptions import DoesNotExist
from tortoise.transactions import in_transaction

from apps.chat.models import ChatSessionModel, ChatMessageModel


class ChatDatabaseService:
    """聊天数据库服务层"""

    @staticmethod
    async def get_or_create_session(
            session_id: str,
            user_id: Optional[str] = None,
            title: Optional[str] = None,
            agent_name: str = "chat_assistant",
            system_message: Optional[str] = None,
            model_name: Optional[str] = None
    ) -> ChatSessionModel:
        """获取或创建聊天会话"""
        try:
            session = await ChatSessionModel.get(session_id=session_id)
            # 更新最后活动时间
            session.updated_time = datetime.now()
            await session.save(update_fields=['updated_time'])
            return session
        except DoesNotExist:
            # 创建新会话
            session = await ChatSessionModel.create(
                session_id=session_id,
                user_id=user_id,
                title=title or f"对话 {session_id}",
                agent_name=agent_name,
                system_message=system_message,
                model_name=model_name,
                last_message_at=datetime.now()
            )
            return session

    @staticmethod
    async def save_message(
            session_id: str,
            role: str,
            content: str,
            message_type: str = "text",
            source: Optional[str] = None,
            message_id: Optional[str] = None,
            parent_message_id: Optional[str] = None,
            model_name: Optional[str] = None,
            finish_reason: Optional[str] = None,
            error_message: Optional[str] = None,
            processing_time: Optional[float] = None,
            metadata: Optional[Dict[str, Any]] = None
    ) -> ChatMessageModel:
        """保存聊天消息"""
        async with in_transaction():
            # 获取会话
            session = await ChatSessionModel.get(session_id=session_id)

            # 创建消息
            message = await ChatMessageModel.create(
                session_id=session,
                role=role,
                content=content,
                message_type=message_type,
                source=source,
                message_id=message_id,
                parent_message_id=parent_message_id,
                model_name=model_name,
                finish_reason=finish_reason,
                error_message=error_message,
                processing_time=processing_time,
                metadata=metadata or {}
            )

            # 更新会话统计信息
            session.total_messages += 1
            session.last_message_at = datetime.now()
            await session.save(update_fields=['total_messages', 'last_message_at'])

            return message

    @staticmethod
    async def get_session_messages(
            session_id: str,
            limit: Optional[int] = None,
            offset: int = 0,
            role_filter: Optional[str] = None
    ) -> List[ChatMessageModel]:
        """获取会话消息列表"""
        query = ChatMessageModel.filter(session_id__session_id=session_id)

        if role_filter:
            query = query.filter(role=role_filter)

        query = query.order_by('created_time').offset(offset)

        if limit:
            query = query.limit(limit)
        return await query.all()

    @staticmethod
    async def get_session_info(session_id: str) -> Optional[ChatSessionModel]:
        """获取会话信息"""
        try:
            return await ChatSessionModel.get(session_id=session_id)
        except DoesNotExist:
            return None

    @staticmethod
    async def get_user_sessions(
            user_id: str,
            limit: int = 20,
            offset: int = 0,
            active_only: bool = True
    ) -> List[ChatSessionModel]:
        """获取用户的会话列表"""
        query = ChatSessionModel.filter(user_id=user_id)

        if active_only:
            query = query.filter(is_active=True)

        return await query.order_by('-last_message_at').offset(offset).limit(limit).all()

    @staticmethod
    async def update_session_title(session_id: str, title: str) -> bool:
        """更新会话标题"""
        try:
            session = await ChatSessionModel.get(session_id=session_id)
            session.title = title
            await session.save(update_fields=['title'])
            return True
        except DoesNotExist:
            return False

    @staticmethod
    async def deactivate_session(session_id: str) -> bool:
        """停用会话"""
        try:
            session = await ChatSessionModel.get(session_id=session_id)
            session.is_active = False
            await session.save(update_fields=['is_active'])
            return True
        except DoesNotExist:
            return False

    @staticmethod
    async def delete_session(session_id: str) -> bool:
        """删除会话及其所有消息"""
        try:
            async with in_transaction():
                session = await ChatSessionModel.get(session_id=session_id)
                # 删除所有相关消息
                await ChatMessageModel.filter(session_id=session).delete()
                # 删除会话
                await session.delete()
            return True
        except DoesNotExist:
            return False

    @staticmethod
    async def get_session_statistics(session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话统计信息"""
        try:
            session = await ChatSessionModel.get(session_id=session_id)
            messages = await ChatMessageModel.filter(session_id=session).all()

            user_messages = [msg for msg in messages if msg.role == 'user']
            assistant_messages = [msg for msg in messages if msg.role == 'assistant']

            return {
                'session_id': session_id,
                'total_messages': len(messages),
                'user_messages': len(user_messages),
                'assistant_messages': len(assistant_messages),
                'created_time': session.created_time,  # 保持datetime对象，在API层转换
                'last_message_at': session.last_message_at,  # 保持datetime对象，在API层转换
                'duration_minutes': (
                    (session.last_message_at - session.created_time).total_seconds() / 60
                    if session.last_message_at and session.created_time else 0
                )
            }
        except DoesNotExist:
            return None

    @staticmethod
    async def search_messages(
            query: str,
            session_id: Optional[str] = None,
            user_id: Optional[str] = None,
            limit: int = 50
    ) -> List[ChatMessageModel]:
        """搜索消息内容"""
        message_query = ChatMessageModel.filter(content__icontains=query)

        if session_id:
            message_query = message_query.filter(session_id__session_id=session_id)

        if user_id:
            message_query = message_query.filter(session_id__user_id=user_id)

        return await message_query.order_by('-created_time').limit(limit).all()


# 创建全局实例
chat_db_service = ChatDatabaseService()
