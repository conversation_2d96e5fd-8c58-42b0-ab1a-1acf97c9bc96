# @Time: 2025/6/19 15:30
# @Author: lvjing
from typing import Optional

from tortoise import fields
from tortoise.models import Model


class ChatSessionModel(Model):
    """
    聊天会话模型
    """
    id = fields.IntField(pk=True, auto_increment=True, description="主键id")
    session_id = fields.CharField(max_length=100, unique=True, description="会话ID")
    title = fields.CharField(max_length=200, null=True, description="会话标题")
    agent_name = fields.CharField(max_length=100, default="chat_assistant", description="智能体名称")
    system_message = fields.TextField(null=True, description="系统消息")
    model_name = fields.CharField(max_length=100, null=True, description="使用的模型名称")
    total_messages = fields.IntField(default=0, description="消息总数")
    last_message_at = fields.DatetimeField(null=True, description="最后消息时间")
    user_id = fields.Char<PERSON><PERSON>(max_length=100, null=True, description="用户ID")
    is_active = fields.<PERSON><PERSON><PERSON><PERSON><PERSON>(default=True, description="是否活跃")
    created_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_time = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "chat_sessions"
        table_description = "聊天会话表"
        indexes = [
            ("session_id",),
            ("user_id",),
            ("created_time",),
            ("last_message_at",),
        ]

    def __str__(self):
        return f"ChatSessionModel(session_id={self.session_id}, title={self.title})"


class ChatMessageModel(Model):
    """聊天消息模型"""
    id = fields.IntField(pk=True, auto_increment=True, description="主键id")
    session_id = fields.ForeignKeyField("models.ChatSessionModel", related_name="messages", description="所属会话")
    role = fields.CharField(max_length=20, description="角色：user/assistant/system")
    content = fields.TextField(description="消息内容")
    message_type = fields.CharField(max_length=20, default="text", description="消息类型：text/image/file等")
    source = fields.CharField(max_length=100, null=True, description="消息来源（AutoGen中的source字段）")
    model_name = fields.CharField(max_length=100, null=True, description="生成此消息的模型")
    finish_reason = fields.CharField(max_length=50, null=True, description="完成原因")
    error_message = fields.TextField(null=True, description="错误信息")
    processing_time = fields.FloatField(null=True, description="处理时间（秒）")
    created_time = fields.DatetimeField(auto_now_add=True, description="创建时间")

    class Meta:
        table = "chat_messages"
        table_description = "聊天消息表"
        indexes = [
            ("session_id", "created_time"),
            ("role",),
            ("message_type",),
            ("created_time",),
        ]
        ordering = ["created_time"]

    def __str__(self):
        return f"ChatMessageModel(id={self.id}, role={self.role}, content={self.content[:50]}...)"

    @property
    def session(self):
        """获取会话对象"""
        return self.session_id

    def get_session_id_str(self) -> Optional[str]:
        """获取会话ID字符串"""
        return self.session_id.session_id if self.session_id else None

    def to_autogen_format(self) -> dict:
        """转换为AutoGen消息格式"""
        return {
            "content": self.content,
            "source": self.source or "unknown",
            "role": self.role,
        }

    @classmethod
    async def from_autogen_message(cls, message, session_id: str, **kwargs):
        """从AutoGen消息创建数据库记录"""
        # 处理不同类型的AutoGen消息
        if hasattr(message, 'content'):
            content = message.content
        elif isinstance(message, dict):
            content = message.get('content', '')
        else:
            content = str(message)

        source = getattr(message, 'source', None) or kwargs.get('source', 'unknown')
        role = getattr(message, 'role', None) or kwargs.get('role', 'assistant')

        # 获取会话对象
        session = await ChatSessionModel.get(session_id=session_id)

        return cls(
            session_id=session,
            content=content,
            source=source,
            role=role,
            message_type=kwargs.get('message_type', 'text'),
            model_name=kwargs.get('model_name'),
        )
