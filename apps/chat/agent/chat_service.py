#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI智能体聊天服务模块

该模块提供了AI智能体的核心聊天服务功能，包括：
- 智能对话管理：支持多轮对话和上下文记忆
- 会话状态管理：自动管理用户会话和智能体实例
- 流式输出支持：提供实时流式对话响应
- 消息持久化：自动保存对话历史到数据库
- 多模型支持：支持不同的大语言模型切换

核心特性：
- 基于AutoGen AgentChat框架
- 支持流式和非流式两种对话模式
- 自动会话创建和管理
- 完整的对话历史记录
- 异常处理和错误恢复

@Time: 2025/6/18 10:50
@Author: lvjing
@LastModified: 2025/7/7
@Version: 2.0
"""

import logging
# ==================== 标准库导入 ====================
import time
from typing import Dict, AsyncGenerator, Any

# ==================== 第三方库导入 ====================
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import ModelClientStreamingChunkEvent, TextMessage

# ==================== 项目内部导入 ====================
from apps.chat.curd import chat_db_service
from apps.llm_manage.models import LLMModel
from apps.llm_manage.service.llm_autogen import get_model_client

# ==================== 日志配置 ====================
logger = logging.getLogger(__name__)

# ==================== 常量定义 ====================
# 默认系统消息
DEFAULT_SYSTEM_MESSAGE = "你是一个智能助手，能够帮助用户解答各种问题。请用中文回答，回答要准确、有用且友好。"


# 会话配置
class SessionConfig:
    MAX_SESSIONS = 1000  # 最大会话数量
    SESSION_TIMEOUT = 3600  # 会话超时时间（秒）
    MAX_MESSAGE_LENGTH = 10000  # 最大消息长度


# 响应类型
class ResponseType:
    CHAT_MESSAGE = "chat_message"  # 聊天消息
    ERROR = "error"  # 错误消息
    SYSTEM = "system"  # 系统消息
    THINKING = "thinking"  # 思考过程


# ==================== AI智能体聊天服务类 ====================

class ChatService:
    """
    AI智能体聊天服务类

    功能说明：
    - 管理用户会话和智能体实例
    - 提供流式和非流式对话接口
    - 自动保存对话历史到数据库
    - 支持多模型切换和配置

    属性：
        model_id: 使用的模型ID
        model_name: 模型名称
        sessions: 会话字典，存储用户会话和智能体实例
        default_system_message: 默认系统提示词
    """

    model_name: str
    model_id: int
    sessions: Dict[str, AssistantAgent]
    default_system_message: str

    def __init__(self, model_id: int):
        """
        初始化聊天服务

        Args:
            model_id: 要使用的大语言模型ID

        Raises:
            ValueError: 当模型ID无效时抛出异常
        """
        if not model_id or model_id <= 0:
            raise ValueError("模型ID必须是正整数")

        self.model_id = model_id
        self.sessions = {}
        self.default_system_message = DEFAULT_SYSTEM_MESSAGE

        logger.info(f"ChatService初始化完成，模型ID: {model_id}")

    def _validate_message(self, message: str) -> bool:
        """
        验证消息内容

        Args:
            message: 用户消息

        Returns:
            bool: 验证是否通过
        """
        if not message or not message.strip():
            return False

        if len(message) > SessionConfig.MAX_MESSAGE_LENGTH:
            return False

        return True

    def _cleanup_sessions(self):
        """
        清理过期会话
        """
        if len(self.sessions) > SessionConfig.MAX_SESSIONS:
            # 简单的清理策略：保留最近的一半会话
            sessions_to_keep = SessionConfig.MAX_SESSIONS // 2
            session_items = list(self.sessions.items())
            self.sessions = dict(session_items[-sessions_to_keep:])
            logger.info(f"清理会话，保留 {sessions_to_keep} 个会话")

    async def _get_or_create_agent(self, session_id: str, user_id: int) -> AssistantAgent:
        """
        获取或创建会话对应的智能体

        功能说明：
        - 检查会话是否已存在，如不存在则创建新的智能体
        - 从数据库获取或创建会话记录
        - 配置智能体的模型客户端和系统消息
        - 启用流式输出支持

        Args:
            session_id: 会话ID，用于标识用户会话
            user_id: 用户ID

        Returns:
            AssistantAgent: 配置好的智能体实例

        Raises:
            ValueError: 当模型配置无效时抛出异常
            Exception: 当智能体创建失败时抛出异常
        """
        logger.debug(f"获取或创建智能体，session_id: {session_id}, user_id: {user_id}")

        try:
            # ========== 1. 获取模型配置 ==========
            model_config = await LLMModel.get_or_none(id=self.model_id)
            if not model_config:
                logger.error(f"未找到模型配置，model_id: {self.model_id}")
                raise ValueError(f"未找到对应的模型配置，模型ID: {self.model_id}")

            self.model_name = model_config.model_name
            logger.debug(f"使用模型: {self.model_name}")

            # ========== 2. 检查会话是否已存在 ==========
            if session_id not in self.sessions:
                logger.info(f"创建新的智能体会话: {session_id}")

                # ========== 3. 从数据库获取或创建会话 ==========
                session = await chat_db_service.get_or_create_session(
                    session_id=session_id,
                    user_id=user_id,
                    agent_name="chat_assistant",
                    system_message=self.default_system_message,
                    model_name=self.model_name
                )

                # ========== 4. 创建智能体实例 ==========
                try:
                    model_client = get_model_client(model_config)

                    self.sessions[session_id] = AssistantAgent(
                        name="chat_assistant",
                        model_client=model_client,
                        system_message=session.system_message or self.default_system_message,
                        model_client_stream=True,  # 启用流式输出
                    )

                    logger.info(f"智能体创建成功，session_id: {session_id}")

                except Exception as e:
                    logger.error(f"智能体创建失败: {str(e)}")
                    raise Exception(f"智能体创建失败: {str(e)}")

                # ========== 5. 会话清理 ==========
                self._cleanup_sessions()

            else:
                logger.debug(f"使用现有智能体会话: {session_id}")

            return self.sessions[session_id]

        except Exception as e:
            logger.error(f"获取或创建智能体失败: {str(e)}")
            raise

    async def chat_stream(self, message: str, user_id: int, session_id: str) -> AsyncGenerator[
        Dict[str, Any], None]:
        """流式聊天"""
        start_time = time.time()
        agent = await self._get_or_create_agent(session_id, user_id)
        try:
            # 保存用户消息到数据库
            await chat_db_service.save_message(
                session_id=session_id,
                role="user",
                content=message,
                source="user",
                model_name=self.model_name
            )

            # 使用 run_stream 方法获取流式响应
            stream = agent.run_stream(task=message)
            assistant_content = ""  # 收集完整的助手回复

            async for item in stream:
                if isinstance(item, ModelClientStreamingChunkEvent):
                    # 流式输出的文本块
                    chunk_content = item.content
                    assistant_content += chunk_content

                    yield {
                        "type": "chat_message",
                        "content": item.content,
                        "source": getattr(item, 'source', 'unknown')
                    }
            # 流式完成后，保存完整的助手回复到数据库
            if assistant_content:
                processing_time = time.time() - start_time

                # 保存原始内容到数据库
                await chat_db_service.save_message(
                    session_id=session_id,
                    role="assistant",
                    content=assistant_content,
                    source="chat_assistant",
                    model_name=self.model_name,
                    processing_time=processing_time,
                    finish_reason="stop"
                )
        except Exception as e:
            yield {
                "type": "error",
                "content": f"\n\n抱歉，处理您的请求时出现了错误：{str(e)}",
                "source": "system"
            }

    async def chat(self, message: str, user_id: int, session_id: str = "default") -> str:
        """非流式聊天"""
        start_time = time.time()
        agent = await self._get_or_create_agent(session_id, user_id)
        try:
            # 保存用户消息到数据库
            await chat_db_service.save_message(
                session_id=session_id,
                role="user",
                content=message,
                source="user",
                model_name=self.model_name
            )
            # 不需要将执行的过程展示给用户
            result = await agent.run(task=message)
            # 获取最后一条助手消息
            assistant_content = "抱歉，没有收到有效的回复。"
            for msg in reversed(result.messages):
                if isinstance(msg, TextMessage) and msg.source == "chat_assistant":
                    assistant_content = msg.content
                    break

            # 保存助手回复到数据库
            processing_time = time.time() - start_time
            await chat_db_service.save_message(
                session_id=session_id,
                role="assistant",
                content=assistant_content,
                source="chat_assistant",
                model_name=self.model_name,
                processing_time=processing_time,
                finish_reason="stop"
            )
            return assistant_content
        except Exception as e:
            return f"抱歉，处理您的请求时出现了错误：{str(e)}"
