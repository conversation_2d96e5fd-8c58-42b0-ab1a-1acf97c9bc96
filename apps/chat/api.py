# @Time: 2025/6/18 11:09
# @Author: lvjing
# 设置日志
import json
import logging
import uuid
from typing import AsyncGenerator, Optional

from fastapi.responses import StreamingResponse

from apps.chat.agent.chat_service import ChatService
from apps.chat.curd import chat_db_service
from apps.chat.schemas import ChatRequest, ChatResponse, SessionResponse, SessionListRequest, MessageResponse, \
    SessionUpdateRequest
from common.utils import response_fail, response_success, get_caller

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
from fastapi import APIRouter, Depends

router = APIRouter(prefix="/api/agent", tags=['AI对话模块'])


@router.post("/chat/stream", summary="大模型对话流式输出接口")
async def chat_stream(request: ChatRequest, user_id=Depends(get_caller)):
    """流式聊天接口"""
    chat_service = ChatService(request.model_id)

    async def generate_response() -> AsyncGenerator[str, None]:
        try:
            # 发送开始事件
            yield f"data: {json.dumps({'type': 'start', 'content': '', 'finished': False})}\n\n"

            session_id = str(uuid.uuid4())
            # 获取流式响应
            async for event in chat_service.chat_stream(request.message, user_id, session_id):
                # 直接传递事件数据，添加finished字段
                event["finished"] = False
                yield f"data: {json.dumps(event)}\n\n"
            # 发送结束事件
            yield f"data: {json.dumps({'type': 'end', 'content': '', 'finished': True})}\n\n"
        except Exception as e:
            error_data = {
                "type": "error",
                "content": f"Error: {str(e)}",
                "finished": True
            }
            yield f"data: {json.dumps(error_data)}\n\n"

    return StreamingResponse(
        generate_response(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )


@router.post("/chat", summary="大模型对话非流式输出接口")
async def chat(request: ChatRequest, user_id=Depends(get_caller)):
    """非流式聊天接口"""
    chat_service = ChatService(request.model_id)
    try:
        session_id = str(uuid.uuid4())

        response = await chat_service.chat(request.message, user_id, session_id)
        return response_success(data=ChatResponse(content=response, finished=True))
    except Exception as e:
        return response_fail(msg=str(e))


@router.post("/sessions", summary="获取用户对话列表")
async def get_user_sessions(request: SessionListRequest):
    """获取用户会话列表"""
    try:
        sessions = await chat_db_service.get_user_sessions(
            user_id=request.user_id,
            limit=request.limit,
            offset=request.offset,
            active_only=request.active_only
        )
        data = [
            SessionResponse(
                session_id=session.session_id,
                title=session.title or f"对话 {session.session_id}",
                user_id=session.user_id,
                agent_name=session.agent_name,
                total_messages=session.total_messages,
                last_message_at=session.last_message_at.isoformat() if session.last_message_at else None,
                created_time=session.created_time.isoformat(),
                is_active=session.is_active
            )
            for session in sessions
        ]
        return response_success(data=data, total=len(sessions))
    except Exception as e:
        return response_fail(msg=str(e))


@router.get("/sessions/{session_id}/messages", summary="根据会话id获取对话历史消息")
async def get_session_messages(
        session_id: str,
        limit: int = 50,
        offset: int = 0,
        role_filter: Optional[str] = None
):
    """获取会话消息历史"""
    try:
        messages = await chat_db_service.get_session_messages(
            session_id=session_id,
            limit=limit,
            offset=offset,
            role_filter=role_filter
        )

        return response_success(data=[
            MessageResponse(
                id=msg.id,
                role=msg.role,
                content=msg.content,
                message_type=msg.message_type,
                source=msg.source,
                created_time=msg.created_time.isoformat(),
                processing_time=msg.processing_time
            )
            for msg in messages
        ])
    except Exception as e:
        return response_fail(msg=str(e))


@router.get("/sessions/{session_id}", summary="获取会话信息")
async def get_session_info(session_id: str):
    """获取会话信息"""
    try:
        session = await chat_db_service.get_session_info(session_id)
        if not session:
            return response_fail(msg="会话不存在")

        return SessionResponse(
            session_id=session.session_id,
            title=session.title or f"对话 {session.session_id}",
            user_id=session.user_id,
            agent_name=session.agent_name,
            total_messages=session.total_messages,
            last_message_at=session.last_message_at.isoformat() if session.last_message_at else None,
            created_time=session.created_time.isoformat(),
            is_active=session.is_active
        )
    except Exception as e:
        return response_fail(msg=str(e))


@router.put("/sessions/{session_id}", summary="更新会话信息")
async def update_session(session_id: str, request: SessionUpdateRequest):
    """更新会话信息"""
    try:
        if request.title:
            success = await chat_db_service.update_session_title(session_id, request.title)
            if not success:
                return response_fail(msg="会话不存在")

        return {"message": "会话更新成功"}
    except Exception as e:
        return response_fail(msg=str(e))


@router.delete("/sessions/{session_id}", summary="删除会话")
async def delete_session(session_id: str):
    """删除会话"""
    try:
        success = await chat_db_service.delete_session(session_id)
        if not success:
            return response_fail(msg="会话不存在")
        return {"message": "会话删除成功"}

    except Exception as e:
        return response_fail(msg=str(e))


@router.post("/sessions/{session_id}/deactivate", summary="停用会话")
async def deactivate_session(session_id: str):
    """停用会话"""
    try:
        success = await chat_db_service.deactivate_session(session_id)
        if not success:
            return response_fail(msg="会话不存在")
        return {"message": "会话已停用"}
    except Exception as e:
        return response_fail(msg=str(e))
