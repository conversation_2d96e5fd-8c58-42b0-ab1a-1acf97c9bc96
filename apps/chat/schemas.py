# @Time: 2025/6/18 11:10
# @Author: lvjing
from typing import Optional

from pydantic import BaseModel


class ChatRequest(BaseModel):
    """
    AI对话请求参数
    """
    model_id: int
    message: str


class ChatResponse(BaseModel):
    """
    AI对话响应内容
    """
    content: str
    type: str = "text"
    finished: bool = False


class SessionListRequest(BaseModel):
    """
    会话列表请求参数
    """
    user_id: str
    limit: int = 20
    offset: int = 0
    active_only: bool = True


class SessionResponse(BaseModel):
    """
    会话信息响应
    """
    session_id: str
    title: str
    user_id: Optional[str] = None
    agent_name: str
    total_messages: int
    last_message_at: Optional[str] = None
    created_time: str
    is_active: bool


class MessageResponse(BaseModel):
    """
    消息响应
    """
    id: int
    role: str
    content: str
    message_type: str
    source: Optional[str] = None
    created_time: str
    processing_time: Optional[float] = None


class SessionHistoryRequest(BaseModel):
    """
    会话历史请求参数
    """
    session_id: str
    limit: int = 50
    offset: int = 0
    role_filter: Optional[str] = None


class SessionUpdateRequest(BaseModel):
    """
    会话更新请求参数
    """
    session_id: str
    title: Optional[str] = None


class SessionStatsResponse(BaseModel):
    """
    会话统计响应
    """
    session_id: str
    total_messages: int
    user_messages: int
    assistant_messages: int
    created_time: str
    last_message_at: Optional[str] = None
    duration_minutes: float
