
from typing import Optional, Union
from pydantic import BaseModel, Field


# class PromptAdd(BaseModel):
#     template_name: Optional[str] = None
#     template_content: Optional[str] = None
#     description: Optional[str] = None
class PromptAdd(BaseModel):
    template_name: str = Field(..., min_length=1, max_length=50)
    template_content: Union[str, dict] = Field(...)
    description: str = Field(None, max_length=200)

class PromptUpdate(BaseModel):
    id: int
    template_name: Optional[str] = None
    template_content: Optional[str] = None
    # update_time: Optional[datetime] = None
    description: Optional[str] = None

prompt_resp_keys = [
    "id",
    "template_name",
    "template_content",
    "creator_id",
]
