from tortoise import models, fields

class PromptModel(models.Model):
    """
    提示词表
    """
    id = fields.IntField(pk=True, auto_increment=True)
    template_name = fields.CharField(max_length=2048, description="模板名称")
    template_content = fields.TextField(description="模版内容")
    # is_deleted = fields.BooleanField(
    #     default=False,
    #     description="是否已删除（0:正常 1:删除）"
    # )
    creator = fields.ForeignKeyField(
        'models.UsersModel',
        related_name="prompt",
        on_delete=fields.SET_NULL,
        null=True,
        description="创建人"
    )
    create_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    update_user = fields.ForeignKeyField(
        'models.UsersModel',
        related_name="update_prompt",
        on_delete=fields.SET_NULL,
        null=True,
        description="更新人"
    )
    update_time = fields.DatetimeField(auto_now=True, description="更新时间")
    description = fields.TextField(description="描述")
    is_admin_template = fields.BooleanField(default=False)

    class Meta:
        table = "prompt"
        table_description = "提示词模版表"
        ordering = ['-id']