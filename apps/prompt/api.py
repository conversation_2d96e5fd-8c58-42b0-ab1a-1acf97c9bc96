import json
from datetime import datetime
from http.client import HTTPException
from fastapi import APIRouter, Request, Depends
from pydantic import BaseModel, ValidationError
from starlette import status
from starlette.responses import JSONResponse
from tortoise.exceptions import Does<PERSON>otExist, IntegrityError
from tortoise.expressions import Q

from apps import prompt
from apps.prompt.models import PromptModel
from apps.prompt.schemas import PromptAdd, PromptUpdate
from apps.users.models import UsersModel
from common.utils import response_success, response_fail, get_caller_id, get_caller

prompt_router = APIRouter(prefix="/prompt")
@prompt_router.post("",
                    summary="新增提示词模板",
                    tags=['提示词'])
async def add_prompt(prompt: PromptAdd, user=Depends(get_caller)):
    """
    创建支持HTML内容的提示词模板

    参数:
    - prompt: 包含模板信息的请求体
    - user: 通过依赖注入获取的当前用户
    """
    try:
        # 处理HTML内容
        content = prompt.template_content
        if isinstance(content, dict):
            content = json.dumps(content)

        new_prompt = await PromptModel.create(
            template_name=prompt.template_name,
            template_content=content,
            creator_id=user.id,
            description=prompt.description
        )

        return response_success(
            msg = "模板创建成功",
            data={"id": new_prompt.id}
        )


    except ValidationError as e:
        return response_success(
            success=False,
            data={"detail": str(e)},
            msg="参数验证失败"
        )

    except IntegrityError:
        return response_success(
            success=False,
            data={"detail": "模板名称已存在"},
            msg="创建失败"
        )

    except Exception as e:
        return response_success(
            success=False,
            data={"detail": f"服务器错误: {str(e)}"},
            msg="系统异常"
        )
# 查询全部模板
class PromptResponse(BaseModel):
    id: int
    template_name: str
    template_content: str
    # is_deleted: bool
    creator_id: int
    is_admin: bool
    description: str

@prompt_router.get("", summary="获取所有提示词模板", tags=['提示词'])
# async def get_all_prompts():
#     prompts = await PromptModel.all()
#     return response_success(data=[PromptResponse(**dict(prompt)).dict() for prompt in prompts])
async def get_filter_prompts(current_user: UsersModel = Depends(get_caller)):
    query = Q(creator__role=1) | Q(creator_id=current_user.id)
    prompts = await PromptModel.filter(query).prefetch_related("creator").all()

    # 构造响应数据.
    response_data = []
    for prompt in prompts:
        is_admin = prompt.creator.role == 1 if prompt.creator else False
        response_data.append(
            PromptResponse(
                id=prompt.id,
                template_name=prompt.template_name,
                template_content=prompt.template_content,
                creator_id=prompt.creator.id if prompt.creator else 0,
                is_admin=is_admin,
                is_admin_template=is_admin,
                description=prompt.description if hasattr(prompt, 'description') else ""
            ).dict()
        )

    return response_success(data=response_data)



# 根据ID查询模板
@prompt_router.get("/{id}", summary="根据ID获取提示词模板", tags=['提示词'])
async def get_prompt_by_id(id: int):
    prompt = await PromptModel.get_or_none(id=id)
    if not prompt:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="提示词模板不存在"
        )
    # return response_success(data=dict(prompt))
    return response_success(data=[PromptResponse(**dict(prompt))])


# 编辑模板
@prompt_router.put("", summary="修改提示词模板", tags=['提示词'])
async def update_prompt(prompt: PromptUpdate, request: Request):
    caller_id = get_caller_id(request)
    print(caller_id)
    try:
        old_prompt = await PromptModel.get(id=prompt.id)
        if old_prompt.creator_id != caller_id:
            return response_fail(msg="无权限")
    except DoesNotExist:
        return response_fail(msg="提示词模板不存在")
    update_data = {
        "template_name": prompt.template_name or old_prompt.template_name,
        "template_content": prompt.template_content or old_prompt.template_content,
        "update_time": datetime.now(),  # 服务端自动填充时间
        "update_user_id": caller_id,
        "description": prompt.description
    }

    count = await PromptModel.filter(id=prompt.id).update(**update_data)
    if count == 0:
        return response_fail(msg="更新失败")

    return response_success(data={"id": prompt.id})




# 删除模板
@prompt_router.delete("/{id}", summary="删除提示词模板", tags=['提示词'])
async def delete_prompt(id: int, request: Request):
    caller_id = get_caller_id(request)
    try:
        old_prompt = await PromptModel.get(id=id)
        if old_prompt.creator_id != caller_id:
            return response_fail(msg="无权限")
    except DoesNotExist:
        return response_fail(msg="提示词模板不存在")

    await old_prompt.delete()
    return response_success(data={"id": id})



