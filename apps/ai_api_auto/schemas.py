# @Time: 2025/8/14 15:08
# @Author: lvjing
import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List

from pydantic import BaseModel, Field

from apps.ai_api_auto.enums import HttpMethod, DataType, ParameterLocation


class YapiBaseInfoAdd(BaseModel):
    """创建YAPI基础信息请求模型"""
    yapi_base_url: str = Field(..., description="YAPI基础地址", max_length=255)
    yapi_token: str = Field(..., description="YAPI token认证", max_length=255)
    description: Optional[str] = Field(None, description="描述", max_length=255)
    project_id: Optional[int] = Field(None, description="项目ID")


class YapiBaseInfoUpdate(BaseModel):
    """更新YAPI基础信息请求模型"""
    id: int = Field(..., description="主键ID")
    yapi_base_url: str = Field(..., description="YAPI基础地址", max_length=255)
    yapi_token: str = Field(..., description="YAPI token认证", max_length=255)
    description: Optional[str] = Field(None, description="描述", max_length=255)
    project_id: Optional[int] = Field(None, description="项目ID")


class YapiBaseInfoResponse(BaseModel):
    """YAPI基础信息响应模型"""
    id: int
    yapi_base_url: str
    yapi_token: str
    description: Optional[str]
    project_id: Optional[int]
    project_name: Optional[str] = None
    creator_id: Optional[int]
    creator_name: Optional[str] = None
    updater_id: Optional[int]
    updater_name: Optional[str] = None
    create_time: datetime
    update_time: datetime


class YapiBaseInfoListResponse(BaseModel):
    """YAPI基础信息列表响应模型"""
    id: int
    yapi_base_url: str
    description: Optional[str]
    project_name: Optional[str] = None
    creator_name: Optional[str] = None
    create_time: datetime
    update_time: datetime


class YapiApiInfoAdd(BaseModel):
    """创建YAPI接口信息请求模型"""
    api_name: str = Field(..., description="接口名称", max_length=255)
    api_path: str = Field(..., description="接口请求地址", max_length=255)
    api_method: str = Field(..., description="接口请求方式", max_length=255)
    api_meta_data: dict = Field(..., description="接口元数据")
    yapi_base_id: int = Field(..., description="关联的YAPI基础信息ID")
    project_id: Optional[int] = Field(None, description="项目ID")


class YapiApiInfoUpdate(BaseModel):
    """更新YAPI接口信息请求模型"""
    id: int = Field(..., description="主键ID")
    api_name: str = Field(..., description="接口名称", max_length=255)
    api_path: str = Field(..., description="接口请求地址", max_length=255)
    api_method: str = Field(..., description="接口请求方式", max_length=255)
    api_meta_data: dict = Field(..., description="接口元数据")
    yapi_base_id: int = Field(..., description="关联的YAPI基础信息ID")
    project_id: Optional[int] = Field(None, description="项目ID")


class YapiApiInfoResponse(BaseModel):
    """YAPI接口信息响应模型"""
    id: int
    api_name: str
    api_path: str
    api_method: str
    api_meta_data: dict
    yapi_base_id: int
    yapi_base_url: Optional[str] = None
    project_id: Optional[int]
    project_name: Optional[str] = None
    creator_id: Optional[int]
    creator_name: Optional[str] = None
    updater_id: Optional[int]
    updater_name: Optional[str] = None
    create_time: datetime
    update_time: datetime


class YapiApiInfoListResponse(BaseModel):
    """YAPI接口信息列表响应模型"""
    id: int
    api_name: str
    api_path: str
    api_method: str
    yapi_base_id: int
    yapi_base_url: Optional[str] = None
    project_name: Optional[str] = None
    creator_name: Optional[str] = None
    create_time: datetime
    update_time: datetime


class YapiFetchRequest(BaseModel):
    """YAPI数据获取请求模型"""
    yapi_base_id: int = Field(..., description="YAPI基础信息ID")
    project_id: Optional[int] = Field(None, description="关联的项目ID")


class YapiFetchResponse(BaseModel):
    """YAPI数据获取响应模型"""
    success: bool = Field(..., description="是否成功")
    project_info: Optional[dict] = Field(None, description="项目信息")
    total_interfaces: int = Field(0, description="总接口数")
    saved_interfaces: int = Field(0, description="成功保存的接口数")
    failed_interfaces: int = Field(0, description="失败的接口数")
    errors: list = Field(default=list, description="错误信息列表")


class YapiProjectInfoResponse(BaseModel):
    """YAPI项目信息响应模型"""
    project_id: int = Field(..., description="项目ID")
    project_name: str = Field(..., description="项目名称")
    basepath: Optional[str] = Field(None, description="基础路径")
    project_type: Optional[str] = Field(None, description="项目类型")
    raw_data: dict = Field(..., description="原始数据")


class YapiInterfaceListResponse(BaseModel):
    """YAPI接口列表响应模型"""
    count: int = Field(..., description="总接口数")
    total: int = Field(..., description="总页数")
    current_page: int = Field(..., description="当前页")
    limit: int = Field(..., description="每页数量")
    interfaces: list = Field(..., description="接口列表")


class YapiInterfaceDetailResponse(BaseModel):
    """YAPI接口详情响应模型"""
    interface_id: int = Field(..., description="接口ID")
    api_path: str = Field(..., description="接口路径")
    api_name: str = Field(..., description="接口名称")
    api_method: str = Field(..., description="接口方法")
    status: Optional[str] = Field(None, description="接口状态")
    project_id: int = Field(..., description="项目ID")
    api_meta_data: dict = Field(..., description="接口元数据")
    raw_data: dict = Field(..., description="原始数据")


class ApiDocumentUploadAdd(BaseModel):
    """接口文档上传请求模型"""
    document_name: str = Field(..., description="文档名称", max_length=255)
    description: Optional[str] = Field(None, description="文档描述")
    document_category: str = Field("api_doc", description="文档分类")
    project_id: Optional[int] = Field(None, description="项目ID")


class ApiDocumentUploadResponse(BaseModel):
    """接口文档上传响应模型"""
    id: int
    document_name: str
    original_filename: str
    file_type: str
    file_size: int
    sso_file_id: str
    sso_file_url: Optional[str]
    document_category: str
    description: Optional[str]
    is_converted_to_pdf: bool
    converted_from_type: Optional[str]
    project_id: Optional[int]
    project_name: Optional[str] = None
    creator_id: Optional[int]
    creator_name: Optional[str] = None
    updater_id: Optional[int]
    updater_name: Optional[str] = None
    create_time: datetime
    update_time: datetime


class ApiDocumentUploadListResponse(BaseModel):
    """接口文档上传列表响应模型"""
    id: int
    document_name: str
    original_filename: str
    file_type: str
    file_size: int
    document_category: str
    description: Optional[str]
    is_converted_to_pdf: bool
    project_name: Optional[str] = None
    creator_name: Optional[str] = None
    create_time: datetime
    update_time: datetime


class ApiDocumentContentResponse(BaseModel):
    """接口文档内容响应模型"""
    id: int
    document_name: str
    original_filename: str
    file_type: str
    file_size: int
    sso_file_id: str
    content_type: str = Field(..., description="内容类型: text, binary")
    text_content: Optional[str] = Field(None, description="文本内容")
    has_binary_content: bool = Field(..., description="是否包含二进制内容")
    decode_error: Optional[str] = Field(None, description="解码错误信息")


# ============================================================================
# API文档内容解析智能体 - 输入输出模型
# ============================================================================
class ApiDocumentContentParseInput(BaseModel):
    """Api文档内容解析输入"""
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="会话ID")
    api_file_content: str = Field(None, description="API文件读取后的内容")


class ParsedApiInfo(BaseModel):
    """解析后的API信息"""
    title: str = Field(..., description="API标题")
    version: str = Field(..., description="API版本")
    description: str = Field("", description="API描述")
    base_url: str = Field("", description="基础URL")
    contact: Dict[str, str] = Field(default=dict, description="联系信息")
    license: Dict[str, str] = Field(default=dict, description="许可证信息")


class ApiParameter(BaseModel):
    """API参数"""
    name: str = Field(..., description="参数名称")
    location: ParameterLocation = Field(..., description="参数位置")
    data_type: DataType = Field(..., description="数据类型")
    required: bool = Field(False, description="是否必需")
    description: str = Field("", description="参数描述")
    example: Any = Field(None, description="示例值")
    constraints: Dict[str, Any] = Field(default=dict, description="参数约束")


class ApiResponse(BaseModel):
    """API响应"""
    status_code: str = Field(..., description="状态码")
    description: str = Field("", description="响应描述")
    content_type: str = Field("application/json", description="内容类型")
    response_schema: Dict[str, Any] = Field(default=dict, description="响应结构")
    example: Any = Field(None, description="响应示例")


class ParsedEndpoint(BaseModel):
    """解析后的API端点"""
    endpoint_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="端点ID")
    path: str = Field(..., description="API路径")
    method: HttpMethod = Field(..., description="HTTP方法")
    summary: str = Field("", description="端点摘要")
    description: str = Field("", description="端点描述")
    tags: List[str] = Field(default=list, description="标签")
    parameters: List[ApiParameter] = Field(default=list, description="参数列表")
    responses: List[ApiResponse] = Field(default=list, description="响应列表")
    auth_required: bool = Field(False, description="是否需要认证")
    deprecated: bool = Field(False, description="是否已废弃")

    # 扩展信息字段 - 用于传递更丰富的接口信息给智能体
    extended_info: Dict[str, Any] = Field(default=dict, description="扩展信息")
    raw_data: Dict[str, Any] = Field(default=dict, description="原始数据")
    security_schemes: Dict[str, Any] = Field(default=dict, description="安全方案")
    complexity_score: float = Field(0.0, description="复杂度评分")
    confidence_score: float = Field(0.0, description="置信度评分")

    # 接口分类和标识信息
    interface_name: str = Field("", description="接口名称")
    category: str = Field("", description="接口分类")
    auth_type: str = Field("", description="认证类型")


class ApiDocumentContentParseOutput(BaseModel):
    """Api文档内容解析输出"""
    session_id: str = Field(..., description="会话ID")
    api_info: ParsedApiInfo = Field(..., description="API基本信息")
    endpoints: List[ParsedEndpoint] = Field(default=list, description="端点列表")
    parse_errors: List[str] = Field(default=list, description="解析错误")
    parse_warnings: List[str] = Field(default=list, description="解析警告")
    processing_time: float = Field(0.0, description="处理时间")
    # 新增：扩展信息字段，保留大模型解析的丰富数据
    extended_info: Dict[str, Any] = Field(default=dict, description="扩展信息")
    raw_parsed_data: Dict[str, Any] = Field(default=dict, description="原始解析数据")


class FileUploadData(BaseModel):
    """文件上传数据模型"""
    file_name: str = Field(..., description="文件名")
    file_content: bytes = Field(..., description="文件内容的字节流")
    document_name: Optional[str] = Field(None, description="文档名称")
    description: Optional[str] = Field(None, description="文档描述")
    project_id: Optional[int] = Field(None, description="关联的项目ID")


class ApiParseRequest(BaseModel):
    """API解析请求的统一模型"""
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="会话ID")
    source_type: str = Field(..., description="解析来源类型: file_upload, oss_selection, project_id")
    file_upload_data: Optional[FileUploadData] = Field(None, description="文件上传时的数据")
    oss_file_id: Optional[str] = Field(None, description="从OSS选择文件时的文件ID")
    project_id: Optional[int] = Field(None, description="根据项目ID查询时的项目ID")
