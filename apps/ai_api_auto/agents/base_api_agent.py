# @Time: 2025/8/25 17:12
# @Author: lvjing
"""
API自动化智能体基类
提供公共的功能和方法，减少代码重复
"""
import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.messages import ModelClientStreamingChunkEvent
from autogen_core import MessageContext
from tortoise import Tortoise


from common import settings
from common.agent.base_agent import BaseAgent
from common.agent.types import AGENT_NAMES, AgentPlatform
from common.llms import get_model_client

logger = logging.getLogger(__name__)


class BaseApiAgent(BaseAgent):
    """
    API自动化智能体基类

    提供公共功能：
    1. 大模型调用和流式处理
    2. JSON数据提取和解析
    3. 错误处理和统计
    4. AssistantAgent管理
    """

    def __init__(self, agent_type=None, model_client_instance=None, agent_id=None, agent_name=None, **kwargs):
        """初始化基类"""
        # 处理参数兼容性
        if agent_type is not None:
            # 从AgentTypes获取agent_name
            if agent_id is None:
                agent_id = agent_type.value if hasattr(agent_type, 'value') else str(agent_type)
            if agent_name is None:
                agent_name = AGENT_NAMES.get(agent_type.value if hasattr(agent_type, 'value') else agent_type,
                                             str(agent_type))

        # 调用父类构造函数
        super().__init__(
            agent_id=agent_id,
            agent_name=agent_name,
            **kwargs
        )

        # 存储agent_type以供子类使用
        self.agent_type = agent_type

        # 初始化大模型客户端
        self.model_client = model_client_instance or get_model_client("deepseek")

        # AssistantAgent管理
        self.assistant_agent = None
        self._assistant_creation_pending = False

        # 公共统计指标
        self.common_metrics = {
            "total_requests": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "total_processing_time": 0.0,
            "avg_processing_time": 0.0
        }

        # 数据库连接状态（基类提供）
        self._db_initialized = False

    def _initialize_assistant_agent(self):
        from apps.ai_api_auto.factory import agent_factory
        """通过工厂创建AssistantAgent"""
        try:

            async def create_assistant():
                return await agent_factory.create_agent(
                    agent_type=self.agent_type.value,
                    platform=AgentPlatform.AUTO_GEN,
                    model_client_instance=self.model_client,
                    model_client_stream=True
                )

            # 异步上下文处理
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    self.assistant_agent = None
                    self._assistant_creation_pending = True
                else:
                    self.assistant_agent = loop.run_until_complete(create_assistant())
                    self._assistant_creation_pending = False
            except RuntimeError:
                self.assistant_agent = None
                self._assistant_creation_pending = True

            logger.info("AssistantAgent初始化配置完成")

        except Exception as e:
            logger.error(f"初始化AssistantAgent失败: {str(e)}")
            self._create_fallback_assistant_agent()

    def _create_fallback_assistant_agent(self):
        """创建备用AssistantAgent"""
        self.assistant_agent = AssistantAgent(
            name=f"{self.agent_type.value}_fallback",
            model_client=self.model_client,
            system_message="你是一个专业的API自动化助手。",
            model_client_stream=True
        )
        self._assistant_creation_pending = False

    async def _ensure_assistant_agent(self):
        from apps.ai_api_auto.factory import agent_factory
        """确保AssistantAgent已创建"""
        if self.assistant_agent is None or self._assistant_creation_pending:
            try:
                self.assistant_agent = await agent_factory.create_agent(
                    agent_type=self.agent_type.value,
                    platform=AgentPlatform.AUTO_GEN,
                    model_client_instance=self.model_client,
                    model_client_stream=True
                )
                self._assistant_creation_pending = False
                logger.info("AssistantAgent异步创建完成")

            except Exception as e:
                logger.error(f"异步创建AssistantAgent失败: {str(e)}")
                if self.assistant_agent is None:
                    self._create_fallback_assistant_agent()

    async def _run_assistant_agent(self, task: str, stream: bool = False) -> Optional[str]:
        """运行AssistantAgent获取结果"""
        try:
            await self._ensure_assistant_agent()

            if self.assistant_agent is None:
                logger.error("AssistantAgent未能成功创建")
                return None
            if stream:
                stream = self.assistant_agent.run_stream(task=task)
                result_content = ""
                async for event in stream:
                    if isinstance(event, ModelClientStreamingChunkEvent):
                        await self.send_response(event.content)
                        continue
                    if isinstance(event, TaskResult):
                        messages = event.messages
                        if messages and hasattr(messages[-1], 'content'):
                            result_content = messages[-1].content
                            break
            else:
                result = await self.assistant_agent.run(task=task)
                result_content = result.messages[-1].content if result.messages else ""

            return result_content

        except Exception as e:
            logger.error(f"运行AssistantAgent失败: {str(e)}")
            return None

    def _extract_json_from_content(self, content: str) -> Optional[Dict[str, Any]]:
        """从内容中提取JSON数据 - 严格版"""
        try:
            import re

            if not content or not content.strip():
                logger.warning("输入内容为空，无法提取JSON。")
                return None

            # 核心逻辑：严格查找并解析 ```json ... ``` 代码块
            match = re.search(r'```json\s*({.*?})\s*```', content, re.DOTALL)

            if match:
                json_str = match.group(1).strip()
                try:
                    # 尝试解析提取到的字符串为JSON
                    parsed_json = json.loads(json_str)
                    logger.info("成功从```json ...```代码块中提取并解析了JSON。")
                    return parsed_json
                except json.JSONDecodeError as e:
                    logger.error(f"在```json ...```代码块中找到的内容无法解析为JSON: {e}")
                    logger.debug(f"无法解析的JSON字符串: {json_str}")
                    return None
            else:
                # 如果严格匹配失败，记录警告并返回None
                logger.warning("在模型响应中未找到严格匹配的```json ...```代码块。")
                # 作为调试辅助，我们尝试调用旧的、更宽松的提取器，但只记录其结果
                fallback_result = self._extract_complete_json_object(content)
                if fallback_result:
                    logger.warning(
                        f"备用提取器找到了一个JSON对象，键: {list(fallback_result.keys())}。但这不符合严格的格式要求。")
                return None

        except Exception as e:
            logger.error(f"提取JSON时发生意外错误: {str(e)}")
            return None

    def _extract_complete_json_object(self, content: str) -> Optional[Dict[str, Any]]:
        """智能提取完整的JSON对象，支持复杂嵌套结构"""
        try:
            # 查找所有可能的JSON起始位置
            start_positions = []
            for i, char in enumerate(content):
                if char == '{':
                    start_positions.append(i)

            # 收集所有有效的JSON对象
            valid_json_objects = []

            # 按起始位置尝试提取JSON
            for start_pos in start_positions:
                json_str = self._extract_balanced_json(content, start_pos)
                if json_str:
                    try:
                        parsed_json = json.loads(json_str)
                        # 验证这是一个有意义的JSON对象（不是空对象或只有一个简单字段）
                        if isinstance(parsed_json, dict) and len(parsed_json) > 0:
                            valid_json_objects.append({
                                'json': parsed_json,
                                'size': len(json_str),
                                'keys': list(parsed_json.keys()),
                                'start_pos': start_pos
                            })
                    except json.JSONDecodeError:
                        continue

            if not valid_json_objects:
                return None

            # 增强的JSON选择逻辑：根据特定键来确定JSON对象的类型

            # 优先级1: API文档解析结果 (api_info 和 endpoints)
            doc_parser_objects = [
                obj for obj in valid_json_objects
                if 'api_info' in obj['keys'] and 'endpoints' in obj['keys']
            ]
            if doc_parser_objects:
                best_object = max(doc_parser_objects, key=lambda x: x['size'])
                logger.info(f"选择API文档解析结果JSON对象，大小: {best_object['size']}, 键: {best_object['keys']}")
                return best_object['json']

            # 优先级2: 测试用例生成结果 (test_cases)
            test_cases_objects = [obj for obj in valid_json_objects if 'test_cases' in obj['keys']]
            if test_cases_objects:
                best_object = max(test_cases_objects, key=lambda x: x['size'])
                logger.info(f"选择测试用例JSON对象，大小: {best_object['size']}, 键: {best_object['keys']}")
                return best_object['json']

            # 优先级3: 脚本生成结果 (scripts)
            script_gen_objects = [obj for obj in valid_json_objects if 'scripts' in obj['keys']]
            if script_gen_objects:
                best_object = max(script_gen_objects, key=lambda x: x['size'])
                logger.info(f"选择脚本生成结果JSON对象，大小: {best_object['size']}, 键: {best_object['keys']}")
                return best_object['json']

            # 备用逻辑: 如果没有特定匹配，选择最大的JSON对象
            best_object = max(valid_json_objects, key=lambda x: x['size'])
            logger.info(f"选择最大的JSON对象 (备用逻辑)，大小: {best_object['size']}, 键: {best_object['keys']}")
            return best_object['json']

        except Exception as e:
            logger.error(f"智能JSON提取失败: {str(e)}")
            return None

    def _extract_balanced_json(self, content: str, start_pos: int) -> Optional[str]:
        """从指定位置提取平衡的JSON字符串"""
        try:
            if start_pos >= len(content) or content[start_pos] != '{':
                return None

            brace_count = 0
            in_string = False
            escape_next = False

            for i in range(start_pos, len(content)):
                char = content[i]

                if escape_next:
                    escape_next = False
                    continue

                if char == '\\':
                    escape_next = True
                    continue

                if char == '"' and not escape_next:
                    in_string = not in_string
                    continue

                if not in_string:
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            # 找到完整的JSON对象
                            json_str = content[start_pos:i + 1]
                            return json_str.strip()

            return None

        except Exception as e:
            logger.error(f"提取平衡JSON失败: {str(e)}")
            return None

    def _update_metrics(self, operation_type: str, success: bool, processing_time: float = 0.0):
        """更新统计指标"""
        self.common_metrics["total_requests"] += 1

        if success:
            self.common_metrics["successful_operations"] += 1
        else:
            self.common_metrics["failed_operations"] += 1

        if processing_time > 0:
            self.common_metrics["total_processing_time"] += processing_time
            self.common_metrics["avg_processing_time"] = (
                    self.common_metrics["total_processing_time"] /
                    self.common_metrics["total_requests"]
            )

    def _handle_common_error(self, error: Exception, operation: str) -> Dict[str, Any]:
        """公共错误处理"""
        error_info = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "operation": operation,
            "timestamp": datetime.now().isoformat()
        }

        logger.error(f"{operation}失败: {error_info}")
        return error_info

    async def _ensure_database_connection(self):
        """确保数据库连接已建立（基类方法）"""
        if not self._db_initialized:
            try:
                # 检查Tortoise是否已初始化
                if not Tortoise._inited:
                    logger.info(f"[{self.agent_name}] 初始化Tortoise ORM连接...")

                    await Tortoise.init(config=settings.TORTOISE_ORM)
                    logger.info(f"[{self.agent_name}] Tortoise ORM连接初始化成功")

                self._db_initialized = True

            except Exception as e:
                logger.error(f"[{self.agent_name}] 数据库连接初始化失败: {str(e)}")
                raise

    async def process_message(self, message: Any, ctx: MessageContext) -> None:
        """处理消息的默认实现 - 子类可以重写此方法"""
        # 这是一个默认实现，子类可以根据需要重写
        logger.info(f"[{self.agent_name}] 收到消息: {message}")
        # 子类应该重写此方法来处理具体的消息逻辑
