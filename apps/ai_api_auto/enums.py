# @Time: 2025/8/25 14:19
# @Author: lvjing
from enum import Enum


class HttpMethod(str, Enum):
    """HTTP方法枚举"""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    HEAD = "HEAD"


class DocumentFormat(str, Enum):
    """文档格式"""
    AUTO = "auto"
    OPENAPI = "openapi"
    SWAGGER = "swagger"
    POSTMAN = "postman"
    PDF = "pdf"
    MARKDOWN = "markdown"
    UNKNOWN = "unknown"


class ParameterLocation(str, Enum):
    """参数位置"""
    QUERY = "query"
    PATH = "path"
    HEADER = "header"
    BODY = "body"
    FORM = "form"
    COOKIE = "cookie"


class DataType(str, Enum):
    """数据类型"""
    STRING = "string"
    INTEGER = "integer"
    NUMBER = "number"
    BOOLEAN = "boolean"
    ARRAY = "array"
    OBJECT = "object"
