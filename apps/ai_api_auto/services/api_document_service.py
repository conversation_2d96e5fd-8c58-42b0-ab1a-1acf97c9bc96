# @Time: 2025/8/25 16:00
# @Author: lvjing

"""
API文档服务

负责处理API文档的上传、数据库记录和业务逻辑
"""
import logging
import os
from typing import Dict, Any

from fastapi import UploadFile

from apps.ai_api_auto.models import ApiDocumentUploadModel
from common.service.file_upload_service import FileUploadService, FileUploadServiceError

logger = logging.getLogger(__name__)


class ApiDocumentService:
    """
    API文档服务类
    """

    def __init__(self):
        """初始化API文档服务"""
        self.file_upload_service = FileUploadService()

    async def upload_document(
            self,
            file: UploadFile,
            document_data: Dict[str, Any],
            current_user: Any
    ) -> ApiDocumentUploadModel:
        """
        处理API文档上传

        1. 上传文件到OSS
        2. 保存文档记录到数据库
        """
        try:
            # 检查文件大小
            max_size = 10 * 1024 * 1024  # 10MB
            file_content = await file.read()
            if len(file_content) > max_size:
                raise FileUploadServiceError("文件大小超过10MB限制")

            logger.info(f"用户 {current_user.username} 上传接口文档: {file.filename}, 大小: {len(file_content)} bytes")

            # 上传文件到OSS
            upload_result = await self.file_upload_service.upload_file(
                file=file_content,
                filename=file.filename,
                convert_office_to_pdf=document_data.get("convert_office_to_pdf", True)
            )

            if not upload_result.get("success"):
                raise FileUploadServiceError(f"文件上传失败: {upload_result.get('error', '未知错误')}")

            logger.info(f"文件上传结果: {upload_result}")

            # 保存文档信息到数据库
            document_record = await self._save_document_record(
                file, upload_result, document_data, current_user
            )

            return document_record

        except Exception as e:
            logger.error(f"上传接口文档失败: {e}", exc_info=True)
            raise

    async def _save_document_record(
            self,
            file: UploadFile,
            upload_result: Dict[str, Any],
            document_data: Dict[str, Any],
            current_user: Any
    ) -> ApiDocumentUploadModel:
        """
        保存文档记录到数据库
        """
        final_document_name = document_data.get("document_name") or file.filename
        original_filename = file.filename or "unknown"
        file_extension = os.path.splitext(original_filename)[1].lstrip('.').lower() if original_filename else "unknown"

        document_record = await ApiDocumentUploadModel.create(
            document_name=final_document_name,
            original_filename=original_filename,
            file_type=file_extension,
            file_size=upload_result.get("file_size", 0),
            oss_file_id=upload_result.get("file_id"),
            oss_file_url=upload_result.get("file_url"),
            document_category=document_data.get("document_category", "api_doc"),
            description=document_data.get("description"),
            is_converted_to_pdf=upload_result.get("converted_to_pdf", False),
            converted_from_type=upload_result.get("original_type"),
            project_id=document_data.get("project_id"),
            creator_id=current_user.id
        )

        return document_record

    async def get_document_detail(
            self,
            document_id: int,
            include_access_url: bool = True,
            expires: int = 7200
    ) -> Dict[str, Any]:
        """
        获取文档详情，包含访问链接
        """
        try:
            # 查询文档记录
            document = await ApiDocumentUploadModel.get(id=document_id).select_related(
                'project', 'creator', 'updater'
            )

            # 基础文档信息
            result_data = {
                "id": document.id,
                "document_name": document.document_name,
                "original_filename": document.original_filename,
                "file_type": document.file_type,
                "file_size": document.file_size,
                "oss_file_id": document.oss_file_id,
                "oss_file_url": document.oss_file_url,
                "document_category": document.document_category,
                "description": document.description,
                "is_converted_to_pdf": document.is_converted_to_pdf,
                "converted_from_type": document.converted_from_type,
                "project_id": document.project_id,
                "project_name": document.project.name if document.project else None,
                "creator_id": document.creator_id,
                "creator_name": document.creator.username if document.creator else None,
                "updater_id": document.updater_id,
                "updater_name": document.updater.username if document.updater else None,
                "create_time": document.create_time,
                "update_time": document.update_time
            }

            # 生成访问链接
            if include_access_url and document.oss_file_id:
                try:
                    url_result = await self.file_upload_service.get_file_access_url(
                        document.oss_file_id, expires
                    )

                    if url_result.get("success"):
                        # 判断文件类型以提供预览建议
                        file_extension = os.path.splitext(document.original_filename or "")[1].lower()
                        preview_info = self._get_preview_info(file_extension)

                        result_data.update({
                            "access_url": url_result["presigned_url"],
                            "access_expires_in": url_result["expires_in"],
                            "access_expires_at": url_result["expires_at"],
                            **preview_info
                        })
                    else:
                        logger.warning(f"生成文档访问链接失败: {url_result.get('error')}")
                        result_data["access_url_error"] = url_result.get('error', '生成访问链接失败')

                except Exception as e:
                    logger.error(f"生成文档访问链接异常: {e}")
                    result_data["access_url_error"] = f"生成访问链接异常: {str(e)}"

            return result_data

        except Exception as e:
            logger.error(f"获取文档详情失败: {e}", exc_info=True)
            raise

    def _get_preview_info(self, file_extension: str) -> Dict[str, Any]:
        """
        根据文件扩展名获取预览信息
        """
        preview_type = "unknown"
        can_preview_in_browser = False

        if file_extension == '.pdf':
            preview_type = "pdf"
            can_preview_in_browser = True
        elif file_extension in ['.txt', '.md', '.json', '.xml', '.csv']:
            preview_type = "text"
            can_preview_in_browser = True
        elif file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
            preview_type = "image"
            can_preview_in_browser = True
        elif file_extension in ['.docx', '.doc']:
            preview_type = "word"
        elif file_extension in ['.xlsx', '.xls']:
            preview_type = "excel"
        elif file_extension in ['.pptx', '.ppt']:
            preview_type = "powerpoint"

        return {
            "preview_type": preview_type,
            "can_preview_in_browser": can_preview_in_browser
        }

    async def download_document(self, document_id: int) -> Dict[str, Any]:
        """
        下载文档内容
        """
        try:
            # 获取文档信息
            document = await ApiDocumentUploadModel.get(id=document_id)

            # 从OSS读取文件内容
            content_result = await self.file_upload_service.read_file_content(document.oss_file_id)

            if not content_result.get("success"):
                raise FileUploadServiceError(f"读取文档内容失败: {content_result.get('error', '未知错误')}")

            # 获取文件内容
            file_content = content_result.get("content")
            if not file_content:
                raise FileUploadServiceError("文件内容为空")

            # 确定文件名和MIME类型
            filename = document.original_filename or document.document_name
            content_type = self._get_content_type(filename)

            return {
                "success": True,
                "filename": filename,
                "content_type": content_type,
                "file_content": file_content,
                "file_size": len(file_content)
            }

        except Exception as e:
            logger.error(f"下载文档失败: {e}", exc_info=True)
            raise

    async def delete_document(self, document_id: int) -> Dict[str, Any]:
        """
        删除文档（包括OSS文件和数据库记录）
        """
        try:
            # 获取文档信息
            document = await ApiDocumentUploadModel.get(id=document_id)

            # 从OSS删除文件
            if document.oss_file_id:
                delete_result = await self.file_upload_service.delete_file(document.oss_file_id)
                if not delete_result.get("success"):
                    logger.warning(f"OSS文件删除失败: {delete_result.get('error', '未知错误')}")
                    # 注意：即使OSS删除失败，我们仍然继续删除数据库记录

            # 删除数据库记录
            count = await ApiDocumentUploadModel.filter(id=document_id).delete()

            return {
                "success": True,
                "deleted_count": count,
                "document_name": document.document_name
            }

        except Exception as e:
            logger.error(f"删除文档失败: {e}", exc_info=True)
            raise

    async def batch_delete_documents(self, document_ids: list) -> Dict[str, Any]:
        """
        批量删除文档
        """
        try:
            # 获取要删除的文档信息
            documents = await ApiDocumentUploadModel.filter(id__in=document_ids)
            if not documents:
                raise ValueError("未找到要删除的文档")

            success_count = 0
            failed_count = 0
            failed_files = []

            # 逐个删除文档和OSS文件
            for document in documents:
                try:
                    # 从OSS删除文件
                    if document.oss_file_id:
                        delete_result = await self.file_upload_service.delete_file(document.oss_file_id)
                        if not delete_result.get("success"):
                            logger.warning(
                                f"OSS文件删除失败: {document.document_name} - {delete_result.get('error', '未知错误')}")

                    # 删除数据库记录
                    await ApiDocumentUploadModel.filter(id=document.id).delete()
                    success_count += 1
                    logger.info(f"成功删除文档: {document.document_name}")

                except Exception as e:
                    failed_count += 1
                    failed_files.append(f"{document.document_name}: {str(e)}")
                    logger.error(f"删除文档失败: {document.document_name} - {str(e)}")

            return {
                "success": True,
                "success_count": success_count,
                "failed_count": failed_count,
                "failed_files": failed_files
            }

        except Exception as e:
            logger.error(f"批量删除文档失败: {e}", exc_info=True)
            raise

    async def get_document_text_content(self, document_id: int) -> str:
        """
        获取文档的文本内容（主要用于PDF文件）
        
        Args:
            document_id: 文档ID
            
        Returns:
            str: 提取的文本内容
            
        Raises:
            FileUploadServiceError: 当文件读取或文本提取失败时
        """
        try:
            # 获取文档信息
            document = await ApiDocumentUploadModel.get(id=document_id)
            
            logger.info(f"开始获取文档文本内容: {document.document_name} (ID: {document_id})")
            
            # 从OSS读取文件内容，确保获取文本内容
            content_result = await self.file_upload_service.read_file_content(document.oss_file_id)
            
            if not content_result.get("success"):
                raise FileUploadServiceError(f"读取文档内容失败: {content_result.get('error', '未知错误')}")
            
            # 获取提取的文本内容
            text_content = content_result.get("text_content")
            if not text_content:
                # 如果没有文本内容，检查是否有错误信息
                decode_error = content_result.get("decode_error")
                if decode_error:
                    raise FileUploadServiceError(f"文本提取失败: {decode_error}")
                else:
                    raise FileUploadServiceError("无法从文档中提取文本内容")
            
            logger.info(f"成功提取文档文本内容，长度: {len(text_content)} 字符")
            return text_content
            
        except Exception as e:
            logger.error(f"获取文档文本内容失败: {e}", exc_info=True)
            raise

    def _get_content_type(self, filename: str) -> str:
        """
        根据文件名获取MIME类型
        """
        file_extension = os.path.splitext(filename)[1].lower()
        mime_types = {
            '.pdf': 'application/pdf',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.doc': 'application/msword',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.xls': 'application/vnd.ms-excel',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.ppt': 'application/vnd.ms-powerpoint',
            '.txt': 'text/plain',
            '.json': 'application/json',
            '.xml': 'application/xml',
            '.csv': 'text/csv'
        }
        return mime_types.get(file_extension, 'application/octet-stream')
