# @Time: 2025/8/26 08:57
# @Author: lvjing
"""
接口自动化智能体编排服务
负责协调各个智能体的工作流程，使用新的数据模型
"""
import logging
from datetime import datetime
from typing import Optional, Dict, Any

from autogen_core import SingleThreadedAgentRuntime, TopicId

from apps.ai_api_auto.factory import agent_factory
from apps.ai_api_auto.schemas import ApiDocumentContentParseInput
from common.agent.stream_response_collector import StreamResponseCollector
from common.agent.types import AgentPlatform, TopicTypes

logger = logging.getLogger(__name__)


class ApiAutomationOrchestrator:
    """
    接口自动化智能体编排器

    负责协调以下智能体的工作流程：
    1. API文档解析智能体 - 解析API文档，输出 DocumentParseOutput

    数据流转：DocumentParseInput
    """

    def __init__(self, collector: Optional[StreamResponseCollector] = None):
        """
        初始化接口自动化编排器

        Args:
            collector: 可选的StreamResponseCollector用于捕获智能体响应
        """
        self.response_collector = collector or StreamResponseCollector(
            platform=AgentPlatform.API_AUTOMATION
        )
        self.runtime: Optional[SingleThreadedAgentRuntime] = None
        self.agent_factory = agent_factory
        self.active_sessions: Dict[str, Dict[str, Any]] = {}

        # 编排器性能指标
        self.orchestrator_metrics = {
            "total_workflows": 0,
            "successful_workflows": 0,
            "failed_workflows": 0,
            "active_sessions": 0
        }

        logger.info("接口自动化智能体编排器初始化完成")

    async def initialize(self, **agent_kwargs) -> None:
        """
        初始化编排器和智能体

        Args:
            **agent_kwargs: 智能体初始化参数
        """
        try:
            logger.info("🚀 初始化接口自动化智能体编排器...")

            if self.runtime is None:
                # 创建运行时
                self.runtime = SingleThreadedAgentRuntime()

                # 注册智能体到运行时
                await self.agent_factory.register_agents_to_runtime(self.runtime)

                # 设置响应收集器
                await self.agent_factory.register_stream_collector(
                    runtime=self.runtime,
                    collector=self.response_collector
                )

                # 启动运行时
                self.runtime.start()

                logger.info("✅ 接口自动化智能体编排器初始化完成")

        except Exception as e:
            logger.error(f"❌ 接口自动化智能体编排器初始化失败: {str(e)}")
            raise

    async def process_api_document_content(
            self,
            session_id: str,
            api_file_content: str,
            config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        处理API文档内容
        """
        try:
            self.orchestrator_metrics["total_workflows"] += 1
            self.orchestrator_metrics["active_sessions"] += 1

            # 记录会话信息
            self.active_sessions[session_id] = {
                "start_time": datetime.now(),
                "status": "processing",
                "current_step": "document_parsing",
                "api_file_content": api_file_content[:1000],
                "config": config or {}
            }

            logger.info(f"开始处理API文档内容: {api_file_content[:1000]} (会话: {session_id})")

            # 解析API文档内容
            await self._parse_api_document_content(
                session_id, api_file_content
            )

            # 更新会话状态为消息已发布，但不设置为完成
            self.active_sessions[session_id]["current_step"] = "message_published"
            self.active_sessions[session_id]["status"] = "processing"  # 保持处理状态
            self.active_sessions[session_id]["published_time"] = datetime.now()

            # 注意：不要在这里设置为完成，因为智能体还没有处理消息
            # self.active_sessions[session_id]["current_step"] = "completed"  # 删除
            # self.active_sessions[session_id]["status"] = "completed"        # 删除
            # self.active_sessions[session_id]["end_time"] = datetime.now()   # 删除

            self.orchestrator_metrics["active_sessions"] -= 1  # 保持这个状态更新

            return {
                "success": True,
                "session_id": session_id,
                "message": "API文档解析请求已发布",  # 更新消息
                "session_info": self.active_sessions[session_id],
                "note": "消息已发布到智能体，等待处理完成"
            }

        except Exception as e:
            self.orchestrator_metrics["failed_workflows"] += 1
            self.orchestrator_metrics["active_sessions"] -= 1

            # 创建会话信息
            self.active_sessions[session_id] = {
                "status": "failed",
                "error": str(e),
                "end_time": datetime.now()
            }

            logger.error(f"处理API文档内容失败: {str(e)}")
            raise

    async def wait_for_agent_completion(self, session_id: str, timeout: int = 180) -> bool:
        """
        智能等待智能体完成处理 - 增强版
        
        Args:
            session_id: 会话 ID
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功完成
        """
        try:
            import asyncio  # 添加asyncio导入
            from apps.ai_api_auto.agents.api_docs_paraser_agent import ApiDocParserAgent

            logger.info(f"[智能等待] 开始等待智能体完成处理: session_id={session_id}, timeout={timeout}秒")

            start_time = datetime.now()
            check_interval = 1  # 每1秒检查一次，更频繁的检查
            last_log_time = start_time
            log_interval = 5  # 每5秒输出一次进度

            while (datetime.now() - start_time).total_seconds() < timeout:
                # 检查智能体是否已完成处理
                if ApiDocParserAgent.is_session_completed(session_id):
                    completion_info = ApiDocParserAgent.get_completion_info(session_id)

                    # 更新编排器会话状态
                    if session_id in self.active_sessions:
                        self.active_sessions[session_id].update({
                            "status": "completed" if completion_info.get('success') else "failed",
                            "current_step": "agent_completed",
                            "end_time": completion_info.get('completion_time'),
                            "agent_success": completion_info.get('success'),
                            "agent_error": completion_info.get('error'),
                            "completion_detected_at": datetime.now()
                        })

                    elapsed = (datetime.now() - start_time).total_seconds()
                    success_status = completion_info.get('success', False)

                    if success_status:
                        logger.info(f"[智能等待] ✅ 智能体处理成功完成: session_id={session_id}, 耗时={elapsed:.2f}秒")
                    else:
                        error_msg = completion_info.get('error', '未知错误')
                        logger.warning(
                            f"[智能等待] ⚠️ 智能体处理失败: session_id={session_id}, 耗时={elapsed:.2f}秒, 错误={error_msg}")

                    return True

                # 等待一段时间再检查
                await asyncio.sleep(check_interval)

                # 定期输出进度信息
                current_time = datetime.now()
                elapsed = (current_time - start_time).total_seconds()

                if (current_time - last_log_time).total_seconds() >= log_interval:
                    logger.info(
                        f"[智能等待] ⏳ 等待智能体处理中... 已等待={elapsed:.1f}秒, 剩余={timeout - elapsed:.1f}秒")

                    # 检查会话状态
                    session_info = self.active_sessions.get(session_id, {})
                    current_status = session_info.get('status', 'unknown')
                    current_step = session_info.get('current_step', 'unknown')
                    logger.info(f"[智能等待] 📊 当前会话状态: status={current_status}, step={current_step}")

                    last_log_time = current_time

            # 超时处理
            elapsed_time = (datetime.now() - start_time).total_seconds()
            logger.warning(
                f"[智能等待] ⏰ 等待智能体处理超时: session_id={session_id}, 实际等待={elapsed_time:.2f}秒, 配置超时={timeout}秒")

            # 更新会话状态为超时
            if session_id in self.active_sessions:
                self.active_sessions[session_id].update({
                    "status": "timeout",
                    "current_step": "agent_timeout",
                    "timeout_at": datetime.now(),
                    "actual_wait_time": elapsed_time
                })

            return False

        except Exception as e:
            logger.error(f"[智能等待] ❌ 等待智能体完成失败: session_id={session_id}, 错误={str(e)}")
            import traceback
            logger.error(f"[智能等待] 错误详情: {traceback.format_exc()}")
            return False

    async def _parse_api_document_content(self, session_id: str, api_file_content: str):
        """
        发送API文档呢日解析请求
        """
        try:
            # 构建解析请求
            parse_request = ApiDocumentContentParseInput(
                session_id=session_id,
                api_file_content=api_file_content
            )
            # 发送到API文档解析智能体
            await self.runtime.publish_message(
                parse_request,
                topic_id=TopicId(type=TopicTypes.API_DOC_PARSER.value, source="api_orchestrator")
            )
        except Exception as e:
            logger.error(f"发送API文档解析请求失败: {str(e)}")
            raise

    async def cleanup(self) -> None:
        """清理编排器资源"""
        try:
            # 清理智能体
            await self.agent_factory.cleanup()

            # 清理响应收集器
            if self.response_collector:
                self.response_collector.cleanup()

            # 停止运行时
            if self.runtime:
                await self.runtime.stop()

            # 清理会话
            self.active_sessions.clear()

            logger.info("接口自动化编排器资源清理完成")

        except Exception as e:
            logger.error(f"清理编排器资源失败: {str(e)}")
