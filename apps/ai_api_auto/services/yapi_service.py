"""
YAPI 数据获取服务
基于 HttpService 实现 YAPI 接口数据的获取功能
包括：获取项目信息、获取接口列表、获取接口详情的完整流程
"""

import logging
import math
from typing import Dict, List, Any, Optional

from apps.ai_api_auto.models import YapiBaseInfoModel, YapiApiInfoModel
from common.service.http_service import HttpService


class YapiService:
    """YAPI 数据获取服务类"""

    def __init__(self, yapi_base_info: YapiBaseInfoModel):
        """
        初始化 YAPI 服务
        
        Args:
            yapi_base_info: YAPI 基础信息模型实例
        """
        self.yapi_base_info = yapi_base_info
        self.base_url = yapi_base_info.yapi_base_url
        self.token = yapi_base_info.yapi_token
        self.logger = logging.getLogger(__name__)

        # 初始化 HTTP 服务
        self.http_service = HttpService(
            base_url=self.base_url,
            timeout=30.0,
            verify=False  # YAPI 通常使用自签名证书
        )

    def get_project_info(self) -> Dict[str, Any]:
        """
        获取项目信息
        
        Returns:
            Dict[str, Any]: 项目信息，包含项目ID和项目名称
            
        Raises:
            Exception: 当请求失败或响应格式不正确时抛出异常
        """
        try:
            # 构建请求参数
            params = {"token": self.token}

            # 发送请求
            self.logger.info(f"开始获取项目信息，token: {self.token[:10]}...")
            response_data = self.http_service.get_json(
                endpoint="/api/project/get",
                params=params
            )

            # 检查响应状态
            if response_data.get("errcode") != 0:
                raise Exception(f"获取项目信息失败: {response_data.get('errmsg', '未知错误')}")

            # 提取项目信息
            project_data = response_data.get("data", {})
            project_info = {
                "project_id": project_data.get("_id"),
                "project_name": project_data.get("name"),
                "basepath": project_data.get("basepath"),
                "project_type": project_data.get("project_type"),
                "raw_data": project_data
            }

            self.logger.info(f"成功获取项目信息: {project_info['project_name']} (ID: {project_info['project_id']})")
            return project_info

        except Exception as e:
            self.logger.error(f"获取项目信息失败: {str(e)}")
            raise

    def get_interface_list(self, project_id: int, page: int = 1, limit: int = 10) -> Dict[str, Any]:
        """
        获取接口列表（单页）
        
        Args:
            project_id: 项目ID
            page: 页码，从1开始
            limit: 每页数量
            
        Returns:
            Dict[str, Any]: 接口列表信息
            
        Raises:
            Exception: 当请求失败或响应格式不正确时抛出异常
        """
        try:
            # 构建请求参数
            params = {
                "token": self.token,
                "project_id": project_id,
                "page": page,
                "limit": limit
            }

            # 发送请求
            self.logger.info(f"获取接口列表，项目ID: {project_id}, 页码: {page}, 每页: {limit}")
            response_data = self.http_service.get_json(
                endpoint="/api/interface/list",
                params=params
            )

            # 检查响应状态
            if response_data.get("errcode") != 0:
                raise Exception(f"获取接口列表失败: {response_data.get('errmsg', '未知错误')}")

            # 提取接口列表信息
            data = response_data.get("data", {})
            interface_list_info = {
                "count": data.get("count", 0),  # 总接口数
                "total": data.get("total", 0),  # 总页数
                "current_page": page,
                "limit": limit,
                "interfaces": data.get("list", []),
                "raw_data": data
            }

            self.logger.info(f"成功获取接口列表，当前页: {page}, 接口数: {len(interface_list_info['interfaces'])}")
            return interface_list_info

        except Exception as e:
            self.logger.error(f"获取接口列表失败: {str(e)}")
            raise

    def get_all_interfaces(self, project_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取所有接口列表（分页获取）
        
        Args:
            project_id: 项目ID
            limit: 每页数量
            
        Returns:
            List[Dict[str, Any]]: 所有接口信息列表
            
        Raises:
            Exception: 当请求失败时抛出异常
        """
        try:
            all_interfaces = []

            # 先获取第一页，了解总数
            first_page = self.get_interface_list(project_id, page=1, limit=limit)
            all_interfaces.extend(first_page["interfaces"])

            total_count = first_page["count"]
            total_pages = math.ceil(total_count / limit)

            self.logger.info(f"总接口数: {total_count}, 总页数: {total_pages}")

            # 获取剩余页面
            for page in range(2, total_pages + 1):
                page_data = self.get_interface_list(project_id, page=page, limit=limit)
                all_interfaces.extend(page_data["interfaces"])

            self.logger.info(f"成功获取所有接口列表，总数: {len(all_interfaces)}")
            return all_interfaces

        except Exception as e:
            self.logger.error(f"获取所有接口列表失败: {str(e)}")
            raise

    def get_interface_detail(self, interface_id: int, project_basepath: str = None) -> Dict[str, Any]:
        """
        获取接口详情

        Args:
            interface_id: 接口ID
            project_basepath: 项目基础路径，用于拼接完整的接口路径

        Returns:
            Dict[str, Any]: 接口详细信息

        Raises:
            Exception: 当请求失败或响应格式不正确时抛出异常
        """
        try:
            # 构建请求参数
            params = {
                "token": self.token,
                "id": interface_id
            }

            # 发送请求
            self.logger.info(f"获取接口详情，接口ID: {interface_id}")
            response_data = self.http_service.get_json(
                endpoint="/api/interface/get",
                params=params
            )

            # 检查响应状态
            if response_data.get("errcode") != 0:
                raise Exception(f"获取接口详情失败: {response_data.get('errmsg', '未知错误')}")

            # 提取接口详情信息
            interface_data = response_data.get("data", {})
            original_path = interface_data.get("path", "")

            # 处理接口路径，确保包含 basepath
            api_path = self._normalize_api_path(original_path, project_basepath)

            interface_detail = {
                "interface_id": interface_data.get("_id"),
                "api_path": api_path,
                "original_path": original_path,  # 保留原始路径用于调试
                "api_name": interface_data.get("title"),
                "api_method": interface_data.get("method"),
                "api_meta_data": interface_data,  # 存储整个data信息作为元数据
                "status": interface_data.get("status"),
                "project_id": interface_data.get("project_id"),
                "raw_data": interface_data
            }

            self.logger.info(
                f"成功获取接口详情: {interface_detail['api_name']} ({interface_detail['api_method']} {interface_detail['api_path']})")
            if original_path != api_path:
                self.logger.info(f"路径已标准化: {original_path} -> {api_path}")

            return interface_detail

        except Exception as e:
            self.logger.error(f"获取接口详情失败: {str(e)}")
            raise

    def _normalize_api_path(self, original_path: str, project_basepath: str = None) -> str:
        """
        标准化接口路径，确保包含项目的 basepath

        Args:
            original_path: 原始接口路径
            project_basepath: 项目基础路径

        Returns:
            str: 标准化后的完整接口路径
        """
        if not original_path:
            return original_path

        if not project_basepath:
            return original_path

        # 清理路径，移除多余的斜杠
        original_path = original_path.strip()
        project_basepath = project_basepath.strip()

        # 确保 basepath 以 / 开头，不以 / 结尾
        if project_basepath and not project_basepath.startswith('/'):
            project_basepath = '/' + project_basepath
        if project_basepath.endswith('/'):
            project_basepath = project_basepath.rstrip('/')

        # 确保 original_path 以 / 开头
        if not original_path.startswith('/'):
            original_path = '/' + original_path

        # 检查路径是否已经包含 basepath
        if original_path.startswith(project_basepath):
            # 路径已经包含 basepath，直接返回
            return original_path
        else:
            # 路径不包含 basepath，需要拼接
            # 避免重复的斜杠
            if project_basepath.endswith('/') or original_path.startswith('/'):
                full_path = project_basepath + original_path
            else:
                full_path = project_basepath + '/' + original_path

            # 清理可能的双斜杠
            full_path = full_path.replace('//', '/')

            return full_path

    def get_interface_detail_with_basepath(self, interface_id: int) -> Dict[str, Any]:
        """
        获取接口详情并自动处理 basepath
        这是一个便利方法，会自动获取项目信息并处理路径标准化

        Args:
            interface_id: 接口ID

        Returns:
            Dict[str, Any]: 接口详细信息（包含标准化的路径）
        """
        try:
            # 先获取项目信息以获得 basepath
            project_info = self.get_project_info()
            project_basepath = project_info.get("basepath", "")

            # 获取接口详情并处理路径
            return self.get_interface_detail(interface_id, project_basepath)

        except Exception as e:
            self.logger.error(f"获取接口详情（含basepath处理）失败: {str(e)}")
            raise

    async def fetch_all_api_data(self, creator_id: Optional[int] = None, project_id: Optional[int] = None) -> Dict[
        str, Any]:
        """
        获取所有API数据的完整流程

        Args:
            creator_id: 创建者ID（可选）
            project_id: 项目ID（可选，用于关联到具体项目）

        Returns:
            Dict[str, Any]: 包含处理结果的字典

        Raises:
            Exception: 当任何步骤失败时抛出异常
        """
        try:
            result = {
                "success": False,
                "project_info": None,
                "total_interfaces": 0,
                "saved_interfaces": 0,
                "failed_interfaces": 0,
                "errors": []
            }

            # 步骤1: 获取项目信息
            self.logger.info("开始执行完整的API数据获取流程")
            project_info = self.get_project_info()
            result["project_info"] = project_info

            yapi_project_id = project_info["project_id"]
            if not yapi_project_id:
                raise Exception("无法获取项目ID")

            # 获取项目的 basepath
            project_basepath = project_info.get("basepath", "")
            self.logger.info(f"项目基础路径: {project_basepath}")

            # 步骤2: 获取所有接口列表
            all_interfaces = self.get_all_interfaces(yapi_project_id, limit=20)  # 增加每页数量提高效率
            result["total_interfaces"] = len(all_interfaces)

            if not all_interfaces:
                self.logger.warning("未找到任何接口")
                result["success"] = True
                return result

            # 步骤3: 获取每个接口的详情并保存到数据库
            saved_count = 0
            failed_count = 0

            for interface_summary in all_interfaces:
                try:
                    interface_id = interface_summary.get("_id")
                    if not interface_id:
                        self.logger.warning(f"接口缺少ID信息: {interface_summary}")
                        failed_count += 1
                        continue

                    # 获取接口详情，传递 basepath 用于路径标准化
                    interface_detail = self.get_interface_detail(interface_id, project_basepath)

                    # 检查是否已存在相同的接口记录
                    existing_api = await YapiApiInfoModel.filter(
                        yapi_base=self.yapi_base_info,
                        api_path=interface_detail["api_path"],
                        api_method=interface_detail["api_method"]
                    ).first()

                    if existing_api:
                        # 更新现有记录
                        existing_api.api_name = interface_detail["api_name"]
                        existing_api.api_path = interface_detail["api_path"]
                        existing_api.api_meta_data = interface_detail["api_meta_data"]
                        if creator_id:
                            existing_api.updater_id = creator_id
                        await existing_api.save()
                        self.logger.info(f"更新接口记录: {interface_detail['api_name']}")
                    else:
                        # 创建新记录
                        api_record = YapiApiInfoModel(
                            api_name=interface_detail["api_name"],
                            api_path=interface_detail["api_path"],
                            api_method=interface_detail["api_method"],
                            api_meta_data=interface_detail["api_meta_data"],
                            yapi_base=self.yapi_base_info,
                            project_id=project_id,
                            creator_id=creator_id
                        )
                        await api_record.save()
                        self.logger.info(f"保存新接口记录: {interface_detail['api_name']}")

                    saved_count += 1

                except Exception as e:
                    error_msg = f"处理接口 {interface_summary.get('title', 'Unknown')} 失败: {str(e)}"
                    self.logger.error(error_msg)
                    result["errors"].append(error_msg)
                    failed_count += 1

            result["saved_interfaces"] = saved_count
            result["failed_interfaces"] = failed_count
            result["success"] = True

            self.logger.info(f"完整流程执行完成，成功: {saved_count}, 失败: {failed_count}")
            return result

        except Exception as e:
            error_msg = f"执行完整API数据获取流程失败: {str(e)}"
            self.logger.error(error_msg)
            result["errors"].append(error_msg)
            raise

    def validate_yapi_response(self, response_data: Dict[str, Any], operation: str) -> None:
        """
        验证YAPI响应数据

        Args:
            response_data: 响应数据
            operation: 操作名称（用于错误信息）

        Raises:
            YapiServiceError: 当响应格式不正确时抛出异常
        """
        if not isinstance(response_data, dict):
            raise YapiServiceError(f"{operation}响应格式错误：不是有效的JSON对象")

        errcode = response_data.get("errcode")
        if errcode is None:
            raise YapiServiceError(f"{operation}响应格式错误：缺少errcode字段")

        if errcode != 0:
            errmsg = response_data.get("errmsg", "未知错误")
            raise YapiServiceError(f"{operation}失败：{errmsg} (错误码: {errcode})")

    def get_interface_count(self, project_id: int) -> int:
        """
        获取项目的接口总数

        Args:
            project_id: 项目ID

        Returns:
            int: 接口总数
        """
        try:
            first_page = self.get_interface_list(project_id, page=1, limit=1)
            return first_page["count"]
        except Exception as e:
            self.logger.error(f"获取接口总数失败: {str(e)}")
            return 0

    @classmethod
    async def create_from_id(cls, yapi_base_info_id: int) -> 'YapiService':
        """
        通过YapiBaseInfoModel的ID创建YapiService实例

        Args:
            yapi_base_info_id: YapiBaseInfoModel的ID

        Returns:
            YapiService: 服务实例

        Raises:
            YapiServiceError: 当找不到对应记录时抛出异常
        """
        yapi_base_info = await YapiBaseInfoModel.get_or_none(id=yapi_base_info_id)
        if not yapi_base_info:
            raise YapiServiceError(f"未找到ID为 {yapi_base_info_id} 的YAPI基础信息记录")

        return cls(yapi_base_info)


class YapiServiceError(Exception):
    """YAPI服务异常"""
    pass
