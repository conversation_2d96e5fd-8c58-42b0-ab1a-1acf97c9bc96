# @Time: 2025/3/24 10:14
# @Author: lvjing
import logging
from typing import List

from datasets import Dataset
from ragas import RunConfig, evaluate
from ragas.metrics import context_precision, context_recall, context_entity_recall, faithfulness, answer_relevancy

from apps.llm_manage.models import LLMModel
from apps.llm_manage.service.llm_model import EmbeddingModel, LLMModelConfig
from apps.requirement.models import RequirementModel
from apps.suite.models import SuiteModel
from apps.suite_execute.models import SuiteExecuteDetailModel, SuiteExecuteSequenceModel

logger = logging.getLogger(__name__)


class RAGEvaluator:
    def __init__(self):
        self.metrics = [
            context_precision,
            context_recall,
            context_entity_recall,
            faithfulness,
            answer_relevancy
        ]

    async def prepare_evaluation_data(self,
                                      user_input: List[str],
                                      retrieved_contexts: List[List[str]],
                                      response: List[str],
                                      reference: List[str]
                                      ) -> Dataset:
        """
        准备评估数据集
        :param user_input:
        :param retrieved_contexts:
        :param response:
        :param reference:
        :return:
        """
        if not user_input or not retrieved_contexts or not response or not reference:
            raise ValueError("所有输入列表都不能为空")

        if not (len(user_input) == len(retrieved_contexts) == len(response) == len(reference)):
            raise ValueError("所有输入列表长度必须相同")

        eval_data = {
            "user_input": user_input,
            "response": response,
            "retrieved_contexts": retrieved_contexts,
            "reference": reference
        }
        return Dataset.from_dict(eval_data)

    async def evaluate(self, model_id: int, embedding_model_id: int, suite_sequence_id: int) -> List[
        dict]:
        """
        评估完整数据集
        :return:
        """
        logger.info("===================构造数据集===================")
        user_input = []
        retrieved_contexts = []
        response = []
        reference = []
        suite_execute_details = await SuiteExecuteDetailModel.filter(sequence_id=suite_sequence_id,
                                                                     is_multiple=False)
        sequence_data = await SuiteExecuteSequenceModel.filter(id=suite_sequence_id).values("suite_id")
        query_suite_id = sequence_data[0]["suite_id"]
        # 根据suite_id在Suite模型查询需求id
        suite_data = await SuiteModel.filter(id=query_suite_id).values("requirement_id", "version")
        requirement_id = suite_data[0]["requirement_id"]
        # 根据requirement_id在Requirement模型查询项目id
        requirement_data = await RequirementModel.filter(id=requirement_id).values("project_id")
        datas = []
        for detail in suite_execute_details:
            datas.append({
                "user_input": detail.question,
                "response": detail.answer_llm,
                "retrieved_contexts": detail.contexts,
                "reference": detail.answer_expect,
                "suite_id": query_suite_id,
                "material_id": detail.material_id,
                "requirement_id": requirement_id,
                "version": suite_data[0]["version"],
                "project_id": requirement_data[0]["project_id"]
            })
            user_input.append(detail.question)
            retrieved_contexts.append(detail.contexts)
            response.append(detail.answer_llm)
            reference.append(detail.answer_expect)
        evaluation_data = await self.prepare_evaluation_data(
            user_input=user_input,
            retrieved_contexts=retrieved_contexts,
            response=response,
            reference=reference,
        )
        logger.info("===================获取大模型信息===================")
        model_config = await LLMModel.get_or_none(id=model_id)
        eval_llm = LLMModelConfig(model_config)
        embedding_model_config = await LLMModel.get_or_none(id=embedding_model_id)
        eval_embedding_fn = EmbeddingModel(embedding_model_config).get_embedding_fun()
        logger.info("===================RAG评估开始===================")
        try:
            config = RunConfig(timeout=1200, max_retries=5, max_wait=120, max_workers=8, log_tenacity=True)
            results = evaluate(
                dataset=evaluation_data,
                llm=eval_llm,
                embeddings=eval_embedding_fn,
                metrics=self.metrics,
                raise_exceptions=True,
                run_config=config
            )
            # 确保结果不为空
            if not results:
                raise ValueError("评估结果为空")
            res = [
                {
                    **{
                        "user_input": data.get("user_input"),
                        "response": data.get("response"),
                        "retrieved_contexts": data.get("retrieved_contexts"),
                        "reference": data.get("reference"),
                        "suite_id": data.get("suite_id"),
                        "material_id": data.get("material_id"),
                        "requirement_id": data.get("requirement_id"),
                        "project_id": data.get("project_id"),
                        "version": data.get("version")
                    },
                    **score
                }
                for data, score in zip(datas, results.scores)
            ]
            logger.info("===================RAG评估结束===================")
            return res
        except Exception as e:
            logger.error(f"评估失败: {e}")
            raise e


rag_evaluator = RAGEvaluator()
