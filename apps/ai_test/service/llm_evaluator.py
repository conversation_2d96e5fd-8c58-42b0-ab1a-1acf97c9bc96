# @Time: 2025/4/3 09:31
# @Author: lvjing
import logging
from typing import List, Any

from pydantic import ValidationError
from ragas import evaluate, EvaluationDataset, MultiTurnSample
from ragas.llms import LangchainLLMWrapper
from ragas.messages import HumanMessage, AIMessage
from ragas.metrics import AgentGoalAccuracyWithoutReference, TopicAdherenceScore

from apps.llm_manage.models import LLMModel
from apps.llm_manage.service.llm_model import LLMModelConfig
from apps.requirement.models import RequirementModel
from apps.suite.models import SuiteModel
from apps.suite_execute.models import SuiteExecuteDetailModel, SuiteExecuteSequenceModel

logger = logging.getLogger(__name__)


class LLMEvaluator:
    """
    适用场景：适用于更复杂的多轮对话评估，例如涉及工具调用（如 API 调用）的对话场景。
    组成要素：
    用户输入（user_input）：包含多轮对话的消息序列，消息可以是用户消息（HumanMessage）、AI 消息（AIMessage）或工具消息（ToolMessage）。
    参考答案（reference）：用于评估的最终目标响应。
    """

    def _convert_conversation(self, messages: list) -> List[Any]:
        """
        转换对话数据为指定格式（不含role字段）

        Args:
            messages: 原始对话数据列表，每个元素应包含 role 和 content 字段
        Returns:
            转换后的消息对象列表，交替包含 HumanMessage 和 AIMessage 实例
        Raises:
            ValueError: 当遇到无效的 role 类型或缺少必要字段时抛出
            TypeError: 当消息结构不符合预期时抛出
        """
        if not isinstance(messages, list):
            raise TypeError("Input must be a list of message dictionaries")
        conversation = []
        # expected_roles = {"human", "ai"}
        expected_roles = {"user", "assistant", "human", "ai"}

        for idx, msg in enumerate(messages):
            role = msg.get('role')
            content = msg.get('content')
            # 验证必要字段
            if content is None:
                raise ValueError(f"第 {idx + 1} 条消息缺少 'content' 字段")
            if role not in expected_roles:
                valid_roles = ", ".join(expected_roles)
                raise ValueError(f"第 {idx + 1} 条消息无效的 role 值 '{msg.get('role')}'. 有效值: {valid_roles}")

            # 创建消息对象（自动排除 role 字段）
            try:
                if role == "user" or role == "human":
                    message_obj = HumanMessage(content=msg.get('content'))
                else:
                    message_obj = AIMessage(content=msg.get('content'))
                conversation.append(message_obj)
            except ValidationError as e:
                raise ValueError(f"第 {idx + 1} 条消息验证失败: {e.errors()}")
        # 验证对话轮次交替性
        if not all(
                isinstance(msg, HumanMessage) if i % 2 == 0
                else isinstance(msg, AIMessage)
                for i, msg in enumerate(conversation)
        ):
            raise ValueError("对话轮次格式不正确，应为 Human <-> AI 交替")
        return conversation

    async def evaluate(self, suite_sequence_id: int, model_id):
        """
        评估LLM多轮对话
        """
        logger.info("===================获取大模型信息===================")
        model_config = await LLMModel.get_or_none(id=model_id)
        llm = LLMModelConfig(model_config)
        evaluator_llm = LangchainLLMWrapper(llm)
        logger.info("===================构造数据集===================")
        suite_execute_details = await SuiteExecuteDetailModel.filter(sequence_id=suite_sequence_id, is_multiple=True)
        sequence_data = await SuiteExecuteSequenceModel.filter(id=suite_sequence_id).values("suite_id")
        query_suite_id = sequence_data[0]["suite_id"]
        # 根据suite_id在Suite模型查询需求id
        suite_data = await SuiteModel.filter(id=query_suite_id).values("requirement_id", "version")
        requirement_id = suite_data[0]["requirement_id"]
        # 根据requirement_id在Requirement模型查询项目id
        requirement_data = await RequirementModel.filter(id=requirement_id).values("project_id")
        datas = []
        for detail in suite_execute_details:
            datas.append({
                "conversation": detail.answer_llm_multiple,
                "reference": detail.answer_expect,
                "topics": detail.topics,
                "suite_id": query_suite_id,
                "material_id": detail.material_id,
                "requirement_id": requirement_id,
                "version": suite_data[0]["version"],
                "project_id": requirement_data[0]["project_id"]
            })
        try:
            # 创建评估数据集
            eval_dataset = EvaluationDataset(samples=[
                MultiTurnSample(
                    user_input=self._convert_conversation(item.answer_llm_multiple),
                    reference=item.answer_expect,
                    reference_topics=item.topics
                )
                for item in suite_execute_details
            ])
            # 设定评估指标
            metrics = [
                AgentGoalAccuracyWithoutReference(llm=evaluator_llm),
                TopicAdherenceScore(mode="precision", llm=evaluator_llm),
                TopicAdherenceScore(mode="recall", llm=evaluator_llm),
                TopicAdherenceScore(mode="f1", llm=evaluator_llm)
            ]
            # 进行评估
            results = evaluate(
                dataset=eval_dataset,
                metrics=metrics
            )
            # 确保结果不为空
            if not results:
                raise ValueError("评估结果为空")

            # 处理结果
            formatted_results = [
                {
                    "conversation": data.get("conversation"),
                    "reference": data.get("reference"),
                    "reference_topics": data.get("topics"),
                    "suite_id": data.get("suite_id"),
                    "material_id": data.get("material_id"),
                    "requirement_id": data.get("requirement_id"),
                    "project_id": data.get("project_id"),
                    "version": data.get("version"),
                    "agent_goal_accuracy": score.get('agent_goal_accuracy'),
                    "precision": score.get('topic_adherence(mode=precision)'),
                    "recall": score.get('topic_adherence(mode=recall)'),
                    "f1": score.get('topic_adherence(mode=f1)')
                }
                for data, score in zip(datas, results.scores)
            ]
            return formatted_results
        except Exception as e:
            logger.error(f"评估失败: {e}")
            raise e


llm_evaluation = LLMEvaluator()
