# @Time: 2025/3/24 10:08
# @Author: lvjing
from datetime import datetime
from typing import Optional, Literal, Union

from fastapi import Query
from pydantic import BaseModel, Field, field_validator
from pydantic_core.core_schema import ValidationInfo

# 定义可选的指标类型
RagMetricType = Literal[
    "context_precision",
    "context_recall",
    "context_entity_recall",
    "faithfulness",
    "answer_relevancy"
]

LLMMetricType = Literal[
    "agent_goal_accuracy",
    "precision",
    "recall",
    "f1"
]

# 定义可选的分数区间类型
ScoreRangeType = Literal[
    "0.6以下",
    "0.6-0.7",
    "0.7-0.8",
    "0.8-0.9",
    "0.9以上"
]


class RagEvaluationDataRequest(BaseModel):
    """
    Rag评估测试数据集
    """
    type_id: int = Field(description="评测框架类型 1-RAGAS")
    suite_sequence_id: int = Field(description="用例集执行序列id")
    model_id: int = Field(description="模型id")
    embedding_model_id: int = Field(description="嵌入模型id")


class RagEvaluationResultQuery(BaseModel):
    type_id: Optional[int] = Field(description="评测框架类型 1-RAGAS")
    project_id: Optional[int] = Field(None, description="项目id")
    requirement_id: Optional[int] = Field(None, description="需求id")
    version: Optional[str] = Query(None, description="版本号")
    suite_id: Optional[int] = Query(None, description="用例集id")
    material_id: Optional[int] = Field(None, description="物料id")
    keyword: Optional[str] = Query(None, description="关键词搜索(用户提问/答案)")
    metric: Optional[RagMetricType] = Query(None, description="指标类型")
    score_range: Optional[ScoreRangeType] = Query(None, description="分数区间")

    @field_validator('score_range')
    def validate_score_range_with_metric(cls, v: str, info: ValidationInfo):
        if v is not None and not info.data.get('metric'):
            raise ValueError("score_range必须配合metric参数使用")
        return v


class RAGEvaluateResultResponse(BaseModel):
    id: int = Field(description="主键id")
    type_id: int = Field(description="评测框架类型 1-RAGAS")
    user_input: str = Field(description="用户的提问")
    retrieved_contexts: str = Field(description="上下文信息（来源向量数据库）")
    response: str = Field(description="生成的答案")
    reference: Optional[str] = Field(description="标准答案", default=None)
    context_precision: float = Field(description="上下文精准率", default=0.0)
    context_recall: float = Field(description="上下文召回率", default=0.0)
    context_entity_recall: float = Field(description="上下文实体召回", default=0.0)
    faithfulness: float = Field(description="忠诚度", default=0.0)
    answer_relevancy: float = Field(description="答案准确性", default=0.0)
    project_id: int = Field(description="项目id", default=None)
    project_name: str = Field(description="项目名", default=None)
    requirement_id: int = Field(description="需求id", default=None)
    requirement_name: str = Field(description="需求名", default=None)
    version: str = Field(description="版本号", default=None)
    suite_id: int = Field(description="用例集id", default=None)
    suite_name: str = Field(description="用例集名", default=None)
    score: float = Field(description="分数", default=0.0)
    remark: str = Field(description="备注", default=None)
    execution_times: int = Field(description="执行次数", default=0)
    executor: str = Field(description="执行者", default=None)
    created_time: datetime = Field(description="创建时间")

    class Config:
        from_attributes = True

    @classmethod
    def from_orm(cls, obj):
        # 格式化浮点数为两位小数
        obj.context_precision = round(obj.context_precision, 2)
        obj.context_recall = round(obj.context_recall, 2)
        obj.context_entity_recall = round(obj.context_entity_recall, 2)
        obj.faithfulness = round(obj.faithfulness, 2)
        obj.answer_relevancy = round(obj.answer_relevancy, 2)
        return super().model_validate(obj)


class RagEvaluationDataUpdateRequest(BaseModel):
    """
    更新测试数据
    """
    remark: Optional[str] = Field(description="备注", default=None)
    score: Optional[float] = Field(description="分数", default=None)


class ConversationsRequest(BaseModel):
    type_id: int = Field(description="评测框架类型 1-RAGAS")
    model_id: int = Field(description="模型配置id")
    suite_sequence_id: int = Field(..., description="用例集执行序列id")


class LLMEvaluationResultQuery(BaseModel):
    type_id: Optional[int] = Query(None, description="评测框架类型")
    project_id: Optional[int] = Field(None, description="项目id")
    requirement_id: Optional[int] = Field(None, description="需求id")
    version: Optional[str] = Query(None, description="版本号")
    suite_id: Optional[int] = Query(None, description="用例集id")
    keyword: Optional[str] = Query(None, description="关键词搜索(用户提问/答案)")
    metric: Optional[LLMMetricType] = Query(None, description="指标类型")
    score_range: Optional[ScoreRangeType] = Query(None, description="分数区间")

    @field_validator('score_range')
    def validate_score_range_with_metric(cls, v: str, info: ValidationInfo):
        if v is not None and not info.data.get('metric'):
            raise ValueError("score_range必须配合metric参数使用")
        return v


class LLMEvaluateResultResponse(BaseModel):
    id: int = Field(description="主键id")
    type_id: int = Field(description="评测框架类型 1-RAGAS")
    conversation: str = Field(description="对话内容")
    reference: str = Field(description="人类确认的正确答案", default=None)
    reference_topics: str = Field(description="填写主题，计算 TopicAdherenceScore 时要用", default=None)
    agent_goal_accuracy: float = Field(description="根据对话交互推断AI是否满足了用户的目标", default=0.0)
    precision: float = Field(description="AI回答的准确性", default=0.0)
    recall: float = Field(description="AI回答的召回率", default=0.0)
    f1: float = Field(description="AI回答的F1", default=0.0)
    project_id: int = Field(description="项目id", default=None)
    project_name: str = Field(description="项目名", default=None)
    requirement_id: int = Field(description="需求id", default=None)
    requirement_name: str = Field(description="需求名", default=None)
    version: str = Field(description="版本号", default=None)
    suite_id: int = Field(description="用例集id", default=None)
    suite_name: str = Field(description="用例集名称", default=None)
    score: float = Field(description="分数", default=0.0)
    remark: str = Field(description="备注", default=None)
    execution_times: int = Field(description="执行次数", default=0)
    executor: str = Field(description="执行者", default=None)
    created_time: datetime = Field(description="创建时间")

    class Config:
        from_attributes = True

    @classmethod
    def from_orm(cls, obj):
        # 格式化浮点数为两位小数
        obj.agent_goal_accuracy = round(obj.agent_goal_accuracy, 2)
        obj.precision = round(obj.precision, 2)
        obj.recall = round(obj.recall, 2)
        obj.f1 = round(obj.f1, 2)
        return super().model_validate(obj)


class ManualEvaluationDataRequest(BaseModel):
    """
    人工评估测试数据
    """
    user_input: str = Field(description="用户的提问")
    response: str = Field(description="生成的答案")
    reference: str = Field(description="标准答案", default=None)
    remark: str = Field(description="备注", default=None)
    score: float = Field(description="分数", default=0.0)
    project_id: int = Field(description="项目id")
    requirement_id: int = Field(description="需求id")
    version: str = Field(description="需求版本", default=None)
    suite_id: int = Field(description="用例集id", default=None)
    material_id: int = Field(description="物料id", default=None)


class ManualEvaluationUpdateDataRequest(BaseModel):
    """
    人工评估测试数据
    """
    reference: str = Field(description="标准答案", default=None)
    remark: str = Field(description="备注", default=None)
    score: float = Field(description="分数", default=0.0)


class ManualEvaluationResultQuery(BaseModel):
    """
    评估测试数据查询条件
    """
    version: Optional[str] = Query(None, description="需求版本")
    suite_id: Optional[int] = Query(None, description="用例集id")
    keyword: Optional[str] = Query(None, description="关键词搜索(用户提问/答案)")


class EvaluationTaskQuery(BaseModel):
    """
    评估任务查询入参条件
    """
    suite_sequence_id: Optional[int] = Query(None, description="用例集id")
    status: Optional[int] = Query(None, description="任务状态 1-pending/2-running/3-completed/4-failed/5-timeout")


class EvaluateTaskResultResponse(BaseModel):
    """
    评估任务结果响应
    """
    id: int = Field(description="主键id")
    suite_id: int = Field(description="用例集id", default=None)
    suite_name: str = Field(description="用例集名", default=None)
    status: int = Field(description="任务状态 1-pending/2-running/3-completed/4-failed/5-timeout")
    execution_times: Union[int, None] = Field(description="执行次数")
    start_time: datetime = Field(description="任务开始时间")
    end_time: Union[datetime, None] = Field(description="任务结束时间")
    error: Union[str, None] = Field(description="错误信息", default=None)
    original_request: dict = Field(description="原始请求数据")
    results: Union[list, None] = Field(description="原始响应数据")

    class Config:
        from_attributes = True


# ==================== 常量定义 ====================
# RAG评估核心指标
RAG_CORE_METRICS = [
    "context_precision",  # 上下文精确度：检索到的上下文与问题的相关性
    "context_recall",  # 上下文召回率：检索到相关上下文的完整性
    "context_entity_recall",  # 上下文实体召回率：关键实体信息的召回程度
    "faithfulness",  # 忠实度：生成答案与检索上下文的一致性
    "answer_relevancy"  # 答案相关性：生成答案与用户问题的相关程度
]

# LLM评估核心指标
LLM_CORE_METRICS = [
    "agent_goal_accuracy",  # 智能体目标准确性
    "precision",  # 精确率
    "recall",  # 召回率
    "f1"  # F1分数
]


# 任务状态常量
class TaskStatus:
    PENDING = 1  # 待执行
    RUNNING = 2  # 执行中
    COMPLETED = 3  # 已完成
    FAILED = 4  # 执行失败
    TIMEOUT = 5  # 执行超时


# 评估类型常量
class EvaluationType:
    RAG = "RAG"
    LLM = "LLM"
    MANUAL = "MANUAL"
