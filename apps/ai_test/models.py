# @Time: 2025/3/24 10:08
# @Author: lvjing
from tortoise import models, fields


class RAGEvaluateResultModel(models.Model):
    """
    RAG评估结果记录表
    """
    id = fields.IntField(pk=True, auto_increment=True, description="主键id")
    type_id = fields.IntField(default=1, description="评测框架类型 1-RAGAS")
    user_input = fields.CharField(max_length=1024, description="用户的提问")
    retrieved_contexts = fields.TextField(description="检索到的上下文")
    response = fields.TextField(description="生成的答案")
    reference = fields.CharField(max_length=1024, description="标准答案", null=True)
    context_precision = fields.FloatField(description="上下文精准率", default=0.0)
    context_recall = fields.FloatField(description="上下文召回率", default=0.0)
    context_entity_recall = fields.FloatField(description="上下文实体召回", default=0.0)
    faithfulness = fields.FloatField(description="忠诚度", default=0.0)
    answer_relevancy = fields.FloatField(description="答案准确性", default=0.0)
    remark = fields.CharField(max_length=1024, description="备注", default="")
    score = fields.FloatField(description="分数", default=0.0)
    project_id = fields.IntField(description="项目ID", default=0)
    requirement_id = fields.IntField(description="需求ID", default=0)
    version = fields.CharField(max_length=255, description="需求版本", default="v1.0.0")
    suite_id = fields.IntField(description="测试套件ID", default=0)
    material_id = fields.IntField(description="物料ID", default=0)
    suite_sequence_id = fields.IntField(description="测试套件执行序列ID", default=0)
    execution_times = fields.IntField(description="执行次数", default=0)
    executor = fields.CharField(max_length=255, description="执行者", default="")
    created_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_time = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "rag_evaluate_result"
        table_description = "RAG评估结果记录表"


class LLMEvaluateResultModel(models.Model):
    """
    LLM评估结果记录表
    """
    id = fields.IntField(pk=True, auto_increment=True, description="主键id")
    type_id = fields.IntField(default=1, description="评测框架类型 1-RAGAS")
    conversation = fields.TextField(description="对话内容集合")
    reference = fields.CharField(max_length=1024, description="人类确认的正确答案", null=True)
    reference_topics = fields.CharField(max_length=1024, description="填写主题，计算 TopicAdherenceScore 时要用",
                                        null=True)
    agent_goal_accuracy = fields.FloatField(description="根据对话交互推断AI是否满足了用户的目标", default=0.0)
    precision = fields.FloatField(description="AI回答的准确性", default=0.0)
    recall = fields.FloatField(description="AI回答的召回率", default=0.0)
    f1 = fields.FloatField(description="AI回答的F1", default=0.0)
    project_id = fields.IntField(description="项目ID", default=0)
    requirement_id = fields.IntField(description="需求ID", default=0)
    version = fields.CharField(max_length=255, description="需求版本", default="v1.0.0")
    suite_id = fields.IntField(description="测试套件ID", default=0)
    suite_sequence_id = fields.IntField(description="测试套件执行序列ID", default=0)
    execution_times = fields.IntField(description="执行次数", default=0)
    executor = fields.CharField(max_length=255, description="执行者", default="")
    created_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_time = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "llm_evaluate_result"
        table_description = "LLM评估结果记录表"


class ManualEvaluateResultModel(models.Model):
    """
    人工评估结果记录表
    """
    id = fields.IntField(pk=True, auto_increment=True, description="主键id")
    user_input = fields.CharField(max_length=255, description="用户的提问")
    response = fields.CharField(max_length=255, description="生成的答案")
    reference = fields.CharField(max_length=255, description="标准答案", null=True)
    remark = fields.CharField(max_length=255, description="备注", default="")
    score = fields.FloatField(description="分数", default=0.0)
    project_id = fields.IntField(description="项目ID", default=0)
    requirement_id = fields.IntField(description="需求ID", default=0)
    version = fields.CharField(max_length=255, description="需求版本", default="v1.0.0")
    suite_id = fields.IntField(description="测试套件ID", default=0)
    material_id = fields.IntField(description="物料ID", default=0)
    executor = fields.CharField(max_length=255, description="执行者", default="")
    created_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_time = fields.DatetimeField(auto_now=True, description="更新时间")

    def __str__(self):
        return self.user_input

    class Meta:
        table = "manual_evaluate_result"
        table_description = "人工评估结果记录表"


class EvaluateTaskModel(models.Model):
    """
    评估任务数据模型
    """
    id = fields.IntField(pk=True, auto_increment=True, description="主键id")
    evaluate_type = fields.CharField(max_length=64, description="评估系统类型：RAG、LLM", default="RAG")
    suite_sequence_id = fields.IntField(description="测试套件执行序列ID")
    status = fields.IntField(default=0, description="任务状态 1-pending/2-running/3-completed/4-failed/5-timeout")
    execution_times = fields.IntField(null=True, description="执行次数", default=0)
    start_time = fields.DatetimeField(description="任务开始时间")
    end_time = fields.DatetimeField(null=True, description="任务结束时间")
    error = fields.TextField(null=True, description="错误信息")
    original_request = fields.JSONField(null=True, description="原始请求数据")
    results = fields.JSONField(null=True, description="评估结果")

    class Meta:
        table = "evaluate_task"
