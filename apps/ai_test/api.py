#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI测试评估模块API接口

该模块提供了完整的AI测试评估功能，包括：
- RAG（检索增强生成）评估：支持异步评估、结果查询、对比分析
- LLM（大语言模型）评估：支持对话评估、性能分析
- 人工评估：支持手动评估结果录入和管理
- 任务管理：支持评估任务的状态跟踪和重试机制

@Time: 2025/3/24 10:08
@Author: lvjing
@LastModified: 2025/7/7
@Version: 2.0
"""

# ==================== 标准库导入 ====================
import logging
import math
from datetime import datetime, timedelta
from typing import List, Dict, Any

# ==================== 第三方库导入 ====================
import numpy as np
import pytz
from fastapi import APIRouter, Query, Depends, BackgroundTasks
from tortoise.exceptions import IntegrityError
from tortoise.expressions import Q
from tortoise.transactions import in_transaction

# ==================== 项目内部导入 ====================
# 数据模型
from apps.ai_test.models import (
    RAGEvaluateResultModel,
    LLMEvaluateResultModel,
    ManualEvaluateResultModel,
    EvaluateTaskModel
)
# 数据模式
from apps.ai_test.schemas import (
    ConversationsRequest,
    RagEvaluationDataRequest,
    RagEvaluationDataUpdateRequest,
    RagEvaluationResultQuery,
    ManualEvaluationDataRequest,
    ManualEvaluationUpdateDataRequest,
    ManualEvaluationResultQuery,
    RAGEvaluateResultResponse,
    LLMEvaluateResultResponse,
    LLMEvaluationResultQuery,
    EvaluationTaskQuery,
    EvaluateTaskResultResponse, TaskStatus, EvaluationType, RAG_CORE_METRICS, LLM_CORE_METRICS
)
# 业务服务
from apps.ai_test.service.llm_evaluator import llm_evaluation
from apps.ai_test.service.rag_evaluator import rag_evaluator
# 关联模型
from apps.project.models import ProjectModel
from apps.requirement.models import RequirementModel
from apps.suite.models import SuiteModel
# 公共工具
from common.utils import response_success, response_fail, get_caller

# ==================== 日志配置 ====================
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ==================== 路由配置 ====================
router = APIRouter(
    prefix="/api/ai",
    tags=['AI测试评估模块'],
    responses={
        404: {"description": "资源未找到"},
        500: {"description": "服务器内部错误"}
    }
)


# ==================== RAG评估相关接口 ====================

@router.post('/rag/evaluate', summary="异步RAG评估接口")
async def ai_rag_evaluate(
        request: RagEvaluationDataRequest,
        background_tasks: BackgroundTasks,
        user_id: str = Depends(get_caller)
) -> Dict[str, Any]:
    """
    异步RAG评估接口

    功能说明：
    - 提交RAG评估任务到后台异步执行
    - 支持自动去重，防止重复提交相同任务
    - 支持超时检测和任务状态跟踪
    - 返回任务ID用于后续状态查询

    Args:
        request: RAG评估请求参数
        background_tasks: FastAPI后台任务管理器
        user_id: 当前用户ID

    Returns:
        Dict[str, Any]: 包含任务ID和状态的响应数据

    Raises:
        IntegrityError: 数据库完整性约束错误
        Exception: 其他系统异常
    """
    logger.info(f"用户 {user_id} 提交RAG评估任务，suite_sequence_id: {request.suite_sequence_id}")

    try:
        async with in_transaction():
            # ========== 1. 检查是否存在正在运行的任务 ==========
            existing_task = await EvaluateTaskModel.filter(
                suite_sequence_id=request.suite_sequence_id,
                status__in=[TaskStatus.PENDING, TaskStatus.RUNNING],
            ).first()

            if existing_task:
                logger.warning(f"发现正在执行的任务，task_id: {existing_task.id}")
                return response_fail(
                    msg=f"已有正在执行的任务（状态：{existing_task.status}）",
                    data={
                        "existing_task_id": existing_task.id,
                        "task_status": existing_task.status,
                        "start_time": existing_task.start_time.isoformat(),
                        "suggestion": "请等待当前任务完成或调用重试接口"
                    }
                )

            # ========== 2. 创建新的评估任务记录 ==========
            status_record = await EvaluateTaskModel.create(
                evaluate_type=EvaluationType.RAG,
                suite_sequence_id=request.suite_sequence_id,
                status=TaskStatus.PENDING,
                start_time=datetime.now(),
                original_request=request.model_dump()
            )

            logger.info(f"创建RAG评估任务成功，task_id: {status_record.id}")

    except IntegrityError as e:
        logger.error(
            f"RAG评估任务创建失败，suite_sequence_id={request.suite_sequence_id}, 错误：{str(e)}",
            exc_info=True
        )
        return response_fail(
            msg="任务创建失败，可能存在数据完整性问题",
            data={"error_type": "IntegrityError", "suggestion": "请检查请求参数或联系管理员"}
        )
    except Exception as e:
        logger.error(f"RAG评估任务创建异常：{str(e)}", exc_info=True)
        return response_fail(msg="系统异常，请稍后重试")

    # ========== 3. 提交后台异步任务 ==========
    background_tasks.add_task(
        execute_rag_evaluation_task,
        request,
        user_id,
        status_record.id
    )

    logger.info(f"RAG评估后台任务已提交，task_id: {status_record.id}")

    return response_success(data={
        "task_id": status_record.id,
        "status": TaskStatus.PENDING,
        "start_time": status_record.start_time.isoformat(),
        "message": "评估任务已提交，请通过task_id查询执行状态"
    })


@router.get('/rag/evaluate/status/{task_id}', summary="查询RAG评估状态")
async def get_rag_evaluate_status(task_id: int) -> Dict[str, Any]:
    """
    查询RAG评估任务状态

    功能说明：
    - 通过任务ID查询评估任务的执行状态
    - 自动检测并处理超时任务（默认1小时超时）
    - 返回详细的任务执行信息和结果数据
    - 支持实时状态更新和错误信息获取

    Args:
        task_id: 评估任务ID

    Returns:
        Dict[str, Any]: 任务状态和结果信息

    Status说明：
        1 - 待执行, 2 - 执行中, 3 - 已完成, 4 - 执行失败, 5 - 执行超时
    """
    logger.info(f"查询RAG评估任务状态，task_id: {task_id}")

    # ========== 1. 获取任务记录 ==========
    record = await EvaluateTaskModel.get_or_none(id=task_id)
    if not record:
        logger.warning(f"任务不存在，task_id: {task_id}")
        return response_fail(
            msg="任务不存在",
            data={"task_id": task_id, "suggestion": "请检查任务ID是否正确"}
        )

    # ========== 2. 自动检测并处理超时任务 ==========
    if record.status == TaskStatus.RUNNING:
        # 计算任务运行时长
        current_time = datetime.now(pytz.utc)
        start_time = record.start_time
        if start_time.tzinfo is None:
            start_time = pytz.utc.localize(start_time)

        time_elapsed = current_time - start_time
        timeout_threshold = timedelta(hours=1)  # 1小时超时阈值

        if time_elapsed > timeout_threshold:
            logger.warning(f"任务超时，task_id: {task_id}, 运行时长: {time_elapsed}")
            await EvaluateTaskModel.filter(id=task_id).update(
                status=TaskStatus.TIMEOUT,
                end_time=datetime.now(),
                error=f"任务执行超时，运行时长: {time_elapsed}"
            )
            record.status = TaskStatus.TIMEOUT
            record.end_time = datetime.now()

    # ========== 3. 构建基础响应数据 ==========
    response_data = {
        "task_id": record.id,
        "status": record.status,
        "status_description": {
            TaskStatus.PENDING: "待执行",
            TaskStatus.RUNNING: "执行中",
            TaskStatus.COMPLETED: "已完成",
            TaskStatus.FAILED: "执行失败",
            TaskStatus.TIMEOUT: "执行超时"
        }.get(record.status, "未知状态"),
        "execution_times": record.execution_times,
        "start_time": record.start_time.isoformat(),
        "end_time": record.end_time.isoformat() if record.end_time else None,
        "duration_seconds": (
            (record.end_time - record.start_time).total_seconds()
            if record.end_time else None
        )
    }

    # ========== 4. 处理已完成任务的结果数据 ==========
    if record.status == TaskStatus.COMPLETED and record.results:
        try:
            # 对RAG指标进行数值格式化处理
            processed_data = []
            for item in record.results:
                processed_item = {}
                for key, value in item.items():
                    if key in RAG_CORE_METRICS and isinstance(value, (int, float)):
                        processed_item[key] = round(value, 2)
                    else:
                        processed_item[key] = value
                processed_data.append(processed_item)

            response_data["results"] = processed_data
            response_data["results_summary"] = {
                "total_cases": len(processed_data),
                "metrics_count": len(RAG_CORE_METRICS)
            }

        except Exception as e:
            logger.error(f"处理结果数据时出错，task_id: {task_id}, 错误: {str(e)}")
            response_data["results_error"] = "结果数据处理异常"

    # ========== 5. 处理失败和超时任务的错误信息 ==========
    if record.status in [TaskStatus.FAILED, TaskStatus.TIMEOUT]:
        response_data["error"] = record.error or "未知错误"
        response_data["retry_suggestion"] = "可以调用重试接口重新执行任务"

    logger.info(f"RAG评估任务状态查询完成，task_id: {task_id}, status: {record.status}")
    return response_success(data=response_data)


@router.post('/rag/evaluate/retry/{task_id}', summary="重试RAG评估任务")
async def retry_rag_evaluate_task(
        task_id: int,
        background_tasks: BackgroundTasks,
        user_id: str = Depends(get_caller)
) -> Dict[str, Any]:
    """
    重试失败的RAG评估任务

    功能说明：
    - 重新执行失败或超时的RAG评估任务
    - 验证原始任务状态，确保只有失败/超时任务可以重试
    - 继承原始任务的请求参数，创建新的任务记录
    - 提交到后台异步执行，返回新任务ID

    Args:
        task_id: 原始任务ID
        background_tasks: FastAPI后台任务管理器
        user_id: 当前用户ID

    Returns:
        Dict[str, Any]: 包含新任务ID和重试信息的响应数据

    允许重试的状态：
        4 - 执行失败, 5 - 执行超时
    """
    logger.info(f"用户 {user_id} 请求重试RAG评估任务，original_task_id: {task_id}")

    # ========== 1. 获取并验证原始任务 ==========
    original_task = await EvaluateTaskModel.get_or_none(id=task_id)
    if not original_task:
        logger.warning(f"原始任务不存在，task_id: {task_id}")
        return response_fail(
            msg="原始任务不存在",
            data={"task_id": task_id, "suggestion": "请检查任务ID是否正确"}
        )

    # ========== 2. 验证任务状态是否允许重试 ==========
    allowed_statuses = [TaskStatus.FAILED, TaskStatus.TIMEOUT]
    if original_task.status not in allowed_statuses:
        logger.warning(f"任务状态不允许重试，task_id: {task_id}, status: {original_task.status}")
        return response_fail(
            msg=f"当前状态 [{original_task.status}] 不允许重试",
            data={
                "current_status": original_task.status,
                "allowed_statuses": allowed_statuses,
                "status_description": {
                    TaskStatus.PENDING: "待执行",
                    TaskStatus.RUNNING: "执行中",
                    TaskStatus.COMPLETED: "已完成",
                    TaskStatus.FAILED: "执行失败",
                    TaskStatus.TIMEOUT: "执行超时"
                }.get(original_task.status, "未知状态"),
                "suggestion": "只有失败或超时的任务才能重试"
            }
        )

    try:
        # ========== 3. 解析原始请求参数 ==========
        if not original_task.original_request:
            logger.error(f"原始任务缺少请求参数，task_id: {task_id}")
            return response_fail(
                msg="原始任务缺少请求参数，无法重试",
                data={"suggestion": "请重新提交评估任务"}
            )

        request_data = original_task.original_request
        rag_request = RagEvaluationDataRequest(**request_data)

        # ========== 4. 创建新的重试任务记录 ==========
        async with in_transaction():
            new_task = await EvaluateTaskModel.create(
                evaluate_type=EvaluationType.RAG,
                suite_sequence_id=original_task.suite_sequence_id,
                status=TaskStatus.PENDING,
                start_time=datetime.now(),
                original_request=request_data  # 继承原始请求参数
            )

        logger.info(f"创建重试任务成功，new_task_id: {new_task.id}, original_task_id: {task_id}")

        # ========== 5. 提交后台重试任务 ==========
        background_tasks.add_task(
            execute_rag_evaluation_task,
            rag_request,
            user_id,
            new_task.id
        )

        logger.info(f"RAG评估重试任务已提交，new_task_id: {new_task.id}")

        return response_success(data={
            "new_task_id": new_task.id,
            "original_task_id": task_id,
            "retry_user": user_id,
            "retry_time": new_task.start_time.isoformat(),
            "message": "重试任务已提交，请通过new_task_id查询执行状态"
        })

    except ValueError as e:
        logger.error(f"请求参数验证失败，task_id: {task_id}, 错误: {str(e)}")
        return response_fail(
            msg="原始请求参数格式错误",
            data={"validation_errors": str(e), "suggestion": "请重新提交评估任务"}
        )
    except Exception as e:
        logger.error(f"重试任务创建异常，task_id: {task_id}, 错误: {str(e)}", exc_info=True)
        return response_fail(
            msg="重试任务创建失败",
            data={"error_type": type(e).__name__, "suggestion": "请稍后重试或联系管理员"}
        )


@router.get('/rag/evaluate', summary="RAG评估结果查询列表接口")
async def ai_rag_evaluate_result(
        query_params: RagEvaluationResultQuery = Depends(),
        page: int = Query(1, ge=1, description="页码"),
        size: int = Query(10, ge=1, le=100, description="每页数量"),
        user_id: str = Depends(get_caller)
) -> Dict[str, Any]:
    """
    RAG评估结果列表查询接口

    功能说明：
    - 支持多维度条件筛选（项目、需求、套件、版本等）
    - 支持按指标和分数区间进行精确筛选
    - 支持关键词模糊搜索（用户输入和系统响应）
    - 支持分页查询，提高查询性能
    - 自动关联项目、需求、套件名称信息

    Args:
        query_params: 查询参数对象，包含各种筛选条件
        page: 页码，从1开始
        size: 每页数量，限制1-100
        user_id: 当前用户ID

    Returns:
        Dict[str, Any]: 包含评估结果列表和分页信息的响应数据

    筛选条件说明：
        - type_id: 评估类型ID
        - project_id: 项目ID
        - requirement_id: 需求ID
        - suite_id: 测试套件ID
        - version: 版本号（模糊匹配）
        - keyword: 关键词（在用户输入和系统响应中搜索）
        - metric: 指标名称（配合score_range使用）
        - score_range: 分数区间（0.6以下、0.6-0.7、0.7-0.8、0.8-0.9、0.9以上）
    """
    logger.info(f"用户 {user_id} 查询RAG评估结果，page: {page}, size: {size}")

    try:
        # ========== 1. 构建基础查询对象 ==========
        query = RAGEvaluateResultModel.all()
        filters = []

        # ========== 2. 构建基础过滤条件 ==========
        if query_params.type_id:
            filters.append(Q(type_id=query_params.type_id))

        if query_params.project_id:
            filters.append(Q(project_id=query_params.project_id))

        if query_params.requirement_id:
            filters.append(Q(requirement_id=query_params.requirement_id))

        if query_params.version:
            filters.append(Q(version__icontains=query_params.version))

        if query_params.suite_id:
            filters.append(Q(suite_id=query_params.suite_id))

        if query_params.material_id:
            filters.append(Q(material_id=query_params.material_id))

        # ========== 3. 构建关键词搜索条件 ==========
        if query_params.keyword:
            keyword_filter = (
                    Q(user_input__icontains=query_params.keyword) |
                    Q(response__icontains=query_params.keyword)
            )
            filters.append(keyword_filter)
            logger.info(f"添加关键词搜索条件: {query_params.keyword}")

        # ========== 4. 构建指标和分数区间过滤条件 ==========
        if query_params.metric:
            # 验证指标名称是否有效
            if query_params.metric not in RAG_CORE_METRICS:
                logger.warning(f"无效的指标名称: {query_params.metric}")
                return response_fail(
                    msg=f"无效的指标名称: {query_params.metric}",
                    data={"valid_metrics": RAG_CORE_METRICS}
                )

            metric_field = query_params.metric

            if query_params.score_range:
                # 定义分数区间映射
                range_filters = {
                    "0.6以下": Q(**{f"{metric_field}__lt": 0.6}),
                    "0.6-0.7": Q(**{f"{metric_field}__gte": 0.6, f"{metric_field}__lt": 0.7}),
                    "0.7-0.8": Q(**{f"{metric_field}__gte": 0.7, f"{metric_field}__lt": 0.8}),
                    "0.8-0.9": Q(**{f"{metric_field}__gte": 0.8, f"{metric_field}__lt": 0.9}),
                    "0.9以上": Q(**{f"{metric_field}__gte": 0.9})
                }

                if query_params.score_range in range_filters:
                    filters.append(range_filters[query_params.score_range])
                    logger.info(f"添加分数区间过滤: {query_params.metric} {query_params.score_range}")
                else:
                    logger.warning(f"无效的分数区间: {query_params.score_range}")
                    return response_fail(
                        msg=f"无效的分数区间: {query_params.score_range}",
                        data={"valid_ranges": list(range_filters.keys())}
                    )

        # ========== 5. 应用所有过滤条件并执行查询 ==========
        if filters:
            query = query.filter(Q(*filters, join_type="AND"))

        # 按创建时间倒序排列
        query = query.order_by("-created_time")

        # 获取总数（用于分页）
        total = await query.count()

        # 执行分页查询
        results = await query.offset((page - 1) * size).limit(size)

        # ========== 6. 丰富结果数据（添加关联信息） ==========
        enriched_results = await enrich_evaluation_records(results, "rag")

        # ========== 7. 格式化响应数据 ==========
        formatted_results = [RAGEvaluateResultResponse.from_orm(r) for r in enriched_results]

        logger.info(f"RAG评估结果查询完成，返回 {len(formatted_results)} 条记录，总计 {total} 条")

        return response_success(
            data=formatted_results,
            total=total
        )

    except Exception as e:
        logger.error(f"RAG评估结果查询失败，错误信息：{str(e)}", exc_info=True)
        return response_fail(
            msg="评估数据查询失败",
            data={"error_type": type(e).__name__, "suggestion": "请检查查询参数或稍后重试"}
        )


@router.get('/rag/evaluate/{id}', summary="RAG评估结果详情接口")
async def ai_rag_evaluate_detail(
        id: int,
        user_id: str = Depends(get_caller)
) -> Dict[str, Any]:
    """
    RAG评估结果详情查询接口

    功能说明：
    - 根据评估结果ID获取详细的评估信息
    - 自动关联并返回项目、需求、套件的名称信息
    - 提供完整的RAG评估指标和上下文信息
    - 支持权限验证，确保数据安全

    Args:
        id: 评估结果记录ID
        user_id: 当前用户ID

    Returns:
        Dict[str, Any]: 包含详细评估信息的响应数据

    返回数据包含：
        - 基础信息：用户输入、系统响应、参考答案等
        - 评估指标：5个核心RAG指标的详细分数
        - 关联信息：项目名称、需求名称、套件名称
        - 执行信息：执行时间、执行次数、评估人员等
    """
    logger.info(f"用户 {user_id} 查询RAG评估详情，record_id: {id}")

    try:
        # ========== 1. 查询评估结果记录 ==========
        result = await RAGEvaluateResultModel.get_or_none(id=id)
        if not result:
            logger.warning(f"RAG评估结果不存在，record_id: {id}")
            return response_fail(
                msg="评估结果不存在",
                data={"record_id": id, "suggestion": "请检查记录ID是否正确"}
            )

        # ========== 2. 丰富关联信息 ==========
        enriched_results = await enrich_evaluation_records([result], "rag")
        enriched_result = enriched_results[0] if enriched_results else result

        # ========== 3. 格式化响应数据 ==========
        formatted_result = RAGEvaluateResultResponse.from_orm(enriched_result)

        # ========== 4. 添加额外的详情信息 ==========
        detail_data = formatted_result.model_dump()

        # 添加指标统计信息
        metrics_summary = {}
        for metric in RAG_CORE_METRICS:
            value = getattr(enriched_result, metric, None)
            if value is not None:
                metrics_summary[metric] = {
                    "value": round(float(value), 2),
                    "description": {
                        "context_precision": "上下文精确度：检索到的上下文与问题的相关性",
                        "context_recall": "上下文召回率：检索到相关上下文的完整性",
                        "context_entity_recall": "上下文实体召回率：关键实体信息的召回程度",
                        "faithfulness": "忠实度：生成答案与检索上下文的一致性",
                        "answer_relevancy": "答案相关性：生成答案与用户问题的相关程度"
                    }.get(metric, "")
                }

        detail_data["metrics_summary"] = metrics_summary
        detail_data["total_metrics"] = len([v for v in metrics_summary.values() if v["value"] > 0])

        logger.info(f"RAG评估详情查询完成，record_id: {id}")

        return response_success(data=detail_data)

    except Exception as e:
        logger.error(f"RAG评估详情查询失败，record_id: {id}, 错误: {str(e)}", exc_info=True)
        return response_fail(
            msg="评估详情查询失败",
            data={"error_type": type(e).__name__, "suggestion": "请稍后重试或联系管理员"}
        )


@router.put('/rag/evaluate/{id}', summary="更新RAG评估结果接口")
async def ai_rag_evaluate_update(
        id: int,
        request: RagEvaluationDataUpdateRequest,
        user_id: str = Depends(get_caller)
) -> Dict[str, Any]:
    """
    更新RAG评估结果接口

    功能说明：
    - 允许用户更新评估结果的备注和评分信息
    - 记录更新操作的执行人和时间
    - 支持部分字段更新，保持数据完整性
    - 提供详细的操作日志和错误处理

    Args:
        id: 评估结果记录ID
        request: 更新请求数据（包含备注和评分）
        user_id: 当前用户ID

    Returns:
        Dict[str, Any]: 更新后的评估结果数据

    更新字段：
        - remark: 评估备注
        - score: 人工评分
        - executor: 执行人（自动设置为当前用户）
        - updated_time: 更新时间（自动设置）
    """
    logger.info(f"用户 {user_id} 更新RAG评估结果，record_id: {id}")

    try:
        # ========== 1. 查询待更新的记录 ==========
        result = await RAGEvaluateResultModel.get_or_none(id=id)
        if not result:
            logger.warning(f"RAG评估结果不存在，record_id: {id}")
            return response_fail(
                msg="评估数据不存在",
                data={"record_id": id, "suggestion": "请检查记录ID是否正确"}
            )

        # ========== 2. 记录更新前的状态（用于日志） ==========
        old_remark = result.remark
        old_score = result.score
        old_executor = result.executor

        # ========== 3. 更新字段值 ==========
        if request.remark is not None:
            result.remark = request.remark

        if request.score is not None:
            # 验证评分范围（假设评分范围为0-100）
            if not (0 <= request.score <= 100):
                return response_fail(
                    msg="评分超出有效范围",
                    data={"valid_range": "0-100", "provided_score": request.score}
                )
            result.score = request.score

        result.executor = user_id

        # ========== 4. 保存更新 ==========
        await result.save()

        # ========== 5. 记录操作日志 ==========
        changes = []
        if old_remark != result.remark:
            changes.append(f"备注: '{old_remark}' -> '{result.remark}'")
        if old_score != result.score:
            changes.append(f"评分: {old_score} -> {result.score}")
        if old_executor != result.executor:
            changes.append(f"执行人: '{old_executor}' -> '{result.executor}'")

        logger.info(f"RAG评估结果更新成功，record_id: {id}, 变更: {'; '.join(changes)}")

        # ========== 6. 返回更新后的数据 ==========
        # 丰富关联信息
        enriched_results = await enrich_evaluation_records([result], "rag")
        enriched_result = enriched_results[0] if enriched_results else result

        # 格式化响应
        formatted_result = RAGEvaluateResultResponse.from_orm(enriched_result)

        return response_success(
            data=formatted_result.model_dump(),
            msg="评估结果更新成功"
        )

    except Exception as e:
        logger.error(f"RAG评估结果更新失败，record_id: {id}, 错误: {str(e)}", exc_info=True)
        return response_fail(
            msg="评估数据更新失败",
            data={"error_type": type(e).__name__, "suggestion": "请稍后重试或联系管理员"}
        )


@router.get('/rag/compare/result', summary="RAG结果对比接口")
async def ai_rag_compare_evaluations(
        suite_id: int = Query(0, description="测试套件ID"),
        execution_times_after: int = Query(..., description="对比执行次数（新）"),
        execution_times_before: int = Query(..., description="对比执行次数（旧）"),
        user_id=Depends(get_caller)
):
    """
    RAG评估结果对比接口

    功能说明：
    - 对比同一测试套件在不同执行次数下的RAG评估结果
    - 支持多个评估指标的横向对比分析
    - 返回详细的指标变化趋势（提升/持平/下降）

    参数说明：
    - suite_id: 测试套件ID，用于筛选特定套件的评估结果
    - execution_times_after: 新版本的执行次数（作为对比基准）
    - execution_times_before: 旧版本的执行次数（作为对比参照）

    返回结果：
    - 每个测试用例的详细对比数据
    - 包含5个核心RAG指标的变化情况
    - result字段表示变化趋势：high(提升)/same(持平)/low(下降)
    """

    # ========== 1. 参数验证 ==========
    if not suite_id or suite_id == 0:
        return response_fail(msg="测试用例集suite_id不能为空或为0")

    # 验证执行次数参数的合理性
    if execution_times_after == execution_times_before:
        return response_fail(msg="新旧版本执行次数不能相同")

    if execution_times_after < 0 or execution_times_before < 0:
        return response_fail(msg="执行次数不能为负数")

    # ========== 2. 构建数据库查询条件 ==========
    # 基础查询条件：按测试套件ID筛选
    base_query = Q(suite_id=suite_id)

    try:
        # ========== 3. 并发查询两个版本的评估结果 ==========
        async with in_transaction():
            # 查询新版本（after）的评估结果
            results_after = await RAGEvaluateResultModel.filter(
                base_query & Q(execution_times=execution_times_after)
            ).values()

            # 查询旧版本（before）的评估结果
            results_before = await RAGEvaluateResultModel.filter(
                base_query & Q(execution_times=execution_times_before)
            ).values()

        # ========== 4. 数据存在性验证 ==========
        if not results_after or not results_before:
            missing_versions = []
            if not results_after:
                missing_versions.append(f"新版本(执行次数:{execution_times_after})")
            if not results_before:
                missing_versions.append(f"旧版本(执行次数:{execution_times_before})")

            return response_fail(
                msg=f"缺少评估数据：{', '.join(missing_versions)}",
                data={
                    "suite_id": suite_id,
                    "missing_execution_times": {
                        "after": execution_times_after if not results_after else None,
                        "before": execution_times_before if not results_before else None
                    }
                }
            )

    except Exception as e:
        logger.error(f"查询评估结果失败，suite_id={suite_id}, 错误信息：{str(e)}")
        return response_fail(msg="数据库查询失败，请稍后重试")

    # ========== 5. 数据预处理和匹配 ==========
    # 将结果列表转换为字典，以user_input作为唯一标识符进行快速查找
    # 这样可以高效地匹配同一测试用例在不同版本下的评估结果
    after_dict = {item['user_input']: item for item in results_after}
    before_dict = {item['user_input']: item for item in results_before}

    # 找出两个版本都存在的测试用例（取交集）
    # 只有同时存在于两个版本中的测试用例才能进行有效对比
    common_user_inputs = sorted(set(after_dict.keys()) & set(before_dict.keys()))

    # 验证是否有可对比的测试用例
    if not common_user_inputs:
        return response_fail(
            msg="没有共同的测试用例可供比较",
            data={
                "after_count": len(results_after),
                "before_count": len(results_before),
                "after_inputs": list(after_dict.keys())[:5],  # 显示前5个用于调试
                "before_inputs": list(before_dict.keys())[:5]
            }
        )

    # ========== 6. 构建详细对比结果 ==========
    comparison_results = []

    # 定义需要对比的RAG核心评估指标
    # 这些指标反映了RAG系统在不同维度的性能表现
    rag_metrics = [
        "context_precision",  # 上下文精确度：检索到的上下文与问题的相关性
        "context_recall",  # 上下文召回率：检索到相关上下文的完整性
        "context_entity_recall",  # 上下文实体召回率：关键实体信息的召回程度
        "faithfulness",  # 忠实度：生成答案与检索上下文的一致性
        "answer_relevancy"  # 答案相关性：生成答案与用户问题的相关程度
    ]

    # 遍历每个共同的测试用例进行详细对比
    for user_input in common_user_inputs:
        after_record = after_dict[user_input]
        before_record = before_dict[user_input]

        # 构建单个测试用例的对比数据结构
        compare_item = {
            "user_input": user_input,  # 用户输入问题
            "retrieved_contexts": after_record.get('retrieved_contexts'),  # 检索到的上下文（使用新版本的）
            "response": after_record.get('response'),  # 生成的回答（使用新版本的）
            "reference": after_record.get('reference'),  # 参考答案
            "compare": []  # 各指标的详细对比结果
        }

        # ========== 7. 逐个指标进行对比分析 ==========
        for metric in rag_metrics:
            # 检查指标是否在两个版本的记录中都存在
            if metric in after_record and metric in before_record:
                # 获取指标值
                val_after = after_record.get(metric)
                val_before = before_record.get(metric)

                # 使用辅助函数计算对比结果
                comparison_result = calculate_metric_comparison(val_after, val_before)

                # 构建单个指标的对比结果
                compare_item["compare"].append({
                    "field": metric,  # 指标名称
                    **comparison_result  # 展开对比结果
                })

        comparison_results.append(compare_item)

    # ========== 9. 构建最终响应数据 ==========
    response_data = {
        "suite_id": suite_id,
        "execution_times_after": execution_times_after,
        "execution_times_before": execution_times_before,
        "comparison_summary": {
            "total_test_cases": len(comparison_results),  # 总测试用例数
            "compared_metrics": len(rag_metrics),  # 对比指标数量
            "after_total_cases": len(results_after),  # 新版本总用例数
            "before_total_cases": len(results_before),  # 旧版本总用例数
        },
        "comparison_results": comparison_results
    }

    return response_success(data=response_data)


# ==================== LLM评估相关接口 ====================

@router.post('/llm/evaluate', summary="异步LLM评估接口")
async def ai_llm_evaluate(
        request: ConversationsRequest,
        background_tasks: BackgroundTasks,
        user_id: str = Depends(get_caller)
) -> Dict[str, Any]:
    """
    异步LLM评估接口

    功能说明：
    - 提交LLM对话评估任务到后台异步执行
    - 支持自动去重，防止重复提交相同任务
    - 支持任务状态跟踪和超时检测
    - 评估LLM在对话场景下的性能表现

    Args:
        request: LLM评估请求参数（包含对话数据和模型配置）
        background_tasks: FastAPI后台任务管理器
        user_id: 当前用户ID

    Returns:
        Dict[str, Any]: 包含任务ID和状态的响应数据

    评估指标：
        - agent_goal_accuracy: 智能体目标准确性
        - precision: 精确率
        - recall: 召回率
        - f1: F1分数
    """
    logger.info(f"用户 {user_id} 提交LLM评估任务，suite_sequence_id: {request.suite_sequence_id}")

    try:
        async with in_transaction():
            # ========== 1. 检查是否存在正在运行的任务 ==========
            existing_task = await EvaluateTaskModel.filter(
                suite_sequence_id=request.suite_sequence_id,
                status__in=[TaskStatus.PENDING, TaskStatus.RUNNING],
            ).first()

            if existing_task:
                logger.warning(f"发现正在执行的LLM评估任务，task_id: {existing_task.id}")
                return response_fail(
                    msg=f"已有正在执行的LLM评估任务（状态：{existing_task.status}）",
                    data={
                        "existing_task_id": existing_task.id,
                        "task_status": existing_task.status,
                        "start_time": existing_task.start_time.isoformat(),
                        "suggestion": "请等待当前任务完成或调用重试接口"
                    }
                )

            # ========== 2. 创建新的LLM评估任务记录 ==========
            status_record = await EvaluateTaskModel.create(
                evaluate_type=EvaluationType.LLM,
                suite_sequence_id=request.suite_sequence_id,
                status=TaskStatus.PENDING,
                start_time=datetime.now(),
                original_request=request.model_dump()
            )

            logger.info(f"创建LLM评估任务成功，task_id: {status_record.id}")

    except Exception as e:
        logger.error(f"LLM评估任务创建异常：{str(e)}", exc_info=True)
        return response_fail(
            msg="LLM评估任务创建失败",
            data={"error_type": type(e).__name__, "suggestion": "请稍后重试或联系管理员"}
        )

    # ========== 3. 提交后台异步任务 ==========
    background_tasks.add_task(
        execute_llm_evaluation_task,
        request,
        user_id,
        status_record.id
    )

    logger.info(f"LLM评估后台任务已提交，task_id: {status_record.id}")

    return response_success(data={
        "task_id": status_record.id,
        "status": TaskStatus.PENDING,
        "start_time": status_record.start_time.isoformat(),
        "message": "LLM评估任务已提交，请通过task_id查询执行状态"
    })


@router.get('/llm/evaluate/{id}', summary="LLM评估结果详情接口")
async def ai_llm_evaluate_detail(
        id: int,
        user_id: str = Depends(get_caller)
) -> Dict[str, Any]:
    """
    LLM评估结果详情查询接口

    功能说明：
    - 根据评估结果ID获取详细的LLM评估信息
    - 自动关联并返回项目、需求、套件的名称信息
    - 提供完整的LLM评估指标和对话上下文信息
    - 支持权限验证，确保数据安全

    Args:
        id: LLM评估结果记录ID
        user_id: 当前用户ID

    Returns:
        Dict[str, Any]: 包含详细LLM评估信息的响应数据

    返回数据包含：
        - 基础信息：对话内容、参考答案、参考主题等
        - 评估指标：4个核心LLM指标的详细分数
        - 关联信息：项目名称、需求名称、套件名称
        - 执行信息：执行时间、执行次数、评估人员等
    """
    logger.info(f"用户 {user_id} 查询LLM评估详情，record_id: {id}")

    try:
        # ========== 1. 查询LLM评估结果记录 ==========
        result = await LLMEvaluateResultModel.get_or_none(id=id)
        if not result:
            logger.warning(f"LLM评估结果不存在，record_id: {id}")
            return response_fail(
                msg="LLM评估结果不存在",
                data={"record_id": id, "suggestion": "请检查记录ID是否正确"}
            )

        # ========== 2. 丰富关联信息 ==========
        enriched_results = await enrich_evaluation_records([result], "llm")
        enriched_result = enriched_results[0] if enriched_results else result

        # ========== 3. 格式化响应数据 ==========
        formatted_result = LLMEvaluateResultResponse.from_orm(enriched_result)

        # ========== 4. 添加额外的详情信息 ==========
        detail_data = formatted_result.model_dump()

        # 添加LLM指标统计信息
        metrics_summary = {}
        for metric in LLM_CORE_METRICS:
            value = getattr(enriched_result, metric, None)
            if value is not None:
                metrics_summary[metric] = {
                    "value": round(float(value), 2),
                    "description": {
                        "agent_goal_accuracy": "智能体目标准确性：评估智能体是否准确理解并完成预期目标",
                        "precision": "精确率：评估模型预测结果的准确程度",
                        "recall": "召回率：评估模型识别相关信息的完整程度",
                        "f1": "F1分数：精确率和召回率的调和平均数"
                    }.get(metric, "")
                }

        detail_data["metrics_summary"] = metrics_summary
        detail_data["total_metrics"] = len([v for v in metrics_summary.values() if v["value"] > 0])

        logger.info(f"LLM评估详情查询完成，record_id: {id}")

        return response_success(data=detail_data)

    except Exception as e:
        logger.error(f"LLM评估详情查询失败，record_id: {id}, 错误: {str(e)}", exc_info=True)
        return response_fail(
            msg="LLM评估详情查询失败",
            data={"error_type": type(e).__name__, "suggestion": "请稍后重试或联系管理员"}
        )


@router.get('/llm/evaluate', summary="LLM评估结果查询列表接口")
async def ai_llm_evaluate_results(
        query_params: LLMEvaluationResultQuery = Depends(),
        page: int = Query(1, ge=1, description="页码"),
        size: int = Query(10, ge=1, le=100, description="每页数量"),
        user_id=Depends(get_caller)
):
    try:
        query = LLMEvaluateResultModel.all()
        filters = []

        # 基础过滤条件
        if query_params.type_id:
            filters.append(Q(type=query_params.type_id))
        if query_params.project_id:
            filters.append(Q(project_id=query_params.project_id))
        if query_params.requirement_id:
            filters.append(Q(requirement_id=query_params.requirement_id))
        if query_params.version:
            filters.append(Q(version__icontains=query_params.version))
        if query_params.suite_id:
            filters.append(Q(suite_id=query_params.suite_id))
        if query_params.keyword:
            filters.append(
                Q(conversation__icontains=query_params.keyword) |
                Q(reference__icontains=query_params.keyword) |
                Q(reference_topics__icontains=query_params.keyword)
            )

        # 指标和分数区间过滤
        if query_params.metric:
            metric_field = f"{query_params.metric}"

            if query_params.score_range:
                # 处理分数区间查询
                range_filters = {
                    "0.6以下": Q(**{f"{metric_field}__lt": 0.6}),
                    "0.6-0.7": Q(**{f"{metric_field}__gte": 0.6, f"{metric_field}__lt": 0.7}),
                    "0.7-0.8": Q(**{f"{metric_field}__gte": 0.7, f"{metric_field}__lt": 0.8}),
                    "0.8-0.9": Q(**{f"{metric_field}__gte": 0.8, f"{metric_field}__lt": 0.9}),
                    "0.9以上": Q(**{f"{metric_field}__gte": 0.9})
                }
                filters.append(range_filters[query_params.score_range])
            # 处理数值范围查询
            value_filters = []
            if value_filters:
                filters.append(Q(*value_filters, join_type="AND"))
        # 应用所有过滤条件
        if filters:
            query = query.filter(Q(*filters, join_type="AND"))
        # 添加排序
        query = query.order_by("-created_time")
        # 获取数量总量
        total = await query.count()
        results = await query.offset((page - 1) * size).limit(size)
        for res in results:
            if res.project_id:
                pro_res = await ProjectModel.get_or_none(id=res.project_id)
                res.project_name = pro_res.name
            if res.requirement_id:
                req_res = await RequirementModel.get_or_none(id=res.requirement_id)
                res.requirement_name = req_res.name
            if res.suite_id:
                suite_res = await SuiteModel.get_or_none(id=res.suite_id)
                res.suite_name = suite_res.name
        # 格式化结果
        formatted_results = [LLMEvaluateResultResponse.from_orm(r) for r in results]
        return response_success(data=formatted_results, total=total)
    except Exception as e:
        logger.error(f"查询数据库失败，错误信息：{e}")
        return response_fail(msg="LLM评估结果数据查询失败")


# ==================== 人工评估相关接口 ====================

@router.post('/manual/evaluate', summary="批量创建人工评估结果")
async def manual_evaluate(
        request: List[ManualEvaluationDataRequest],
        user_id: str = Depends(get_caller)
) -> Dict[str, Any]:
    """
    批量创建人工评估结果接口

    功能说明：
    - 支持批量提交人工评估结果数据
    - 自动记录评估执行人和时间
    - 提供数据验证和重复检查
    - 支持事务处理，确保数据一致性

    Args:
        request: 人工评估数据列表，包含多个评估记录
        user_id: 当前用户ID，自动设置为评估执行人

    Returns:
        Dict[str, Any]: 批量创建结果和统计信息

    """
    # 将字典转换为模型实例
    model_instances = [ManualEvaluateResultModel(executor=user_id, **res.model_dump()) for res in request]
    # 存入数据库
    try:
        await ManualEvaluateResultModel.bulk_create(model_instances)
        return response_success()
    except Exception as e:
        logger.error(f"存入数据库失败，错误信息：{e}")
        return response_fail(msg="评估数据入库失败")


@router.put('/manual/evaluate/{id}', summary="更新人工评估结果")
async def manual_evaluate_update(
        id: int,
        request: ManualEvaluationUpdateDataRequest,
        user_id: str = Depends(get_caller)
) -> Dict[str, Any]:
    """
    更新人工评估结果接口

    功能说明：
    - 更新指定ID的人工评估记录
    - 支持更新评分、备注等字段
    - 自动记录更新人和更新时间
    - 提供详细的操作日志和变更记录

    Args:
        id: 评估记录ID
        request: 更新数据，包含新的评分和备注
        user_id: 当前用户ID，自动设置为更新执行人

    Returns:
        Dict[str, Any]: 更新后的评估记录数据

    可更新字段：
        - score: 人工评分（0-100）
        - remark: 评估备注
        - executor: 执行人（自动设置为当前用户）
    """
    res = await ManualEvaluateResultModel.get_or_none(id=id)
    if not res:
        return response_fail(msg="评估数据不存在")
    res.remark = request.remark
    res.score = request.score
    res.executor = user_id
    try:
        await res.save()
        return response_success(data=res)
    except Exception as e:
        logger.error(f"更新数据库失败，错误信息：{e}")
        return response_fail(msg="人工评估数据更新失败")


@router.get('/manual/evaluate', summary="人工评估结果查询列表")
async def manual_evaluate_result(
        query_params: ManualEvaluationResultQuery = Depends(),
        page: int = Query(1, ge=1, description="页码"),
        size: int = Query(10, ge=1, le=100, description="每页数量"),
        user_id: str = Depends(get_caller)
) -> Dict[str, Any]:
    """
    人工评估结果查询列表接口

    功能说明：
    - 分页查询人工评估结果列表
    - 支持多维度筛选条件
    - 支持关键词模糊搜索
    - 按创建时间倒序排列
    - 自动关联项目、需求、套件信息

    Args:
        query_params: 查询参数，包含筛选条件
        page: 页码，从1开始
        size: 每页数量，限制1-100
        user_id: 当前用户ID

    Returns:
        Dict[str, Any]: 包含评估结果列表和分页信息的响应数据

    查询条件：
        - version: 版本号（模糊匹配）
        - suite_id: 测试套件ID
        - keyword: 关键词（在用户输入和系统响应中搜索）

    返回数据包含：
        - 基础信息：用户输入、系统响应、参考答案
        - 评估信息：人工评分、备注、执行人
        - 关联信息：套件ID、版本信息
        - 时间信息：创建时间、更新时间
    """
    logger.info(f"用户 {user_id} 查询人工评估结果列表，page: {page}, size: {size}")

    try:
        # ========== 1. 构建基础查询 ==========
        query = ManualEvaluateResultModel.all()

        # ========== 2. 构建筛选条件 ==========
        filters = []

        if query_params.version:
            filters.append(Q(version__icontains=query_params.version))
            logger.debug(f"添加版本筛选: {query_params.version}")

        if query_params.suite_id:
            filters.append(Q(suite_id=query_params.suite_id))
            logger.debug(f"添加套件ID筛选: {query_params.suite_id}")

        if query_params.keyword:
            keyword_filter = (
                    Q(user_input__icontains=query_params.keyword) |
                    Q(response__icontains=query_params.keyword)
            )
            filters.append(keyword_filter)
            logger.debug(f"添加关键词搜索: {query_params.keyword}")

        # ========== 3. 应用筛选条件 ==========
        if filters:
            query = query.filter(Q(*filters, join_type="AND"))
            logger.debug(f"应用了 {len(filters)} 个筛选条件")

        # ========== 4. 添加排序和分页 ==========
        query = query.order_by("-created_time")
        total = await query.count()
        results = await query.offset((page - 1) * size).limit(size)

        # ========== 5. 丰富结果数据 ==========
        enriched_results = await enrich_evaluation_records(results, "manual")

        # ========== 6. 格式化响应数据 ==========
        formatted_results = []
        for record in enriched_results:
            formatted_record = {
                "id": record.id,
                "user_input": record.user_input,
                "response": record.response,
                "reference": record.reference,
                "score": record.score,
                "remark": record.remark,
                "executor": record.executor,
                "project_id": record.project_id,
                "project_name": getattr(record, 'project_name', None),
                "requirement_id": record.requirement_id,
                "requirement_name": getattr(record, 'requirement_name', None),
                "suite_id": record.suite_id,
                "suite_name": getattr(record, 'suite_name', None),
                "version": record.version,
                "created_time": record.created_time.isoformat(),
                "updated_time": record.updated_time.isoformat() if record.updated_time else None
            }
            formatted_results.append(formatted_record)

        logger.info(f"人工评估结果查询完成，返回 {len(formatted_results)} 条记录，总计 {total} 条")

        return response_success(
            data=formatted_results,
            total=total,
        )
    except Exception as e:
        logger.error(f"人工评估结果查询失败: {str(e)}", exc_info=True)
        return response_fail(
            msg="评估数据查询失败",
            data={"error_type": type(e).__name__, "suggestion": "请稍后重试或联系管理员"}
        )


@router.get('/manual/evaluate/{id}', summary="人工评估结果详情接口")
async def manual_evaluate_update(id: int, user_id=Depends(get_caller)):
    """
    人工评估结果详情
    """
    result = await ManualEvaluateResultModel.get_or_none(id=id)
    if not result:
        raise response_fail(msg="数据查询失败")
    return response_success(data=result)


@router.get('/llm/compare/result', summary="LLM结果对比接口")
async def llm_compare_evaluations(
        suite_id: int = Query(0, description="测试套件ID"),
        execution_times_after: int = Query(..., description="对比执行次数（新）"),
        execution_times_before: int = Query(..., description="对比执行次数（旧）"),
        user_id=Depends(get_caller)
):
    """
    对比不同执行次数的评估结果（支持suite/test_case双维度）
    """
    # 参数验证
    if not suite_id:
        return response_fail(msg="测试用例集suite_id不能为空")

    # 动态构建查询条件
    base_query = Q()
    if suite_id and suite_id != 0:
        base_query &= Q(suite_id=suite_id)

    # 获取两次执行结果
    async with in_transaction():
        results_after = await LLMEvaluateResultModel.filter(
            base_query & Q(execution_times=execution_times_after)
        ).values()

        results_before = await LLMEvaluateResultModel.filter(
            base_query & Q(execution_times=execution_times_before)
        ).values()

    # 结果验证
    if not results_after or not results_before:
        missing = []
        if not results_after:
            missing.append(f"新版本{execution_times_after}")
        if not results_before:
            missing.append(f"旧版本{execution_times_before}")
        return response_fail(msg=f"执行记录不存在：{', '.join(missing)}")

    # 使用 conversation 作为唯一标识符
    after_dict = {item['conversation']: item for item in results_after}
    before_dict = {item['conversation']: item for item in results_before}

    # 获取共同的conversation
    common_conversations = sorted(set(after_dict.keys()) & set(before_dict.keys()))

    if not common_conversations:
        return response_fail(msg="没有共同的测试用例可供比较")

    # 构建对比结果
    comparison_results = []

    for conversations in common_conversations:
        after_record = after_dict[conversations]
        before_record = before_dict[conversations]

        # 提取基础字段
        compare_item = {
            "conversations": conversations,
            "reference": after_record.get('reference'),
            "reference_topics": after_record.get('reference_topics'),
            "compare": []
        }

        # 获取所有需要比较的字段
        all_keys = set(after_record.keys()) | set(before_record.keys())

        for key in sorted(all_keys):
            # 排除非业务指标字段
            if key in ["agent_goal_accuracy", "precision", "recall", "f1"]:
                # 获取值
                # 对指定字段进行保留两位小数处理
                val_after = round(after_record.get(key), 2) if isinstance(after_record.get(key),
                                                                          (int, float)) else after_record.get(key)
                val_before = round(before_record.get(key), 2) if isinstance(before_record.get(key),
                                                                            (int, float)) else before_record.get(key)

                # 计算比较结果
                if val_after > val_before:
                    result = "high"
                elif val_after == val_before:
                    result = "same"
                else:  # val_after < val_before
                    result = "low"

                # 添加比较结果
                compare_item["compare"].append({
                    "field": key,
                    "after_value": val_after,
                    "before_value": val_before,
                    "result": result
                })
        comparison_results.append(compare_item)

    return response_success(data={
        "suite_id": suite_id,
        "execution_times_after": execution_times_after,
        "execution_times_before": execution_times_before,
        "comparison_results": comparison_results
    })


@router.get('/evaluate/task', summary="评估任务查询列表接口")
async def evaluate_task_result(
        query_params: EvaluationTaskQuery = Depends(),
        page: int = Query(1, ge=1, description="页码"),
        size: int = Query(10, ge=1, le=100, description="每页数量"),
        user_id=Depends(get_caller)
):
    """
    评估任务列表查询

    :param query_params: 查询参数
    :param page: 分页
    :param size: 每页的数量
    :return:
    """
    try:
        query = EvaluateTaskModel.all()
        filters = []

        # 基础过滤条件
        if query_params.suite_sequence_id:
            filters.append(Q(suite_sequence_id=query_params.suite_sequence_id))
        if query_params.status:
            filters.append(Q(status=query_params.status))

        # 应用所有过滤条件
        if filters:
            query = query.filter(Q(*filters, join_type="AND"))

        # 排序和分页
        query = query.order_by("-start_time")
        total = await query.count()
        results = await query.offset((page - 1) * size).limit(size)
        for res in results:
            if res.results != '' and res.results is not None:
                if res.evaluate_type == 'RAG':
                    # 需要处理的字段列表
                    metrics = [
                        'answer_relevancy',
                        'context_entity_recall',
                        'context_precision',
                        'context_recall',
                        'faithfulness'
                    ]
                else:
                    metrics = [
                        'agent_goal_accuracy',
                        'precision',
                        'recall',
                        'f1'
                    ]
                processed_data = [
                    {
                        key: round(value if value is not None else 0.0, 2) if key in metrics else value
                        for key, value in (item.items() if isinstance(item, dict) else [])
                        # 添加类型检查，如果是字典就遍历items()
                    }
                    for item in res.results
                ]
                res.results = processed_data
        # 格式化结果
        formatted_results = [EvaluateTaskResultResponse.model_validate(r) for r in results]
        return response_success(data=formatted_results, total=total)
    except Exception as e:
        logger.error(f"查询数据库失败，错误信息：{e}")
        return response_fail(msg="评估数据查询失败")


async def execute_rag_evaluation_task(
        request: RagEvaluationDataRequest,
        user_id: str,
        task_id: int
):
    """Rag实际执行评估任务"""
    try:
        # 更新任务状态为2-running
        await EvaluateTaskModel.filter(id=task_id).update(
            status=2,
            start_time=datetime.now()
        )
        # 执行评估逻辑
        results = await rag_evaluator.evaluate(request.model_id, request.embedding_model_id, request.suite_sequence_id)
        # 处理评估结果
        processed_results = convert_numpy_types(results)
        # 数据库操作
        async with in_transaction():
            max_record = await RAGEvaluateResultModel.filter(
                suite_sequence_id=request.suite_sequence_id,
            ).order_by("-execution_times").select_for_update().first()

            current_max = max_record.execution_times if max_record else 0
            new_execution_times = current_max + 1

            model_instances = [
                RAGEvaluateResultModel(
                    type_id=request.type_id,
                    suite_sequence_id=request.suite_sequence_id,
                    executor=user_id,
                    execution_times=new_execution_times,
                    **res
                )
                for res in processed_results
            ]
            await RAGEvaluateResultModel.bulk_create(model_instances)
        # 更新任务状态
        await EvaluateTaskModel.filter(id=task_id).update(
            status=3,
            execution_times=new_execution_times,
            end_time=datetime.now(),
            results=processed_results  # 存储结果数据
        )
    except Exception as e:
        error_msg = f"Task failed: {str(e)}"
        logger.error(error_msg, exc_info=True)
        await EvaluateTaskModel.filter(id=task_id).update(
            status=4,
            end_time=datetime.now(),
            error=error_msg
        )


async def execute_llm_evaluation_task(
        request: ConversationsRequest,
        user_id: str,
        task_id: int
):
    """Rag实际执行评估任务"""
    try:
        # 更新任务状态为running
        await EvaluateTaskModel.filter(id=task_id).update(
            status=2,
            start_time=datetime.now()
        )
        # 执行评估逻辑
        results = await llm_evaluation.evaluate(request.suite_sequence_id, request.model_id)
        # 处理评估结果
        processed_results = convert_numpy_types(results)
        # 数据库操作
        async with in_transaction():
            max_record = await LLMEvaluateResultModel.filter(
                suite_sequence_id=request.suite_sequence_id,
            ).order_by("-execution_times").select_for_update().first()

            current_max = max_record.execution_times if max_record else 0
            new_execution_times = current_max + 1

            # 将字典转换为模型实例
            model_instances = [LLMEvaluateResultModel(type_id=request.type_id,
                                                      suite_sequence_id=request.suite_sequence_id,
                                                      executor=user_id,
                                                      execution_times=new_execution_times, **res) for res in results]
            # 在调用 bulk_create 之前添加数据清洗逻辑
            for instance in model_instances:
                # 将所有浮点数字段中的nan替换为None
                float_fields = ['agent_goal_accuracy', 'precision', 'recall', 'f1']
                for field in float_fields:
                    value = getattr(instance, field)
                    if isinstance(value, float) and math.isnan(value):
                        setattr(instance, field, 0.0)  # 转换为0.0
            await LLMEvaluateResultModel.bulk_create(model_instances)

            # 更新任务状态
            await EvaluateTaskModel.filter(id=task_id).update(
                status=3,
                execution_times=new_execution_times,
                end_time=datetime.now(),
                results=processed_results  # 存储结果数据
            )
    except Exception as e:
        error_msg = f"Task failed: {str(e)}"
        logger.error(error_msg, exc_info=True)
        await EvaluateTaskModel.filter(id=task_id).update(
            status=4,
            end_time=datetime.now(),
            error=error_msg
        )


def convert_numpy_types(obj):
    """
    递归转换所有 NumPy 类型到 Python 原生类型
    """
    if isinstance(obj, np.generic):
        return obj.item()
    elif isinstance(obj, dict):
        return {k: convert_numpy_types(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(v) for v in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_numpy_types(v) for v in obj)
    else:
        return obj


# ==================== 辅助函数 ====================

def safe_numeric_conversion(value: Any, default: float = 0.0) -> float:
    """
    安全的数值转换函数

    Args:
        value: 待转换的值
        default: 转换失败时的默认值

    Returns:
        float: 转换后的浮点数
    """
    if value is None:
        return default

    if isinstance(value, (int, float)):
        if math.isnan(value) or math.isinf(value):
            return default
        return round(float(value), 2)

    try:
        converted = float(value)
        if math.isnan(converted) or math.isinf(converted):
            return default
        return round(converted, 2)
    except (ValueError, TypeError):
        return default


def calculate_metric_comparison(val_after: float, val_before: float) -> Dict[str, Any]:
    """
    计算单个指标的对比结果

    Args:
        val_after: 新版本指标值
        val_before: 旧版本指标值

    Returns:
        Dict[str, Any]: 包含变化趋势、变化率和绝对变化量的字典
        {
            "after_value": float,      # 新版本指标值
            "before_value": float,     # 旧版本指标值
            "result": str,             # 变化趋势：high/same/low
            "change_rate": float,      # 变化率（百分比）
            "absolute_change": float   # 绝对变化量
        }
    """
    # 使用安全转换函数进行数值标准化
    val_after = safe_numeric_conversion(val_after)
    val_before = safe_numeric_conversion(val_before)

    # 计算变化趋势
    if val_after > val_before:
        result = "high"  # 指标提升
    elif val_after == val_before:
        result = "same"  # 指标持平
    else:
        result = "low"  # 指标下降

    # 计算变化率（避免除零错误）
    if val_before != 0:
        change_rate = round(((val_after - val_before) / val_before * 100), 2)
    else:
        # 特殊情况处理：从0变为非0视为100%增长，从0到0为0%变化
        change_rate = 0.0 if val_after == 0 else 100.0

    # 计算绝对变化量
    absolute_change = round(val_after - val_before, 2)

    return {
        "after_value": val_after,
        "before_value": val_before,
        "result": result,
        "change_rate": change_rate,
        "absolute_change": absolute_change
    }


async def enrich_evaluation_records(records: List[Any], record_type: str = "rag") -> List[Any]:
    """
    丰富评估记录，添加关联的项目、需求、套件信息

    Args:
        records: 评估记录列表
        record_type: 记录类型，用于确定处理逻辑

    Returns:
        List[Any]: 丰富后的记录列表
    """
    for record in records:
        try:
            # 添加项目信息
            if hasattr(record, 'project_id') and record.project_id:
                project = await ProjectModel.get_or_none(id=record.project_id)
                if project:
                    record.project_name = project.name

            # 添加需求信息
            if hasattr(record, 'requirement_id') and record.requirement_id:
                requirement = await RequirementModel.get_or_none(id=record.requirement_id)
                if requirement:
                    record.requirement_name = requirement.name

            # 添加套件信息
            if hasattr(record, 'suite_id') and record.suite_id:
                suite = await SuiteModel.get_or_none(id=record.suite_id)
                if suite:
                    record.suite_name = suite.name

        except Exception as e:
            logger.warning(f"丰富记录信息时出错: {str(e)}")
            continue

    return records
