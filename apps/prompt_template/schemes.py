from typing import Optional, List
from pydantic import BaseModel


class PromptTemplateAdd(BaseModel):
    name: str
    content: str
    description: str = None


class PromptTemplateUpdate(PromptTemplateAdd):
    id: int


prompt_template_response_key = [
    "id",
    "name",
    "content",
    "description"
]

prompt_template_response_key_manage = [
    "id",
    "name",
    "content",
    "creator_id",
    "creator__username",
    "description",
    "updater_id",
    "updater__username",
    "create_time",
    "update_time"
]
