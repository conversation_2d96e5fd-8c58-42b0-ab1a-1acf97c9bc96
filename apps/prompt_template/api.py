import math
from fastapi import APIRouter, Request
from apps.prompt_template.models import PromptTemplateModel
from apps.prompt_template.schemes import prompt_template_response_key, prompt_template_response_key_manage, \
    PromptTemplateAdd, PromptTemplateUpdate
from common.utils import response_success, response_success_paged, is_superuser, response_fail, get_caller_id, \
    model_data_exist

prompt_template_router = APIRouter(prefix="")


@prompt_template_router.get("/prompt_template", summary='用户获取全部提示词模版', tags=['提示词模版'])
async def get_prompt_template_all(page: int = 1, page_size: int = 10):
    """
    获取全部提示词模版
    :param page:
    :param page_size:
    :return:
    """
    all_template = await PromptTemplateModel.all() \
        .limit(page_size) \
        .offset((page - 1) * page_size) \
        .values(*prompt_template_response_key)
    total_count = await PromptTemplateModel.all().count()

    return response_success_paged(
        data=all_template,
        total=total_count,
        current_page=page,
        total_page=math.ceil(total_count / page_size)
    )


@prompt_template_router.get("/manage/prompt_template", summary='后台管理获取全部提示词模版', tags=['提示词模版'])
async def get_all_prompt_template(request: Request, page: int = 1, page_size: int = 10):
    """
    后台管理获取全部提示词模版
    :param request:
    :param page:
    :param page_size:
    :return:
    """
    if await is_superuser(request):
        all_template = await PromptTemplateModel.all() \
            .limit(page_size) \
            .offset((page - 1) * page_size) \
            .values(*prompt_template_response_key_manage)
        total_count = await PromptTemplateModel.all().count()

        return response_success_paged(
            data=all_template,
            total=total_count,
            current_page=page,
            total_page=math.ceil(total_count / page_size)
        )
    else:
        return response_fail(msg="无权限")


@prompt_template_router.get("/manage/prompt_template/{id}", summary='后台管理获取一个提示词模版', tags=['提示词模版'])
async def get_one_prompt_template(id: int, request: Request):
    """
    后台管理获取一个提示词模版
    :param request:
    :param id:
    :return:
    """
    if await is_superuser(request):
        if await model_data_exist(id, PromptTemplateModel):
            prompt_template = await PromptTemplateModel.filter(id=id).values(*prompt_template_response_key_manage)
            return response_success(prompt_template, total=len(prompt_template))
        else:
            return response_fail(msg="对应提示词模版[{}]不存在".format(id))
    else:
        return response_fail(msg="无权限")


@prompt_template_router.post("/manage/prompt_template", summary='后台管理创建提示词模版', tags=['提示词模版'])
async def create_prompt_template(data: PromptTemplateAdd, request: Request):
    """
    后台管理创建提示词模版
    :param suite:
    :param request:
    :return:
    """
    if await is_superuser(request):
        caller_id = get_caller_id(request)
        prompt_template = await PromptTemplateModel.create(
            name=data.name,
            content=data.content,
            creator_id=caller_id,
            description=data.description,
            updater_id=caller_id,
        )
        return response_success(data={"id": prompt_template.id})
    else:
        return response_fail(msg="无权限")


@prompt_template_router.put("/manage/prompt_template", summary='后台管理更新提示词模版', tags=['提示词模版'])
async def update_prompt_template(data: PromptTemplateUpdate, request: Request):
    """
    后台管理更新提示词模版
    :param data:
    :param request:
    :return:
    """
    if await is_superuser(request):
        if await model_data_exist(data.id, PromptTemplateModel):
            caller_id = get_caller_id(request)
            count = await PromptTemplateModel.filter(id=data.id).update(
                name=data.name,
                content=data.content,
                description=data.description,
                updater_id=caller_id,
            )
            return response_success(total=count)
        else:
            return response_fail(msg="提示词模版[{}]不存在".format(data.id))
    else:
        return response_fail(msg="无权限")


@prompt_template_router.delete("/manage/prompt_template", summary='后台管理删除提示词模版', tags=['提示词模版'])
async def delete_prompt_template(id: int, request: Request):
    """
    后台管理删除提示词模版
    :param id:
    :param request:
    :return:
    """
    if await is_superuser(request):
        if await model_data_exist(id, PromptTemplateModel):
            await PromptTemplateModel.filter(id=id).delete()
            return response_success()
        else:
            return response_fail(msg="提示词模版[{}]不存在".format(id))
    else:
        return response_fail(msg="无权限")
