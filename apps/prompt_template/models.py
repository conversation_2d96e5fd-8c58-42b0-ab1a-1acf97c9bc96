from tortoise import models, fields


class PromptTemplateModel(models.Model):
    """
    提示词模版表
    """
    id = fields.IntField(pk=True, auto_increment=True)
    name = fields.CharField(max_length=255, description="名称")
    content = fields.TextField(description="模版内容")
    creator = fields.ForeignKeyField('models.UsersModel', related_name="temp_c", on_delete=fields.SET_NULL, null=True)
    description = fields.CharField(max_length=255, description="描述", null=True)
    updater = fields.ForeignKeyField('models.UsersModel', related_name="temp_u", on_delete=fields.SET_NULL, null=True)
    create_time = fields.data.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.data.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "prompt_template"
        table_description = "提示词模版表"
        ordering = ['-id']
