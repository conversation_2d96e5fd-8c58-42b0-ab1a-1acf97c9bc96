# @Time: 2025/4/3 09:28
# @Author: lvjing
from tortoise import models, fields


class LLMModel(models.Model):
    """
    LLM模型管理
    """
    id = fields.IntField(pk=True, auto_increment=True, description="主键id")
    model_name = fields.CharField(max_length=255, description="模型名称")
    api_base_url = fields.CharField(max_length=255, description="API base url")
    api_key = fields.CharField(max_length=255, description="API key")
    temperature = fields.FloatField(description="温度", default=0.0)
    top_p = fields.FloatField(description="概率累积达到", default=0.0)
    max_tokens = fields.IntField(description="最大token", default=4096)
    stream = fields.BooleanField(description="流式输出", default=False)
    remark = fields.CharField(max_length=255, description="备注", default="")
    creator = fields.Char<PERSON>ield(max_length=255, description="创建人", default="")
    created_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_time = fields.DatetimeField(auto_now=True, description="更新时间")

    def __str__(self):
        return self.model

    class Meta:
        table = "llm_model"
        table_description = "LLM模型管理"
