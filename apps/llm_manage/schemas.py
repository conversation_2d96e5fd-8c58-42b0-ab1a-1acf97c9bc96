# @Time: 2025/4/3 09:28
# @Author: lvjing
from datetime import datetime
from typing import Optional

from fastapi import Query
from pydantic import BaseModel, Field


class LLMModelRequest(BaseModel):
    """
    大模型管理请求入参
    """
    model_name: str = Field(description="大模型名称")
    api_base_url: str = Field(description="API base url")
    api_key: str = Field(description="API key", default=None)
    temperature: float = Field(description="生成结果的随机性控制，0为最确定，1为最随机", default=0.7, ge=0, le=1)
    top_p: float = Field(description="从累计概率达到这个阈值的那一组词中选择下一个词", default=0.8, ge=0, le=1)
    max_tokens: int = Field(description="生成结果的最大token数，None表示使用模型默认值", default=4096, ge=1)
    stream: bool = Field(description="流式输出", default=False)
    remark: str = Field(description="备注", default="")


class LLMModelResponse(BaseModel):
    """
    大模型管理响应出参
    """
    id: int = Field(description="主键id")
    model_name: str = Field(description="大模型名称")
    api_base_url: str = Field(description="API base url")
    api_key: str = Field(description="API key", default=None)
    temperature: float = Field(description="温度", default=0.0)
    top_p: float = Field(description="top_p", default=0.0)
    max_tokens: int = Field(description="最大token", default=None)
    stream: bool = Field(description="流式输出", default=False)
    remark: str = Field(description="备注", default="")
    creator: str = Field(description="创建人")
    created_time: datetime = Field(description="创建时间")


class LLMModelQuery(BaseModel):
    """
    大模型查询条件参数
    """
    model_name: Optional[str] = Query(None, description="评测框架类型")


class LLMChatRequest(BaseModel):
    """
    大模型聊天请求入参
    """
    messages: list = Field(description="聊天记录")
    model_name: str = Field(description="大模型名称")
    temperature: float = Field(description="生成结果的随机性控制，0为最确定，1为最随机", default=0.7, ge=0, le=1)
    top_p: float = Field(description="从累计概率达到这个阈值的那一组词中选择下一个词", default=0.8, ge=0, le=1)
    max_tokens: int = Field(description="生成结果的最大token数，None表示使用模型默认值", default=4096, ge=1)
    stream: bool = Field(description="流式输出", default=False)


# ==================== 常量定义 ====================
# 支持的模型类型
class ModelType:
    CHAT = "chat"  # 对话模型
    COMPLETION = "completion"  # 文本补全模型
    EMBEDDING = "embedding"  # 嵌入模型
    MULTIMODAL = "multimodal"  # 多模态模型


# 模型状态
class ModelStatus:
    ACTIVE = "active"  # 激活状态
    INACTIVE = "inactive"  # 非激活状态
    TESTING = "testing"  # 测试状态
    ERROR = "error"  # 错误状态


# 支持的模型提供商
SUPPORTED_PROVIDERS = [
    "openai",  # OpenAI官方
    "azure",  # Azure OpenAI
    "anthropic",  # Anthropic Claude
    "alibaba",  # 阿里云通义千问
    "baidu",  # 百度文心一言
    "tencent",  # 腾讯混元
    "custom"  # 自定义端点
]
