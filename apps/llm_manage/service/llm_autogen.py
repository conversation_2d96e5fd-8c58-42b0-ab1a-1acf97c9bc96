# @Time: 2025/6/18 10:40
# @Author: lvjing

from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient

from apps.llm_manage.models import LLMModel


def get_model_client(config: LLMModel):
    openai_model_client = OpenAIChatCompletionClient(
        model=config.model_name,  # 使用配置的默认模型
        api_key=config.api_key,  # API密钥
        base_url=config.api_base_url,  # API基础URL（可选）
        model_info={
            "vision": False,  # 不支持视觉功能
            "function_calling": True,  # 支持函数调用
            "json_output": True,  # 支持JSON输出
            "family": ModelFamily.UNKNOWN,  # 模型家族
            "structured_output": True,  # 支持结构化输出
            "multiple_system_messages": True,  # 支持多个系统消息
        }
    )
    return openai_model_client
