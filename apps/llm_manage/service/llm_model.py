#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import warnings
from typing import Optional, Any, List

from langchain.callbacks.manager import CallbackManagerForLLMRun
from langchain.llms.base import LLM
from langchain_openai import OpenAIEmbeddings
from openai import OpenAI

from apps.llm_manage.models import LLMModel

warnings.filterwarnings('ignore')


class LLMModelConfig(LLM):
    client: Optional[Any] = None
    model_name: str = None

    def __init__(self, config: LLMModel):
        super().__init__()
        self.model_name = config.model_name
        self.client = OpenAI(base_url=config.api_base_url,
                             api_key=config.api_key)

    def _call(self,
              prompt: str,
              stop: Optional[List[str]] = None,
              run_manager: Optional[CallbackManagerForLLMRun] = None,
              **kwargs: Any):
        completion = self.client.completions.create(model=self.model_name,
                                                    prompt=prompt,
                                                    temperature=kwargs.get('temperature', 0.1),
                                                    top_p=kwargs.get('top_p', 0.9),
                                                    max_tokens=kwargs.get('max_tokens', 4096),
                                                    stream=kwargs.get('stream', False))
        return completion.choices[0].text

    @property
    def _llm_type(self) -> str:
        return self.model_name


# class EmbeddingModel:
#     def __init__(self, model_path,
#                  device="cpu"):
#         self.embedding = HuggingFaceEmbeddings(model_name=model_path,
#                                                model_kwargs={"device": "cpu"})
#
#     def get_embedding_fun(self):
#         return self.embedding
class EmbeddingModel:
    def __init__(self, config: LLMModel):
        self.embedding = OpenAIEmbeddings(
            model=config.model_name,
            openai_api_base=config.api_base_url,
            openai_api_key=config.api_key
        )

    def get_embedding_fun(self):
        return self.embedding
