#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大语言模型管理模块API接口

该模块提供了大语言模型的完整管理功能，包括：
- 模型配置管理：LLM模型的创建、更新、删除和查询
- 模型验证：API连接测试和配置验证
- 模型服务：为其他模块提供统一的模型访问接口
- 模型监控：模型使用情况统计和性能监控

支持的模型类型：
- OpenAI兼容模型：GPT系列、Claude、通义千问等
- 自定义API端点：支持私有部署的模型服务
- 嵌入模型：文本向量化和语义搜索支持

@Time: 2025/4/3 09:28
@Author: lvjing
@LastModified: 2025/7/7
@Version: 2.0
"""

# ==================== 标准库导入 ====================
import logging
from typing import Dict, Any, Optional

# ==================== 第三方库导入 ====================
from fastapi import APIRouter, Query, Depends
from tortoise.exceptions import IntegrityError
from tortoise.expressions import Q

# ==================== 项目内部导入 ====================
# 数据模型
from apps.llm_manage.models import LLMModel
# 数据模式
from apps.llm_manage.schemas import LLMModelRequest, LLMModelQuery
# 公共工具
from common.utils import response_success, response_fail, get_caller

# ==================== 日志配置 ====================
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ==================== 路由配置 ====================
router = APIRouter(
    prefix="/api/llm",
    tags=['大模型管理模块'],
    responses={
        404: {"description": "模型未找到"},
        409: {"description": "模型配置冲突"},
        500: {"description": "模型服务异常"}
    }
)


# ==================== 辅助函数 ====================

async def validate_model_config(config: LLMModelRequest) -> Optional[str]:
    """
    验证模型配置的有效性

    Args:
        config: 模型配置请求对象

    Returns:
        Optional[str]: 验证失败时返回错误信息，成功时返回None
    """
    # 验证模型名称
    if not config.model_name or len(config.model_name.strip()) == 0:
        return "模型名称不能为空"

    # 验证API基础URL
    if not config.api_base_url or not config.api_base_url.startswith(('http://', 'https://')):
        return "API基础URL格式无效，必须以http://或https://开头"

    # 验证API密钥
    if not config.api_key or len(config.api_key.strip()) < 10:
        return "API密钥无效，长度至少为10个字符"
    return None


async def test_model_connection(config: LLMModelRequest) -> Dict[str, Any]:
    """
    测试模型连接

    Args:
        config: 模型配置

    Returns:
        Dict[str, Any]: 测试结果
    """
    try:
        # 这里可以添加实际的连接测试逻辑
        # 例如发送一个简单的请求来验证配置是否正确
        return {
            "success": True,
            "message": "连接测试成功",
            "latency": 0.5  # 模拟延迟
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"连接测试失败: {str(e)}",
            "latency": None
        }


# ==================== LLM模型管理接口 ====================

@router.post('/config/', summary='创建大模型配置')
async def create_llm_config(
        req: LLMModelRequest,
        user_id: str = Depends(get_caller)
) -> Dict[str, Any]:
    """
    创建大模型配置接口

    功能说明：
    - 创建新的大语言模型配置
    - 验证模型配置参数的有效性
    - 检查模型名称的唯一性
    - 支持多种模型提供商和自定义端点

    Args:
        req: 模型配置请求数据
        user_id: 当前用户ID

    Returns:
        Dict[str, Any]: 创建结果和模型配置信息

    配置参数：
        - model_name: 模型名称（必须唯一）
        - api_base_url: API基础URL
        - api_key: API密钥
        - description: 模型描述
        - provider: 模型提供商
    """
    logger.info(f"用户 {user_id} 创建大模型配置，模型名称: {req.model_name}")

    try:
        # ========== 1. 配置参数验证 ==========
        validation_error = await validate_model_config(req)
        if validation_error:
            logger.warning(f"模型配置验证失败: {validation_error}")
            return response_fail(
                msg=validation_error,
                data={"field_errors": {"config": validation_error}}
            )

        # ========== 2. 检查模型名称唯一性 ==========
        existing_model = await LLMModel.get_or_none(model_name=req.model_name)
        if existing_model:
            logger.warning(f"模型名称已存在: {req.model_name}")
            return response_fail(
                msg="大模型名称已存在",
                data={
                    "existing_model_id": existing_model.id,
                    "suggestion": "请使用不同的模型名称或更新现有配置"
                }
            )

        # ========== 3. 创建模型配置 ==========
        llm_model = await LLMModel.create(
            creator=user_id,
            **req.model_dump()
        )

        logger.info(f"大模型配置创建成功，ID: {llm_model.id}, 名称: {llm_model.model_name}")

        return response_success(
            data={
                "id": llm_model.id,
                "model_name": llm_model.model_name,
                "api_base_url": llm_model.api_base_url,
                "api_key": llm_model.api_key,
                "remark": llm_model.remark,
                "creator": llm_model.creator,
                "created_time": llm_model.created_time.isoformat()
            },
            msg="大模型配置创建成功"
        )

    except IntegrityError as e:
        logger.error(f"数据库完整性错误: {str(e)}")
        return response_fail(
            msg="配置创建失败，可能存在数据冲突",
            data={"error_type": "IntegrityError"}
        )
    except Exception as e:
        logger.error(f"创建大模型配置异常: {str(e)}", exc_info=True)
        return response_fail(
            msg="配置创建失败",
            data={"error_type": type(e).__name__, "suggestion": "请稍后重试或联系管理员"}
        )


@router.get('/config/', summary='查询大模型配置列表')
async def get_llm_configs(
        query_params: LLMModelQuery = Depends(),
        page: int = Query(1, ge=1, description="页码"),
        size: int = Query(10, ge=1, le=100, description="每页数量"),
        user_id: str = Depends(get_caller)
) -> Dict[str, Any]:
    """
    查询大模型配置列表接口

    功能说明：
    - 分页查询大模型配置列表
    - 支持按模型名称模糊搜索
    - 按创建时间倒序排列
    - 返回配置的基本信息（隐藏敏感信息如API密钥）

    Args:
        query_params: 查询参数，包含筛选条件
        page: 页码，从1开始
        size: 每页数量，限制1-100
        user_id: 当前用户ID

    Returns:
        Dict[str, Any]: 包含模型配置列表和分页信息的响应数据

    查询条件：
        - model_name: 模型名称（模糊匹配）
    """
    logger.info(f"用户 {user_id} 查询大模型配置列表，page: {page}, size: {size}")

    try:
        # ========== 1. 构建基础查询 ==========
        query = LLMModel.all()

        # ========== 2. 构建筛选条件 ==========
        filters = []
        if query_params.model_name:
            filters.append(Q(model_name__icontains=query_params.model_name))
            logger.debug(f"添加模型名称筛选: {query_params.model_name}")

        # ========== 3. 应用筛选条件 ==========
        if filters:
            query = query.filter(Q(*filters, join_type="AND"))
            logger.debug(f"应用了 {len(filters)} 个筛选条件")

        # ========== 4. 添加排序和分页 ==========
        query = query.order_by("-created_time")
        total = await query.count()
        results = await query.offset((page - 1) * size).limit(size)

        # ========== 5. 处理响应数据（隐藏敏感信息） ==========
        safe_results = []
        for model in results:
            safe_model = {
                "id": model.id,
                "model_name": model.model_name,
                "api_base_url": model.api_base_url,
                "api_key": model.api_key,
                "remark": model.remark,
                "temperature": model.temperature,
                "top_p": model.top_p,
                "max_tokens": model.max_tokens,
                "stream": model.stream,
                "creator": model.creator,
                "created_time": model.created_time.isoformat(),
                "updated_time": model.updated_time.isoformat() if model.updated_time else None,
            }
            safe_results.append(safe_model)

        logger.info(f"大模型配置列表查询成功，返回 {len(safe_results)} 条记录，总计 {total} 条")

        return response_success(
            data=safe_results,
            total=total
        )

    except Exception as e:
        logger.error(f"查询大模型配置列表失败: {str(e)}", exc_info=True)
        return response_fail(
            msg="大模型配置数据查询失败",
            data={"error_type": type(e).__name__, "suggestion": "请稍后重试或联系管理员"}
        )


@router.get('/config/{id}', summary='查询大模型配置信息详情接口')
async def get_llm_detail(id: int, user_id=Depends(get_caller)):
    """
    查询大模型配置信息详情
    """
    llm = await LLMModel.get_or_none(id=id)
    if not llm:
        return response_fail(msg="大模型配置不存在")
    return response_success(data=llm)


@router.put('/config/{id}', summary='更新大模型配置接口')
async def update_llm_config(id: int, req: LLMModelRequest, user_id=Depends(get_caller)):
    """
    更新大模型配置
    """
    llm = await LLMModel.get_or_none(id=id)
    if not llm:
        return response_fail(msg="大模型配置不存在")
    llm.creator = user_id
    await llm.update_from_dict(req.model_dump())
    await llm.save()
    return response_success(data=llm)


@router.delete('/config/{id}', summary='删除大模型配置接口')
async def delete_llm_config(id: int):
    """
    删除大模型配置
    """
    llm = await LLMModel.get_or_none(id=id)
    if not llm:
        return response_fail(msg="大模型配置不存在")
    await llm.delete()
    return response_success(data='删除成功')
