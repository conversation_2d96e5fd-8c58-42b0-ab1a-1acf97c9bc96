from typing import Optional, List
from pydantic import BaseModel


class SuiteAdd(BaseModel):
    name: str
    version: str
    suite_type: int
    requirement: int
    material: List[int] = None
    description: Optional[str] = None


class SuiteUpdate(SuiteAdd):
    id: int


case_response_key = [
    "id",
    "content_answer",
    "content_question",
    "content",
    "type_id",
    "type__name",
    "source_id",
    "source__name",
    "creator_id",
    "creator__username",
    "share",
    "update_time",
    "create_time",
    # "is_multiple",
    # "topics",
]

suite_response_key = [
    "id",
    "name",
    "version",
    "suite_type",
    "description",
    "requirement_id",
    "requirement__name",
    "creator_id",
    "creator__username",
    "create_time",
    "update_time",
]


project_keys = [
    "id",
    "name",
]

requirement_keys = [
    "id",
    "name",
    "version",
]

suite_keys = [
    "id",
    "name",
]