import math
from fastapi import APIRouter, Request
from tortoise.exceptions import DoesNotExist

from apps.material.models import MaterialModel
from apps.project.models import ProjectModel
from apps.project_member.models import ProjectMemberModel
from apps.requirement.models import RequirementModel
from apps.suite.models import SuiteModel
from apps.suite.schemes import suite_response_key, case_response_key, SuiteAdd, SuiteUpdate, project_keys, \
    requirement_keys, suite_keys
from apps.users.models import UsersModel
from common.utils import response_success, response_success_paged, is_superuser, response_fail, get_caller_id, \
    is_project_member, model_data_exist

suite_router = APIRouter(prefix="/suite")


@suite_router.get("/in_project", summary='获取项目下全部用例集', tags=['用例集'])
async def get_suite_all_project(project_id: int, request: Request, page: int = 1, page_size: int = 10):
    """
    查看项目下所有的用例集
    超管\项目成员
    超管\项目成员：项目下全部的用例集

    :param project_id:
    :param request:
    :param page:
    :param page_size:
    :return:
    """
    user_id = get_caller_id(request)
    # 查询项目的需求id列表
    requirement_ids = await RequirementModel.filter(project_id=project_id).values_list('id', flat=True)

    # 管理员/项目成员 可以看全部的
    if await is_superuser(request) or await is_project_member(project_id, user_id):
        all_suite = await SuiteModel.filter(requirement_id__in=requirement_ids) \
            .limit(page_size) \
            .offset((page - 1) * page_size) \
            .values(*suite_response_key)
        total_count = await SuiteModel.filter(requirement_id__in=requirement_ids).count()
    else:
        return response_fail(msg="无操作权限")

    return response_success_paged(
        data=all_suite,
        total=total_count,
        current_page=page,
        total_page=math.ceil(total_count / page_size)
    )


@suite_router.get("/in_requirement", summary='获取需求下全部用例集', tags=['用例集'])
async def get_suite_all_requirement(requirement_id: int, request: Request, page: int = 1, page_size: int = 10):
    """
    获取需求下全部用例集
    超管\项目成员
    超管\项目成员：需求下全部的用例集
    :param requirement_id:
    :param request:
    :param page:
    :param page_size:
    :return:
    """
    user_id = get_caller_id(request)
    if await model_data_exist(requirement_id, RequirementModel):
        requirement = await RequirementModel.get(id=requirement_id)
        # 超管\项目成员才有权限
        if await is_superuser(request) or await is_project_member(requirement.project_id, user_id):
            all_suite = await SuiteModel.filter(requirement_id=requirement_id) \
                .limit(page_size) \
                .offset((page - 1) * page_size) \
                .values(*suite_response_key)
            total_count = await SuiteModel.filter(requirement_id=requirement_id).count()
            return response_success_paged(
                data=all_suite,
                total=total_count,
                current_page=page,
                total_page=math.ceil(total_count / page_size)
            )
        else:
            return response_fail(msg="无操作权限")
    else:
        return response_fail(msg="需求id[{}]对应数据不存在".format(requirement_id))


@suite_router.get("/case", summary='获取用例集下全部用例信息', tags=['用例集'])
async def get_case_all(suite_id: int, request: Request, page: int = 1, page_size: int = 10):
    """
    获取用例集下全部用例信息
    超管/项目成员
    :param suite_id:
    :param request:
    :param page:
    :param page_size:
    :return:
    """

    if await model_data_exist(suite_id, SuiteModel):
        suite = await SuiteModel.get(id=suite_id)
        caller_id = get_caller_id(request)
        # 超管 / 项目成员 才有权限
        if await is_superuser(request) or await is_project_member(suite.project_id, caller_id):
            case = await suite.material.all() \
                .limit(page_size) \
                .offset((page - 1) * page_size) \
                .values(*case_response_key)
            count = await suite.material.all().count()

            return response_success_paged(
                data=case,
                total=count,
                current_page=page,
                total_page=math.ceil(count / page_size)
            )
        else:
            return response_fail(msg="无操作权限")
    else:
        return response_fail(msg="用例集[{}]不存在".format(suite_id))


@suite_router.post("/", summary='创建用例集', tags=['用例集'])
async def create_suite(suite: SuiteAdd, request: Request):
    """
    创建用例集
    超管/项目成员
    :param suite:
    :param request:
    :return:
    """
    if await model_data_exist(suite.requirement, RequirementModel):
        requirement = await RequirementModel.get(id=suite.requirement)
        project_id = requirement.project_id
        caller_id = get_caller_id(request)
        # 超管/项目成员才可创建
        if await is_superuser(request) or await is_project_member(project_id, caller_id):
            new_suite = await SuiteModel.create(
                name=suite.name,
                version=suite.version,
                suite_type=suite.suite_type,
                creator_id=caller_id,
                requirement_id=suite.requirement,
                description=suite.description
            )
            # 如果传递了所选物料，则添加
            if len(suite.material):
                material_list = await MaterialModel.filter(id__in=suite.material)
                await new_suite.material.add(*material_list)
            return response_success(data={'id': new_suite.id})
        else:
            return response_fail(msg="无操作权限")
    else:
        return response_fail(msg="需求[{}]不存在".format(suite.requirement_id))


@suite_router.put("/", summary='更新用例集', tags=['用例集'])
async def update_suite(suite: SuiteUpdate, request: Request):
    """
    更新用例集
    超管/创建人
    :param suite:
    :param request:
    :return:
    """
    caller_id = get_caller_id(request)
    # 超管/用例集创建人 才有权限
    if await model_data_exist(suite.id, SuiteModel):
        old_suite = await SuiteModel.get(id=suite.id)
        requirement = await old_suite.requirement

        if (await is_superuser(request) or
                (await is_project_member(requirement.project_id, caller_id) and old_suite.creator_id == caller_id)
        ):
            await SuiteModel.filter(id=suite.id).update(
                name=suite.name,
                version=suite.version,
                suite_type=suite.suite_type,
                requirement_id=suite.requirement,
                updater_id=caller_id,
                description=suite.description
            )

            # 删除旧的物料
            await old_suite.material.clear()
            # 添加新的
            material_list = await MaterialModel.filter(id__in=suite.material)
            await old_suite.material.add(*material_list)
            return response_success()
        else:
            return response_fail(msg="无权限")
    else:
        return response_fail(msg="用例集[{}]不存在")


@suite_router.delete("/", summary='删除用例集', tags=['用例集'])
async def delete_suite(id: int, request: Request):
    """
    删除用例集
    超管/创建人
    :param id:
    :param request:
    :return:
    """
    caller_id = get_caller_id(request)

    if await model_data_exist(id, SuiteModel):
        old_suite = await SuiteModel.get(id=id)
        if old_suite.creator_id == caller_id or await is_superuser(request):
            await old_suite.material.clear()
            await old_suite.delete()
            return response_success()
        else:
            return response_fail(msg="无权限")
    return response_fail(msg="用例集[{}]数据不存在".format(id))


@suite_router.get("/hierarchical/structure", summary='获取全部的层级结构', tags=['用例集'])
async def get_hierarchical_structure(request: Request):
    """
    获取 项目-需求-用例集的层级结构
    超管：全部数据
    非超管：权限内的数据
    :param request:
    :return:
    """

    try:
        caller = get_caller_id(request)
        user = await UsersModel.get(id=caller)
    except DoesNotExist:
        return response_fail(msg="未登陆，用户不存在")
    resp_data = await generate_hierarchical_structure(user)

    return response_success(data=resp_data.get('hierarchical_structure'))


@suite_router.get("/belong", summary='获取用例集所属的需求项目', tags=['用例集'])
async def get_suite_belong(request: Request):
    """
    获取用例集所属的需求项目
    """
    project_list = await ProjectModel.all().values(*project_keys)
    resp_data = await generate_hierarchical_structure_pure(project_list)
    transformed_data = transform_data(resp_data.get('hierarchical_structure'))
    resp_data = {}
    total = len(transformed_data.get('suite'))
    for key, value in transformed_data.get('suite').items():
        temp_dict = {'id': value['id'], 'name': value['name']}
        temp_dict.update(value.get('requirement')[0])
        temp_dict.update(value.get('project')[0])
        resp_data[key] = temp_dict

    return response_success(data=resp_data,total=total)


async def generate_hierarchical_structure(user: UsersModel):
    """
    生成用例集所属的需求项目
    """
    # 超管
    if user.role == 1:
        project_list = await ProjectModel.all().values(*project_keys)
    else:
        project_ids = await ProjectMemberModel.filter(user_id=user.id).values_list('project_id', flat=True)
        project_list = await (ProjectModel.filter(id__in=project_ids)).values(*project_keys)

    return await generate_hierarchical_structure_pure(project_list)


async def generate_hierarchical_structure_pure(project_list):
    """
    对给定的项目集进行数据归类处理
    :param project_list:
    :return:
    """
    level1_set = set()
    level2_set = set()
    level3_set = set()
    hierarchical_structure = list()
    resp_data = dict()

    for project in project_list:
        level1_set.add(project['id'])
        temp_dict_level1 = dict()
        temp_dict_level1['id'] = project['id']
        temp_dict_level1['value'] = project['id']
        temp_dict_level1['level'] = 1
        temp_dict_level1['label'] = project['name']
        temp_dict_level1['children'] = list()
        requirement_list = await RequirementModel.filter(project_id=project['id']).values(*requirement_keys)
        if len(requirement_list):
            for requirement in requirement_list:
                level2_set.add(requirement['id'])
                temp_dict_level2 = dict()
                temp_dict_level2['id'] = requirement['id']
                temp_dict_level2['value'] = requirement['id']
                temp_dict_level2['level'] = 2
                temp_dict_level2['label'] = requirement['name']
                temp_dict_level2['children'] = list()
                suite_list = await SuiteModel.filter(requirement_id=requirement['id']).values(*suite_keys)
                if len(suite_list):
                    for suite in suite_list:
                        level3_set.add(suite['id'])
                        temp_dict_level3 = dict()
                        temp_dict_level3['id'] = suite['id']
                        temp_dict_level3['value'] = suite['id']
                        temp_dict_level3['level'] = 3
                        temp_dict_level3['label'] = suite['name']
                        temp_dict_level2['children'].append(temp_dict_level3)
                temp_dict_level1['children'].append(temp_dict_level2)
        hierarchical_structure.append(temp_dict_level1)
    resp_data['level1'] = list(level1_set)
    resp_data['level2'] = list(level2_set)
    resp_data['level3'] = list(level3_set)
    resp_data['hierarchical_structure'] = hierarchical_structure
    return resp_data


def transform_data(hierarchical_structure):
    """
    纯数据处理，不涉及数据库操作
    统计 project 的 requirement 和 suite 信息
    统计 requirement 的 project 和 suite 信息
    统计 suite 的 project 和 requirement 信息
    :param hierarchical_structure:
    :return:
    """
    resp_dict = dict()
    resp_dict_project = dict()
    resp_dict_requirement = dict()
    resp_dict_suite = dict()
    for level1_data in hierarchical_structure:
        # 用于存储 project 的 suite 信息
        temp_list1_suite = []
        # 用于存储 project 的 requirement 信息
        temp_list1_requirement = []

        # 遍历 project 下的 requirement
        for level2_data in level1_data['children']:
            # 用于存储 requirement 的 suite 信息
            temp_list2_suite = []

            # 遍历 requirement 下的 suite
            for level3_data in level2_data['children']:
                resp_dict_suite[level3_data['id']] = {
                    'id': level3_data['id'],
                    'name': level3_data['label'],
                    'suite': [],
                    'requirement': [{
                        'requirement_id': level2_data['id'],
                        'requirement_name': level2_data['label']
                    }],
                    'project': [{
                        'project_id': level1_data['id'],
                        'project_name': level1_data['label']
                    }]
                }
                temp_list2_suite.append(level3_data['id'])

            # 存储 requirement 的 project 和 suite 信息
            resp_dict_requirement[level2_data['id']] = {
                'id': level2_data['id'],
                'name': level2_data['label'],
                "suite": temp_list2_suite,
                'requirement': [],
                'project': [{
                    'project_id': level1_data['id'],
                    'project_name': level1_data['label'],
                }]
            }

            # 更新 project 的 requirement 信息
            temp_list1_requirement.append(level2_data['id'])
            # 更新 project 的 suite 信息
            temp_list1_suite.extend(temp_list2_suite)

        resp_dict_project[level1_data['id']] = {
            'id': level1_data['id'],
            'name': level1_data['label'],
            "suite": temp_list1_suite,
            "requirement": temp_list1_requirement,
            'project': []
        }

    resp_dict['project'] = resp_dict_project
    resp_dict['requirement'] = resp_dict_requirement
    resp_dict['suite'] = resp_dict_suite
    return resp_dict
