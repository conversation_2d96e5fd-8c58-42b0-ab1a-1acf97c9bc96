from tortoise import models, fields


class SuiteModel(models.Model):
    """
    用例集表
    """
    id = fields.IntField(pk=True, auto_increment=True)
    name = fields.CharField(max_length=255)
    version = fields.CharField(max_length=255, description="需求版本")
    suite_type = fields.IntField(default=0,description="suite类型 0:RAGS 1:LLM")
    creator = fields.ForeignKeyField('models.UsersModel', related_name="suite", on_delete=fields.SET_NULL, null=True)
    # project = fields.ForeignKeyField('models.ProjectModel', related_name="suite", on_delete=fields.SET_NULL, null=True)
    requirement = fields.ForeignKeyField('models.RequirementModel', related_name="suite", on_delete=fields.SET_NULL,
                                         null=True)
    material = fields.ManyToManyField('models.MaterialModel', related_name="case", null=True)
    description = fields.CharField(max_length=255, description="用例集描述", null=True)
    updater = fields.ForeignKeyField('models.UsersModel', related_name="suite_update", on_delete=fields.SET_NULL, null=True)
    create_time = fields.data.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.data.DatetimeField(auto_now=True, description="更新时间")

    def __str__(self):
        return self.name

    class Meta:
        table = "suite"
        table_description = "用例集表"
        ordering = ['-id']
