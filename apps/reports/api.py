#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告管理模块API接口

该模块提供了AI测试评估报告的生成、查询和分析功能，包括：
- 项目统计报告：项目维度的测试数据统计和分析
- RAG评估报告：检索增强生成模型的评估结果统计
- LLM评估报告：大语言模型的性能评估报告
- 综合数据分析：多维度的测试结果对比和趋势分析

@Time: 2025/4/17 17:33
@Author: lvjing
@LastModified: 2025/7/7
@Version: 2.0
"""

# ==================== 标准库导入 ====================
import logging
from functools import lru_cache
from typing import Optional, Dict, Any

# ==================== 第三方库导入 ====================
from fastapi import APIRouter, Query, Depends
from tortoise.expressions import Q

# ==================== 项目内部导入 ====================
# 数据模型
from apps.ai_test.models import RAGEvaluateResultModel, LLMEvaluateResultModel
from apps.material.models import MaterialModel
from apps.project.models import ProjectModel
# 数据模式
from apps.reports.schemes import BaseQueryParams, ProjectStatResponse, RAG_METRICS
from apps.requirement.models import RequirementModel
from apps.suite.models import SuiteModel
# 公共工具
from common.utils import response_success, response_fail, get_caller

# ==================== 日志配置 ====================
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ==================== 路由配置 ====================
router = APIRouter(
    prefix="/api/reports",
    tags=['报告管理模块'],
    responses={
        404: {"description": "报告数据未找到"},
        500: {"description": "报告生成失败"}
    }
)


# ==================== 辅助函数 ====================

@lru_cache(maxsize=128)
def _build_cache_key(params: BaseQueryParams) -> str:
    """
    构建缓存键

    Args:
        params: 查询参数对象

    Returns:
        str: 缓存键字符串
    """
    return f"query_{params.project_id}_{params.version}_{params.suite_id}_{params.requirement_id}"


async def get_base_query(model, params: BaseQueryParams):
    """
    构建基础查询条件

    功能说明：
    - 根据查询参数构建数据库查询条件
    - 支持多维度筛选（项目、版本、套件、需求）
    - 提供查询结果缓存机制

    Args:
        model: 数据模型类（RAGEvaluateResultModel或LLMEvaluateResultModel）
        params: 查询参数对象，包含各种筛选条件

    Returns:
        QuerySet: 构建好的查询对象

    筛选条件：
        - project_id: 项目ID筛选
        - version: 版本号筛选
        - suite_id: 测试套件ID筛选
        - requirement_id: 需求ID筛选
    """
    logger.debug(f"构建基础查询条件，参数: {params}")

    # 创建基础查询对象
    query = model.all()

    # ========== 构建筛选条件 ==========
    if params.project_id:
        query = query.filter(project_id=params.project_id)
        logger.debug(f"添加项目ID筛选: {params.project_id}")

    if params.version:
        query = query.filter(version=params.version)
        logger.debug(f"添加版本筛选: {params.version}")

    if params.suite_id:
        query = query.filter(suite_id=params.suite_id)
        logger.debug(f"添加套件ID筛选: {params.suite_id}")

    if params.requirement_id:
        query = query.filter(requirement_id=params.requirement_id)
        logger.debug(f"添加需求ID筛选: {params.requirement_id}")

    return query


def calculate_score_distribution(ranges: Dict[str, tuple]) -> dict[str, int]:
    """
    初始化分数分布统计结构

    Args:
        ranges: 分数区间定义

    Returns:
        Dict[str, Dict[str, int]]: 初始化的统计结构
    """
    return {key: 0 for key in ranges.keys()}


# ==================== RAG评估报告接口 ====================

@router.get("/rag/statistics/", summary="RAG评估统计报告")
async def get_rag_statistics(
        params: BaseQueryParams = Depends(),
        user_id: str = Depends(get_caller)
) -> Dict[str, Any]:
    """
    RAG评估统计报告接口

    功能说明：
    - 生成RAG评估结果的统计分析报告
    - 按分数区间统计各项指标的分布情况
    - 支持多维度筛选条件
    - 提供详细的数据分析和可视化支持

    Args:
        params: 查询参数，包含筛选条件
        user_id: 当前用户ID

    Returns:
        Dict[str, Any]: RAG评估统计报告数据

    统计指标：
        - context_precision: 上下文精确度
        - context_recall: 上下文召回率
        - context_entity_recall: 上下文实体召回率
        - faithfulness: 忠实度
        - answer_relevancy: 答案相关性

    分数区间：
        - 0.6以下: 需要改进
        - 0.6-0.7: 一般
        - 0.7-0.8: 良好
        - 0.8-0.9: 优秀
        - 0.9以上: 卓越
    """
    logger.info(f"用户 {user_id} 请求RAG评估统计报告")

    try:
        # ========== 1. 参数验证 ==========
        if not any([params.project_id, params.version, params.suite_id, params.requirement_id]):
            logger.warning("RAG统计查询缺少必要参数")
            return response_fail(
                msg="必须提供至少一个查询条件",
                data={
                    "required_params": ["project_id", "version", "suite_id", "requirement_id"],
                    "suggestion": "请至少提供一个筛选条件以获取统计数据"
                }
            )

        # ========== 2. 构建查询条件 ==========
        base_query = await get_base_query(RAGEvaluateResultModel, params)

        # 定义分数区间
        score_ranges = {
            "0.6以下": (0, 0.6),  # 需要改进
            "0.6-0.7": (0.6, 0.7),  # 一般
            "0.7-0.8": (0.7, 0.8),  # 良好
            "0.8-0.9": (0.8, 0.9),  # 优秀
            "0.9以上": (0.9, 1.0)  # 卓越
        }

        # ========== 3. 初始化统计结构 ==========
        stats = {}
        for metric in RAG_METRICS:
            stats[metric] = calculate_score_distribution(score_ranges)

        # ========== 4. 统计各指标在不同分数区间的分布 ==========
        for metric, range_dict in stats.items():
            for label, (low, high) in score_ranges.items():
                try:
                    # 构建查询条件
                    if low == 0:
                        # 处理最低区间（包含0值）
                        count = await base_query.filter(**{f"{metric}__lte": high}).count()
                    elif high == 1.0:
                        # 处理最高区间（包含1值）
                        count = await base_query.filter(**{f"{metric}__gte": low}).count()
                    else:
                        # 处理中间区间
                        count = await base_query.filter(**{f"{metric}__gte": low, f"{metric}__lt": high}).count()

                    range_dict[label] = count

                except Exception as e:
                    logger.error(f"统计指标 {metric} 区间 {label} 时出错: {str(e)}")
                    range_dict[label] = 0

        # ========== 5. 计算总体统计信息 ==========
        total_records = await base_query.count()

        # 构建响应数据
        response_data = {
            "statistics": stats,
            "summary": {
                "total_records": total_records,
                "metrics_count": len(RAG_METRICS),
                "score_ranges": list(score_ranges.keys()),
                "query_params": {
                    "project_id": params.project_id,
                    "version": params.version,
                    "suite_id": params.suite_id,
                    "requirement_id": params.requirement_id
                }
            }
        }

        logger.info(f"RAG评估统计报告生成成功，总记录数: {total_records}")
        return response_success(data=response_data)

    except Exception as e:
        logger.error(f"生成RAG评估统计报告失败: {str(e)}", exc_info=True)
        return response_fail(
            msg="统计报告生成失败",
            data={"error_type": type(e).__name__, "suggestion": "请稍后重试或联系管理员"}
        )


@router.get("/rag/stats/", summary="RAG统计指标占有百分比")
async def get_rag_pie_stats(
        metric: Optional[str] = Query(description="指标名称"),
        params: BaseQueryParams = Depends()
):
    # 验证指标有效性
    if metric not in ["context_precision", "context_recall", "context_entity_recall", "faithfulness",
                      "answer_relevancy"]:
        return response_fail(msg="传入指标错误")

    # 验证至少有一个查询条件
    if not any([params.project_id, params.version, params.suite_id, params.requirement_id]):
        return response_fail(msg="必须提供至少一个查询条件(project_id/version/suite_id/requirement_id)")
    # 构建基础查询
    base_query = await get_base_query(RAGEvaluateResultModel, params)
    # 获取总数
    total_count = await base_query.count()
    if total_count == 0:
        return response_success(data=[])
    # 定义区间
    intervals = [
        (0.0, 0.6),
        (0.6, 0.7),
        (0.7, 0.8),
        (0.8, 0.9),
        (0.9, 1.0)
    ]
    labels = ["<0.6", "0.6-0.7", "0.7-0.8", "0.8-0.9", ">=0.9"]
    result = []
    for (low, high), label in zip(intervals, labels):
        query = base_query
        if high == 1.0:
            # 处理最后一个区间，包含等于1.0的情况
            count = await query.filter(
                Q(**{f"{metric}__gte": low}) & Q(**{f"{metric}__lte": high})
            ).count()
        else:
            count = await query.filter(
                Q(**{f"{metric}__gte": low}) & Q(**{f"{metric}__lt": high})
            ).count()

        percentage = (count / total_count) * 100 if total_count > 0 else 0
        result.append({
            "label": label,
            "value": round(percentage, 2)  # 保留两位小数
        })

    return response_success(data={
        "metric": metric,
        "stats": result
    }, total=total_count)


@router.get("/rag", summary="RAG评估报告总计")
async def rag_report(project_id: int):
    project = await ProjectModel.get_or_none(id=project_id)
    if not project:
        return response_fail(msg="项目不存在")

    results = []

    # 1. 获取项目基本信息
    project_data = {
        "project_name": project.name,
        "requirement_count": 0,
        "suite_count": 0,
        "test_case_count": 0,
        "metrics": {
            "context_precision": 0.0,
            "context_recall": 0.0,
            "context_entity_recall": 0.0,
            "faithfulness": 0.0,
            "answer_relevancy": 0.0
        }
    }
    # 2. 统计项目需求数量
    requirements = await RequirementModel.filter(project=project)
    requirement_count = len(requirements)
    project_data["requirement_count"] = requirement_count
    # 3. 统计测试套件数量
    suite_count = 0
    for requirement in requirements:
        suite_count += await SuiteModel.filter(requirement=requirement).count()
    project_data["suite_count"] = suite_count

    # 4. 统计测试用例数量
    test_case_count = await MaterialModel.filter(project=project).count()
    project_data["test_case_count"] = test_case_count

    # 4. 计算各项指标大于60%的百分比
    total_results = await RAGEvaluateResultModel.filter(project_id=project.id).count()

    if total_results > 0:
        metrics = ["context_precision", "context_recall",
                   "context_entity_recall", "faithfulness", "answer_relevancy"]

        for metric in metrics:
            passed_count = await RAGEvaluateResultModel.filter(
                project_id=project.id,
                **{f"{metric}__gt": 0.6}
            ).count()

            percentage = round((passed_count / total_results) * 100, 2)
            project_data["metrics"][metric] = percentage

    results.append(ProjectStatResponse(**project_data))

    return results


@router.get("/llm/statistics/", summary="llm统计指标")
async def get_llm_statistics(params: BaseQueryParams = Depends()):
    # 验证至少有一个查询条件
    if not any([params.version, params.suite_id, params.requirement_id]):
        return response_fail(msg="必须提供至少一个查询条件(version/suite_id/requirement_id)")

    base_query = await get_base_query(LLMEvaluateResultModel, params)
    ranges = {
        "0.6以下": (0, 0.6),
        "0.6-0.7": (0.6, 0.7),
        "0.7-0.8": (0.7, 0.8),
        "0.8-0.9": (0.8, 0.9),
        "0.9以上": (0.9, 1.0)
    }
    stats = {}
    for metric in ["agent_goal_accuracy", "precision", "recall", "f1"]:
        stats[metric] = {key: 0 for key in ranges.keys()}
    for metric, range_dict in stats.items():
        for label, (low, high) in ranges.items():
            query = base_query
            if low == 0:
                count = await query.filter(**{f"{metric}__lte": high}).count()
            elif high == 1.0:
                count = await query.filter(**{f"{metric}__gte": low}).count()
            else:
                count = await query.filter(
                    **{f"{metric}__gte": low, f"{metric}__lt": high}
                ).count()
            range_dict[label] = count
    return response_success(data=stats)


@router.get("/llm/stats/", summary="llm统计指标占有百分比")
async def get_llm_pie_stats(
        metric: Optional[str] = Query(description="指标名称"),
        version: Optional[str] = Query(None, description="版本号"),
        suite_id: Optional[int] = Query(None, description="测试套ID"),
        requirement_id: Optional[int] = Query(None, description="需求ID")
):
    # 验证指标有效性
    if metric not in ["agent_goal_accuracy", "precision", "recall", "f1"]:
        return response_fail(msg="传入指标错误")

    # 验证至少有一个查询条件
    if not any([version, suite_id, requirement_id]):
        return response_fail(msg="必须提供至少一个查询条件(version/suite_id/requirement_id)")
    # 构建基础查询
    base_query = LLMEvaluateResultModel.all()
    if version:
        base_query = base_query.filter(version=version)
    if suite_id:
        base_query = base_query.filter(suite_id=suite_id)
    if requirement_id:
        base_query = base_query.filter(test_case_id=requirement_id)

    # 获取总数
    total_count = await base_query.count()
    if total_count == 0:
        return response_success(data=[])
    # 定义区间
    intervals = [
        (0.0, 0.6),
        (0.6, 0.7),
        (0.7, 0.8),
        (0.8, 0.9),
        (0.9, 1.0)
    ]
    labels = ["<0.6", "0.6-0.7", "0.7-0.8", "0.8-0.9", ">=0.9"]

    result = []
    for (low, high), label in zip(intervals, labels):
        query = base_query
        if high == 1.0:
            # 处理最后一个区间，包含等于1.0的情况
            count = await query.filter(
                Q(**{f"{metric}__gte": low}) & Q(**{f"{metric}__lte": high})
            ).count()
        else:
            count = await query.filter(
                Q(**{f"{metric}__gte": low}) & Q(**{f"{metric}__lt": high})
            ).count()

        percentage = (count / total_count) * 100 if total_count > 0 else 0
        result.append({
            "label": label,
            "value": round(percentage, 2)  # 保留两位小数
        })

    return response_success(data={
        "metric": metric,
        "stats": result
    }, total=total_count)


@router.get("/llm", summary="LLM评估报告总计")
async def llm_report(project_id: int):
    project = await ProjectModel.get_or_none(id=project_id)
    if not project:
        return response_fail(msg="项目不存在")

    results = []

    # 1. 获取项目基本信息
    project_data = {
        "project_name": project.name,
        "requirement_count": 0,
        "suite_count": 0,
        "test_case_count": 0,
        "metrics": {
            "agent_goal_accuracy": 0.0,
            "precision": 0.0,
            "recall": 0.0,
            "f1": 0.0
        }
    }
    # 2. 统计项目需求数量
    requirements = await RequirementModel.filter(project=project)
    requirement_count = len(requirements)
    project_data["requirement_count"] = requirement_count
    # 3. 统计测试套件数量
    suite_count = 0
    for requirement in requirements:
        suite_count += await SuiteModel.filter(requirement=requirement).count()
    project_data["suite_count"] = suite_count

    # 4. 统计测试用例数量
    test_case_count = await MaterialModel.filter(project=project).count()
    project_data["test_case_count"] = test_case_count

    # 4. 计算各项指标大于60%的百分比
    total_results = await LLMEvaluateResultModel.filter(project_id=project.id).count()

    if total_results > 0:
        metrics = ["agent_goal_accuracy", "precision", "recall", "f1"]

        for metric in metrics:
            passed_count = await LLMEvaluateResultModel.filter(
                project_id=project.id,
                **{f"{metric}__gt": 0.6}
            ).count()

            percentage = round((passed_count / total_results) * 100, 2)
            project_data["metrics"][metric] = percentage

    results.append(ProjectStatResponse(**project_data))

    return results
