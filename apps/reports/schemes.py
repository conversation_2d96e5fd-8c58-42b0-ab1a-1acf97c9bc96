# @Time: 2025/4/17 17:34
# @Author: lvjing
# 定义响应模型
from typing import List, Optional

from fastapi import Query
from pydantic import BaseModel, Field


class RangeStat(BaseModel):
    label: str
    value: float


class MetricStat(BaseModel):
    metric: str
    stats: List[RangeStat]


class BaseQueryParams:
    def __init__(
            self,
            project_id: Optional[str] = Query(None, description="项目id"),
            requirement_id: Optional[int] = Query(None, description="需求ID"),
            version: Optional[str] = Query(None, description="版本号"),
            suite_id: Optional[int] = Query(None, description="测试套ID"),
    ):
        self.project_id = project_id
        self.requirement_id = requirement_id
        self.version = version
        self.suite_id = suite_id


class ProjectStatResponse(BaseModel):
    """
    报告统计响应模型
    """
    project_name: str = Field(description="项目名称")
    requirement_count: int = Field(description="需求数量")
    suite_count: int = Field(description="测试套数量")
    test_case_count: int = Field(description="测试用例数量")
    metrics: dict = Field(description="指标统计")


# ==================== 常量定义 ====================
# 报告类型常量
class ReportType:
    PROJECT_STATS = "PROJECT_STATS"  # 项目统计报告
    RAG_EVALUATION = "RAG_EVALUATION"  # RAG评估报告
    LLM_EVALUATION = "LLM_EVALUATION"  # LLM评估报告
    COMPREHENSIVE = "COMPREHENSIVE"  # 综合报告


# 统计维度常量
class StatsDimension:
    PROJECT = "project"  # 项目维度
    REQUIREMENT = "requirement"  # 需求维度
    SUITE = "suite"  # 测试套件维度
    MATERIAL = "material"  # 素材维度


# RAG评估指标
RAG_METRICS = [
    "context_precision",  # 上下文精确度
    "context_recall",  # 上下文召回率
    "context_entity_recall",  # 上下文实体召回率
    "faithfulness",  # 忠实度
    "answer_relevancy"  # 答案相关性
]

# LLM评估指标
LLM_METRICS = [
    "agent_goal_accuracy",  # 智能体目标准确性
    "precision",  # 精确率
    "recall",  # 召回率
    "f1"  # F1分数
]
