from typing import Optional
from pydantic import BaseModel


class RequirementBase(BaseModel):
    name: str
    version: str
    prd_link: str
    author: str
    description: Optional[str] = None


class RequirementAdd(RequirementBase):
    project: int


class RequirementUpdate(RequirementBase):
    id: int


requirement_resp_key = {
    "id",
    "name",
    "version",
    "creator_id",
    "creator__username",
    "project_id",
    "project__name",
    "author",
    "prd_link",
    "description",
    "create_time",
    "update_time",
}
