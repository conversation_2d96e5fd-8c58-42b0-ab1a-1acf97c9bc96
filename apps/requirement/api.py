import math
from fastapi import APIRouter, Request
from tortoise.exceptions import DoesNotExist

from apps.project.models import ProjectModel
from apps.project_member.models import ProjectMemberModel
from apps.requirement.models import RequirementModel
from apps.requirement.schemes import requirement_resp_key, RequirementAdd, RequirementUpdate
from common.utils import response_success, is_superuser, response_fail, response_success_paged, is_project_member, \
    get_caller_id, model_data_exist

requirement_router = APIRouter(prefix="/requirement")


@requirement_router.get("/{project_id}", summary='获取项目下全部需求信息', tags=['需求'])
async def get_requirement_all(project_id: int, request: Request, page: int = 1, page_size: int = 10):
    """
    查询项目下的所有需求
    仅 项目成员 和 超级管理员
    :param project_id:
    :param request:
    :param page:
    :param page_size:
    :return:
    """
    caller_id = get_caller_id(request)
    if await is_project_member(project_id, caller_id) or await is_superuser(request):
        all_requirement = await (RequirementModel.filter(project_id=project_id)
                                 .limit(page_size)
                                 .offset((page - 1) * page_size)
                                 .values(*requirement_resp_key))

        count = await RequirementModel.filter(project_id=project_id).count()

        return response_success_paged(
            data=all_requirement,
            total=count,
            current_page=page,
            total_page=math.ceil(count / page_size)
        )
    else:
        return response_fail(msg="无操作权限")


@requirement_router.get("/", summary='获取用户可见的全部需求信息', tags=['需求'])
async def get_requirement_all_user(request: Request, page: int = 1, page_size: int = 10):
    """
    获取用户可见的全部需求信息
    :param request:
    :param page:
    :param page_size:
    :return:
    """
    caller_id = get_caller_id(request)

    # 获取用户可用的project
    if await is_superuser(request):
        project_list = await ProjectModel.all().values_list("id", flat=True)
    else:
        project_ids = await ProjectMemberModel.filter(user_id=caller_id).values_list('project_id', flat=True)
        project_list = await (ProjectModel.filter(id__in=project_ids)).values_list("id", flat=True)

    all_requirement = await (RequirementModel.filter(project_id__in=project_list)
                             .limit(page_size)
                             .offset((page - 1) * page_size)
                             .values(*requirement_resp_key))
    count = await RequirementModel.filter(project_id__in=project_list).count()

    return response_success_paged(
        data=all_requirement,
        total=count,
        current_page=page,
        total_page=math.ceil(count / page_size)
    )


@requirement_router.get("/one", summary="获取单个需求信息", tags=['需求'])
async def get_requirement_one(id: int, request: Request):
    """
    查询项目下的单个需求
    仅 项目成员 和 超级管理员
    :param id:
    :param request:
    :return:
    """
    if await model_data_exist(id, RequirementModel):
        requirement = await RequirementModel.get(id=id)
        if await is_project_member(requirement.project_id, get_caller_id(request)) or await is_superuser(request):
            requirement = await RequirementModel.filter(id=id).values(*requirement_resp_key)
            return response_success(data=requirement, total=len(requirement))
        else:
            return response_fail("无操作权限")
    else:
        return response_fail(msg="[{}]对应数据不存在".format(id))


@requirement_router.post("", summary="新增需求", tags=['需求'])
async def add_requirement(req: RequirementAdd, request: Request):
    """
    添加需求
    仅项目成员和超级管理员
    :param req:
    :param request:
    :return:
    """

    caller_id = get_caller_id(request)
    if await is_project_member(req.project, caller_id) or await is_superuser(request):
        new_requirement = await RequirementModel.create(
            name=req.name,
            version=req.version,
            creator_id=caller_id,
            project_id=req.project,
            prd_link=req.prd_link,
            author=req.author,
            description=req.description
        )
        return response_success(data={'id': new_requirement.id})
    else:
        return response_fail(msg="非项目成员，不可添加")


@requirement_router.put("", summary="更新需求", tags=['需求'])
async def update_requirement(req: RequirementUpdate, request: Request):
    """
    修改需求
    仅 需求创建人 和 超级管理员
    :param req:
    :param request:
    :return:
    """
    caller_id = get_caller_id(request)
    if await model_data_exist(req.id, RequirementModel):
        requirement = await RequirementModel.get(id=req.id)
        if (await is_superuser(request)
                or
                (await is_project_member(requirement.project_id, caller_id) and requirement.creator_id == caller_id)
        ):
            count = await RequirementModel.filter(id=req.id) \
                .update(name=req.name,
                        version=req.version,
                        updater_id=caller_id,
                        # project_id=req.project,
                        prd_link=req.prd_link,
                        author=req.author,
                        description=req.description
                        )
            return response_success(total=count)
        else:
            return response_fail(msg="无操作权限")
    else:
        return response_fail(msg="[{}]对应需求不存在".format(req.id))


@requirement_router.delete("", summary="删除需求", tags=['需求'])
async def delete_requirement(req_id: int, request: Request):
    """
    删除需求
    仅 超级管理员
    :param req_id:
    :param request:
    :return:
    """
    if not await is_superuser(request):
        return response_fail(msg="无操作权限")

    try:
        old_requirement = await RequirementModel.get(id=req_id)
    except DoesNotExist:
        return response_fail(msg="需求不存在")

    await old_requirement.delete()
    return response_success()
