from tortoise import models, fields


class RequirementModel(models.Model):
    """
    需求表
    """
    id = fields.IntField(pk=True, auto_increment=True)
    name = fields.CharField(max_length=255, description="需求名")
    version = fields.CharField(max_length=255, description="需求版本")
    creator = fields.ForeignKeyField('models.UsersModel', related_name="requirement",
                                     on_delete=fields.SET_NULL, null=True, description="创建人")
    updater = fields.ForeignKeyField('models.UsersModel', related_name="requirement_update",
                                     on_delete=fields.SET_NULL, null=True, description="更新人")
    project = fields.ForeignKeyField('models.ProjectModel', related_name="requirement",
                                     on_delete=fields.SET_NULL, null=True, description="所属项目")
    prd_link = fields.Char<PERSON>ield(max_length=255, description="prd文档链接", null=True)
    author = fields.Char<PERSON><PERSON>(max_length=255, description="需求提出人", null=True)
    description = fields.Char<PERSON>ield(max_length=1024, description="项目描述", null=True)
    create_time = fields.data.DatetimeField(auto_now_add=True, description="创建时间")
    update_time = fields.data.DatetimeField(auto_now=True, description="更新时间")

    def __str__(self):
        return self.name

    class Meta:
        table = "requirement"
        table_description = "需求表"
        ordering = ['-id']
