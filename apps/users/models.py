# @Time：2025/3/19 16:26
# @Author：jinglv
from tortoise import models, fields


class UsersModel(models.Model):
    """
    用户模型
    """
    id = fields.IntField(pk=True, auto_increment=True, description="用户id")
    role = fields.IntField(description="角色: 0、普通用户 1、管理员")
    # source = fields.IntField(description="来源: 0、系统创建 1、SSO被动创建")
    staff_no = fields.CharField(max_length=32, description="工号", unique=True)
    # email = fields.CharField(max_length=32, description="邮箱")
    # phone_no = fields.CharField(max_length=16, description="手机号")
    username = fields.CharField(max_length=255, description="用户名")
    # password = fields.CharField(max_length=255, description="密码")
    created_time = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_time = fields.DatetimeField(auto_now=True, description="更新时间")

    def __str__(self):
        return self.username

    class Meta:
        table = "users"
        table_description = "用户表"
        ordering = ['-id']
