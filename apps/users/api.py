# @Time：2025/3/19 16:26
# @Author：jinglv
import logging
import math
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter
from fastapi import Request
from tortoise.exceptions import DoesNotExist
from tortoise.expressions import Q

from apps.users.models import UsersModel
from apps.users.schemas import UserUpdate, UserAdd, UserLogin
from common.utils import response_success, response_fail, response_success_paged, encode_jwt, verify_sso_user_ticket, \
    is_superuser, model_data_exist

logger = logging.getLogger(__name__)
router = APIRouter(prefix="")


@router.get("/manage/user", summary="获取全部用户", tags=['用户管理'])
async def get_user_all(request: Request, page: int = 1, page_size: int = 10, fliter: str = None):
    """
    获取全部用户
    仅限超管
    :param request
    :param page:当前页
    :param page_size:每页展示数量
    :param fliter: 模糊匹配
    :return: role: 0普通用户 1超级管理员
    """
    if not (await is_superuser(request)):
        return response_fail(msg="无操作权限")

    user_info = UsersModel.all()
    if fliter is not None:
        user_info = user_info.filter(Q(username__contains=fliter) | Q(staff_no__contains=fliter))

    paged_user_all = await (user_info
                            .limit(page_size)
                            .offset((page - 1) * page_size)
                            .values("username", "role", "staff_no", "id"))
    total_data_count = await user_info.count()

    return response_success_paged(
        data=paged_user_all,
        total=total_data_count,
        current_page=page,
        total_page=math.ceil(total_data_count / page_size)
    )


@router.get("/manage/user/{id}", summary="获取单个用户详情", tags=['用户管理'])
async def get_user_one(request: Request, id: int):
    """
    获取全部用户
    仅限超管
    :param request
    :param id
    """
    if not (await is_superuser(request)):
        return response_fail(msg="无操作权限")

    if await model_data_exist(id, UsersModel):
        user_info = await UsersModel.filter(id=id).values("username", "role", "staff_no", "id")
        return response_success(data=user_info)
    else:
        return response_fail(msg="用户【{}】不存在".format(id))


@router.post("/manage/user", summary="创建用户", tags=['用户管理'])
async def create_user(user: UserAdd, request: Request):
    """
    创建用户
    仅限超管
    :param user:
    :param request:
    :return:
    """
    # 验证权限
    if not (await is_superuser(request)):
        return response_fail(msg="无操作权限")

    # 验证工号是否重复
    if await UsersModel.exists(staff_no=user.staff_no):
        return response_fail(msg="对应工号[{}]已存在".format(user.staff_no))
    # 添加用户
    new_user = await UsersModel.create(username=user.username, role=user.role, staff_no=user.staff_no)
    return response_success(data={"id": new_user.id})


@router.put("/manage/user", summary="更新用户", tags=['用户管理'])
async def update_user(user: UserUpdate, request: Request):
    """
    更新用户
    仅限超管
    :param user:
    :param request:
    :return:
    """
    # 调用者必须是管理员
    if not await is_superuser(request):
        return response_fail(msg="无操作权限")

    # 验证工号是否重复
    if await UsersModel.exclude(id=user.id).filter(staff_no=user.staff_no).exists():
        return response_fail(msg="新工号[{}]已存在".format(user.staff_no))

    # 修改用户信息
    count = await UsersModel.filter(id=user.id).update(username=user.username, role=user.role, staff_no=user.staff_no)
    return response_success(total=count)


@router.delete("/manage/user", summary="删除用户", tags=['用户管理'])
async def delete_user(id: int, request: Request):
    """
    删除用户
    仅限超管
    :param id:
    :param request:
    :return:
    """
    if not (await is_superuser(request)):
        return response_fail(msg="无操作权限")
    count = await UsersModel.filter(id=id).delete()
    return response_success(total=count)


@router.post("/user/login", summary="登入", tags=['用户管理'])
async def login(user_info: UserLogin):
    """
    登陆接口
    使用sso登陆，前端使用用户名密码sso登陆成功后，拿到用户的ticket。传给后端换取token
    """
    ticket = user_info.ticket
    # 调用sso验证用户信息
    status, user_data = verify_sso_user_ticket('https://api-test.seres.cn/sso', ticket)
    if not status:
        return response_fail(msg='SSO用户验证失败:' + user_data)

    username = user_data['userName']
    staff_no = user_data['userOneId']
    try:
        user = await UsersModel.get(staff_no=staff_no)
    # 无账号 创建账号
    except DoesNotExist:
        user = await UsersModel.create(username=username, staff_no=staff_no, role=0)
    token = encode_jwt(payload={'id': user.id, 'username': user.username})

    return response_success(data={"token": token, "admin": user.role, 'id': user.id, 'username': user.username})


# @router.post("/admin/login", summary="后台登入", tags=['后台管理'])
# async def login(user_info: UserLogin):
#     """
#     登陆接口
#     使用sso登陆，前端使用用户名密码sso登陆成功后，拿到用户的ticket。传给后端换取token
#     """
#     ticket = user_info.ticket
#     # 调用sso验证用户信息
#     status, user_data = verify_sso_user_ticket('https://api-test.seres.cn/sso', ticket)
#     if not status:
#         return response_fail(msg='SSO用户验证失败:' + user_data)
#
#     staff_no = user_data['userOneId']
#
#     if not await UsersModel.filter(staff_no=staff_no, role=1).exists():
#         return response_fail(msg="账号非管理员")
#
#     user = await UsersModel.get(staff_no=staff_no, role=1)
#     return response_success(data=encode_jwt(payload={'id': user.id, 'username': user.username}))

@router.get("/user/generate_token", summary="生成JWT Token（开发环境）", tags=['用户管理'])
async def generate_token(user_id: int) -> Dict[str, Any]:
    """
    生成JWT Token接口（开发/测试环境专用）

    功能说明：
    - 为指定用户ID生成JWT认证token
    - 无需认证即可访问（已加入白名单）
    - 主要用于开发和测试环境的快速认证
    - 生产环境建议禁用此接口

    Args:
        user_id: 用户ID，必须是有效的用户标识

    Returns:
        Dict[str, Any]: 包含JWT token和用户信息的响应数据
        {
            "token": "jwt_token_string",
            "admin": 0/1,  # 用户角色：0-普通用户，1-管理员
            "id": user_id,
            "username": "用户名"
        }

    注意事项：
        - 此接口仅用于开发和测试环境
        - 生产环境必须禁用或移除此接口
        - 生成的token具有完整的用户权限

    安全警告：
        ⚠️ 此接口存在安全风险，生产环境请务必禁用！
    """
    logger.info(f"生成Token请求，user_id: {user_id}")

    # ========== 1. 参数验证 ==========
    if not user_id or user_id <= 0:
        logger.warning(f"无效的用户ID: {user_id}")
        return response_fail(
            msg="用户ID无效",
            data={"user_id": user_id, "suggestion": "请提供有效的正整数用户ID"}
        )

    try:
        # ========== 2. 查询用户信息 ==========
        user = await UsersModel.get(id=user_id)

        # ========== 3. 生成JWT Token ==========
        token_payload = {
            'id': user.id,
            'username': user.username
        }

        token = encode_jwt(payload=token_payload)

        # ========== 4. 构建响应数据 ==========
        response_data = {
            "token": token,
            "admin": user.role,  # 用户角色
            "id": user.id,  # 用户ID
            "username": user.username,  # 用户名
            "generated_at": datetime.now().isoformat(),  # 生成时间
            "environment": "development"  # 环境标识
        }

        logger.info(f"Token生成成功，user_id: {user_id}, username: {user.username}")

        return response_success(
            data=response_data,
            msg="Token生成成功"
        )

    except DoesNotExist:
        logger.warning(f"用户不存在，user_id: {user_id}")
        return response_fail(
            msg=f"用户不存在",
            data={
                "user_id": user_id,
                "suggestion": "请检查用户ID是否正确，或联系管理员创建用户"
            }
        )

    except Exception as e:
        logger.error(f"生成Token失败，user_id: {user_id}, 错误: {str(e)}", exc_info=True)
        return response_fail(
            msg="Token生成失败",
            data={
                "error_type": type(e).__name__,
                "suggestion": "请稍后重试或联系管理员"
            }
        )
