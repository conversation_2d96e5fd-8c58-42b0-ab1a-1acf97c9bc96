通过已经封装完成的httpx的服务，传入YapiBaseInfoModel模型的id查询数据模型YapiBaseInfoModel中的信息，获取到的yapi_base_url、yapi_token等信息，调用YAPI的接口获取信息，流程为获取项目信息  --> 获取接口列表 --> 获取接口详情：

1. 获取项目信息接口：https://yapiprd.seres.cn/api/project/get?token=40352cfd86cfdad6603dc3cabb1575d6826d56dc8175d7c5813fe9e96b9c070f

   接口返回：

   ```json
   {
       "errcode": 0,
       "errmsg": "成功！",
       "data": {
           "switch_notice": true,
           "is_mock_open": false,
           "strice": false,
           "is_json5": false,
           "_id": 145,
           "name": "seres-equity-service-new",
           "basepath": "/equity",
           "project_type": "private",
           "uid": 25,
           "group_id": 553,
           "icon": "code-o",
           "color": "yellow",
           "add_time": 1669885407,
           "up_time": 1755140424,
           "env": [
               {
                   "header": [],
                   "global": [],
                   "_id": "63886ddfeb78905447146a14",
                   "name": "local",
                   "domain": "http://127.0.0.1"
               }
           ],
           "tag": [],
           "cat": [],
           "role": false
       }
   }
   ```

   提取返回响应体中的项目id：data._id和项目名称：data.name

2. 获取接口列表接口：https://yapiprd.seres.cn/api/interface/list?token=40352cfd86cfdad6603dc3cabb1575d6826d56dc8175d7c5813fe9e96b9c070f&project_id=145&page=1&limit=10

   接口返回：

   ```json
   {
       "errcode": 0,
       "errmsg": "成功！",
       "data": {
           "count": 146,
           "total": 15,
           "list": [
               {
                   "edit_uid": 0,
                   "status": "done",
                   "api_opened": false,
                   "tag": [],
                   "_id": 12097,
                   "title": "权益包excel导出",
                   "path": "/equity/v1/excel/packExcelExport",
                   "catid": 2957,
                   "method": "POST",
                   "project_id": 145,
                   "uid": 29,
                   "add_time": **********
               },
               {
                   "edit_uid": 0,
                   "status": "done",
                   "api_opened": false,
                   "tag": [],
                   "_id": 12112,
                   "title": "权益明细excel导出",
                   "path": "/equity/v1/excel/detailExcelExport",
                   "catid": 2957,
                   "method": "POST",
                   "project_id": 145,
                   "uid": 29,
                   "add_time": **********
               },
               {
                   "edit_uid": 0,
                   "status": "done",
                   "api_opened": false,
                   "tag": [],
                   "_id": 12136,
                   "title": "权益账户导出",
                   "path": "/equity/v1/excel/accountExcelExport",
                   "catid": 2957,
                   "method": "POST",
                   "project_id": 145,
                   "uid": 29,
                   "add_time": **********
               },
               {
                   "edit_uid": 0,
                   "status": "undone",
                   "api_opened": false,
                   "tag": [],
                   "_id": 12259,
                   "method": "POST",
                   "catid": 2957,
                   "title": "下载中心列表",
                   "path": "/equity/v1/excel/queryExcelList",
                   "project_id": 145,
                   "uid": 29,
                   "add_time": **********
               },
               {
                   "edit_uid": 0,
                   "status": "done",
                   "api_opened": false,
                   "tag": [],
                   "_id": 14653,
                   "title": "权益包条件查询",
                   "path": "/equity/v1/packManage/queryEquityPack",
                   "catid": 3752,
                   "method": "POST",
                   "project_id": 145,
                   "uid": 25,
                   "add_time": 1676862080
               },
               {
                   "edit_uid": 0,
                   "status": "done",
                   "api_opened": false,
                   "tag": [],
                   "_id": 15013,
                   "title": "权益明细配件查询",
                   "path": "/equity/v1/detailManage/queryDetailPartList",
                   "catid": 3752,
                   "method": "POST",
                   "project_id": 145,
                   "uid": 29,
                   "add_time": 1678182209
               },
               {
                   "edit_uid": 0,
                   "status": "done",
                   "api_opened": false,
                   "tag": [],
                   "_id": 15754,
                   "title": "权益包创建",
                   "path": "/v1/packManage/draftEquityPack_1681372450126",
                   "catid": 4227,
                   "method": "POST",
                   "project_id": 145,
                   "uid": 480,
                   "add_time": 1681372450
               },
               {
                   "edit_uid": 0,
                   "status": "done",
                   "api_opened": false,
                   "tag": [],
                   "_id": 16381,
                   "method": "POST",
                   "title": "生成业务编码（废弃）",
                   "path": "/v1/businessCode/generateCode",
                   "catid": 4579,
                   "project_id": 145,
                   "uid": 480,
                   "add_time": 1683256245
               },
               {
                   "edit_uid": 0,
                   "status": "done",
                   "api_opened": false,
                   "tag": [],
                   "_id": 16384,
                   "title": "业务编码保存",
                   "path": "/v1/businessCode/saveBusinessCode",
                   "catid": 4579,
                   "method": "POST",
                   "project_id": 145,
                   "uid": 480,
                   "add_time": 1683256245
               },
               {
                   "edit_uid": 0,
                   "status": "done",
                   "api_opened": false,
                   "tag": [],
                   "_id": 16387,
                   "title": "业务编码更新",
                   "path": "/v1/businessCode/updateBusinessCode",
                   "catid": 4579,
                   "method": "POST",
                   "project_id": 145,
                   "uid": 480,
                   "add_time": 1683256245
               }
           ]
       }
   }
   ```

   提取返回响应体data.count和data.total进行10个分页请求接口，获取接口id（data.list._id）

3. 获取接口详情接口：https://yapiprd.seres.cn/api/interface/get?token=40352cfd86cfdad6603dc3cabb1575d6826d56dc8175d7c5813fe9e96b9c070f&id=12259 

   接口返回

   ```json
   {
       "errcode": 0,
       "errmsg": "成功！",
       "data": {
           "query_path": {
               "path": "/equity/v1/excel/queryExcelList",
               "params": []
           },
           "edit_uid": 0,
           "status": "undone",
           "type": "static",
           "req_body_is_json_schema": true,
           "res_body_is_json_schema": true,
           "api_opened": false,
           "index": 0,
           "tag": [],
           "_id": 12259,
           "method": "POST",
           "catid": 2957,
           "title": "下载中心列表",
           "path": "/equity/v1/excel/queryExcelList",
           "project_id": 145,
           "req_params": [],
           "res_body_type": "json",
           "uid": 29,
           "add_time": **********,
           "up_time": 1671502509,
           "req_query": [],
           "req_headers": [
               {
                   "required": "1",
                   "_id": "63a11aadeb78905447146e0c",
                   "name": "Content-Type",
                   "value": "application/json"
               }
           ],
           "req_body_form": [],
           "__v": 0,
           "desc": "",
           "markdown": "",
           "req_body_other": "{\"type\":\"object\",\"title\":\"empty object\",\"properties\":{\"pageNum\":{\"type\":\"number\"},\"pageSize\":{\"type\":\"number\"}},\"required\":[\"pageNum\",\"pageSize\"]}",
           "req_body_type": "json",
           "res_body": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"type\":\"object\",\"properties\":{\"msgCode\":{\"type\":\"string\"},\"msg\":{\"type\":\"string\"},\"resp\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"id\":{\"type\":\"number\"},\"vin\":{\"type\":\"null\"},\"url\":{\"type\":[\"string\",\"null\"]},\"createTime\":{\"type\":\"string\",\"description\":\"导出时间\"},\"updateTime\":{\"type\":\"string\"},\"status\":{\"type\":\"number\",\"description\":\"状态\"},\"statusName\":{\"type\":\"string\",\"description\":\"状态翻译\"},\"pageSize\":{\"type\":\"null\"},\"pageNum\":{\"type\":\"null\"},\"fileName\":{\"type\":\"string\",\"description\":\"文件名称\"}},\"required\":[\"id\",\"vin\",\"url\",\"createTime\",\"updateTime\",\"status\",\"pageSize\",\"pageNum\",\"fileName\",\"statusName\"]}},\"success\":{\"type\":\"boolean\"},\"total\":{\"type\":\"number\"},\"traceId\":{\"type\":\"string\"}}}",
           "username": "高于峰"
       }
   }
   ```

   提取返回响应体接口名称data.path，接口名称data.title，接口请求方式data.method，接口元数据api_meta_data存储整个data的信息

