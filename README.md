# AI Test Plat服务端

## 技术栈

- Python 3.12.8
- FastApi
- Tortoise

## 项目结构

```
project_root/

├── README.md
├── apps
│   └── users
│       ├── api.py			# api定义
│       ├── models.py		# 数据模型
│       └── schemas.py	# Pydantic 模型
|   └── projects
│       ├── api.py
│       ├── models.py
│       └── schemas.py
| ...
├── common							# 公共配置和方法
│   └── settings.py
├── main.py							# 应用启动文件
├── migrations					# 存放迁移记录
├── pyproject.toml			# 项目管理配置
├── requirements.txt		# 依赖库列表
├── tests								# 测试代码

```

## 数据库迁移命令

- 在项目根目录下运行以下命令

  ```
  aerich init -t main.TORTOISE_ORM
  ```

  其中 `app.TORTOISE_ORM` 是 Tortoise ORM 的配置路径。例如，如果 Tortoise 配置在 `main.py` 中，配置路径可以是
  `main.TORTOISE_ORM`。

  执行完成后，项目目录下会生成一个`migrations`目录(用于存放迁移记录)和`pyproject.toml`文件

- 生成迁移文件，初始化 数据库

  ```
  aerich init-db
  ```

- 每次修改模型后，运行以下命令生成迁移文件

  ```
  aerich migrate
  ```


- 应用迁移

  ```
  aerich upgrade
  ```

## 部署相关

nginx + supervisor + gunicorn + uvicorn

### 1、nginx配置

#### 1.1、配置文件位置

```shell
# 1、工程位置
automatrix/nginx-be.conf

# 2、服务器位置
/etc/nginx/conf.d/nginx-be.conf
```

#### 1.2、具体配置

```text
server {
    listen         8008;
    server_name    127.0.0.1
    charset UTF-8;
    access_log      /var/log/automatrix/be-nginx-access.log;
    error_log       /var/log/automatrix/be-nginx-error.log;
    client_max_body_size 75M;
    location / {
        proxy_pass http://127.0.0.1:8080/;
    }
}
```

#### 1.3、相关命令

```shell
# 状态
sudo systemctl status nginx
# 启动 
sudo systemctl start nginx
# 停止
sudo systemctl stop nginx
# 重启,配置文件更新后需要重启nginx
sudo systemctl restart nginx
```

### 2、supervisor配置

### 2.1 配置文件位置

```shell
# 1、工程位置
automatrix/supervisor.ini

# 2、服务器位置
/etc/supervisord.conf
```

#### 2.2、具体配置

```text
[program:automatrix]
command=/home/<USER>/ai-venv/bin/gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 127.0.0.1:8080
directory=/home/<USER>/automatrix
user=aiats
autostart=true
autorestart=true
stderr_logfile=/var/log/automatrix/supervisor-err.log
stdout_logfile=/var/log/automatrix/supervisor-out.log
```

#### 2.3、相关命令

```shell
# 启动 supervisor
sudo systemctl start supervisor

# 重新加载配置并启动你的服务 每次修改配置文件后，需要Supervisor重新加载配置
sudo supervisorctl reread
sudo supervisorctl update

# 启动服务
sudo supervisorctl start automatrix

# 检查服务状态
sudo supervisorctl status automatrix
```
